{"substations": [{"name": "NORTH_SUBSTATION", "description": "138kV transmission substation with multiple feeders", "devices": [{"device_id": "RELAY_LINE_138_1", "profile": "schweitzer_sel351", "description": "Line protection relay for 138kV transmission line 1", "config": {"nominal_voltage": 138000.0, "nominal_current": 1200.0, "sampling_rate": 960, "channels": 8, "update_rate": 4.0, "health_check_interval": 15.0}}, {"device_id": "RELAY_LINE_138_2", "profile": "abb_red670", "description": "Line differential protection relay for 138kV transmission line 2", "config": {"nominal_voltage": 138000.0, "nominal_current": 1500.0, "sampling_rate": 4000, "channels": 12, "update_rate": 10.0}}, {"device_id": "RELAY_TRANSFORMER_1", "profile": "ge_d60", "description": "Transformer protection relay for 138/13.8kV transformer", "config": {"nominal_voltage": 138000.0, "nominal_current": 2000.0, "sampling_rate": 1024, "channels": 16}}, {"device_id": "DFR_MAIN", "profile": "qualitrol_dfr", "description": "Main digital fault recorder for substation events", "config": {"nominal_voltage": 138000.0, "sampling_rate": 15360, "channels": 32, "update_rate": 0.1}}, {"device_id": "PMU_BUS_138", "profile": "arbiter_pmu", "description": "Phasor measurement unit on 138kV bus", "config": {"nominal_voltage": 138000.0, "update_rate": 30.0, "sampling_rate": 1920}}, {"device_id": "METER_FEEDER_1", "profile": "schneider_pm8000", "description": "Revenue meter for 13.8kV feeder 1", "config": {"nominal_voltage": 13800.0, "nominal_current": 600.0, "update_rate": 2.0}}, {"device_id": "METER_FEEDER_2", "profile": "cooper_form6", "description": "Power quality meter for 13.8kV feeder 2", "config": {"nominal_voltage": 13800.0, "nominal_current": 400.0, "update_rate": 1.0}}, {"device_id": "METER_FEEDER_3", "profile": "schneider_pm8000", "description": "Advanced meter for 13.8kV feeder 3", "config": {"nominal_voltage": 13800.0, "nominal_current": 800.0, "update_rate": 2.0}}]}, {"name": "SOUTH_SUBSTATION", "description": "69kV distribution substation", "devices": [{"device_id": "RELAY_LINE_69_1", "profile": "siemens_7sa522", "description": "Distance protection relay for 69kV line", "config": {"nominal_voltage": 69000.0, "nominal_current": 800.0, "sampling_rate": 800, "channels": 8}}, {"device_id": "RELAY_TRANSFORMER_2", "profile": "schweitzer_sel351", "description": "Transformer protection for 69/12.47kV transformer", "config": {"nominal_voltage": 69000.0, "nominal_current": 1000.0}}, {"device_id": "DFR_BACKUP", "profile": "qualitrol_dfr", "description": "Backup fault recorder", "config": {"nominal_voltage": 69000.0, "sampling_rate": 7680, "channels": 16}}, {"device_id": "PMU_BUS_69", "profile": "arbiter_pmu", "description": "PMU on 69kV bus", "config": {"nominal_voltage": 69000.0, "update_rate": 30.0}}, {"device_id": "METER_MAIN", "profile": "schneider_pm8000", "description": "Main substation revenue meter", "config": {"nominal_voltage": 69000.0, "nominal_current": 1000.0}}]}], "scenarios": [{"name": "transmission_fault_cascade", "description": "Transmission line fault with cascading effects", "event_type": "line_fault", "severity": 0.9, "duration_range": [0.1, 1.5], "affected_devices": ["relay_line_*", "dfr_*"], "probability": 0.2, "cascading_events": [{"type": "voltage_sag", "delay": 0.05, "duration": 2.0, "severity": 0.6, "devices": ["meter_*", "pmu_*"]}, {"type": "frequency_deviation", "delay": 0.2, "duration": 10.0, "severity": 0.4, "devices": ["pmu_*"]}]}, {"name": "transformer_thermal_trip", "description": "Transformer overload and thermal protection trip", "event_type": "transformer_trip", "severity": 0.8, "duration_range": [5.0, 30.0], "affected_devices": ["relay_transformer_*"], "probability": 0.05, "prerequisites": ["high_load_condition"], "cascading_events": [{"type": "voltage_sag", "delay": 0.1, "duration": 5.0, "severity": 0.7, "devices": ["meter_feeder_*"]}]}, {"name": "capacitor_switching_transient", "description": "Capacitor bank switching causing voltage transient", "event_type": "capacitor_switch", "severity": 0.3, "duration_range": [0.5, 3.0], "affected_devices": ["meter_*", "pmu_*"], "probability": 1.5, "cascading_events": [{"type": "harmonic_distortion", "delay": 0.1, "duration": 15.0, "severity": 0.4, "devices": ["meter_*"]}]}, {"name": "load_rejection_event", "description": "Large industrial load sudden disconnection", "event_type": "frequency_deviation", "severity": 0.6, "duration_range": [3.0, 45.0], "affected_devices": ["pmu_*", "meter_*"], "probability": 0.3}, {"name": "power_quality_disturbance", "description": "Non-linear load causing harmonic distortion", "event_type": "harmonic_distortion", "severity": 0.5, "duration_range": [300.0, 7200.0], "affected_devices": ["meter_*"], "probability": 0.4}, {"name": "system_oscillation", "description": "Inter-area power system oscillation", "event_type": "power_swing", "severity": 0.7, "duration_range": [10.0, 180.0], "affected_devices": ["relay_*", "pmu_*"], "probability": 0.08, "prerequisites": ["system_stressed"]}], "simulation_settings": {"auto_start_events": true, "file_generation": {"comtrade_enabled": true, "pqdif_enabled": true, "cleanup_hours": 24}, "performance_monitoring": {"update_interval": 30, "health_check_interval": 60, "metrics_retention_hours": 168}, "integration": {"triangle_microworks": {"enabled": true, "measurement_forwarding": true, "event_forwarding": true, "file_notifications": true}, "sttp_publishing": {"enabled": true, "server_url": "localhost:7165", "measurement_filtering": ["voltage_*", "current_*", "frequency", "power_*"]}}}}