#!/usr/bin/env python3
"""
Power System Simulation Demo
Demonstrates the comprehensive simulated device framework for CR-010

This example shows how to:
1. Create realistic power system devices (DFRs, meters, relays, PMUs)
2. Set up coordinated event scenarios
3. Generate IEEE-compliant files (COMTRADE, PQDIF)
4. Monitor performance and collect metrics
5. Integrate with external systems (Triangle MicroWorks, STTP)
"""

import asyncio
import logging
import json
from datetime import datetime, timedelta
from pathlib import Path
import sys

# Add the parent directory to the path to import simulated_devices
sys.path.append(str(Path(__file__).parent.parent))

from simulated_devices import (
    SimulationCoordinator, DeviceFactory, EventGenerator, 
    PowerSystemSimulator, DeviceConfig, DeviceType, EventType
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('simulation.log'),
        logging.StreamHandler()
    ]
)

class TriangleMicroWorksIntegration:
    """Mock Triangle MicroWorks integration for demonstration"""
    
    def __init__(self):
        self.logger = logging.getLogger("TriangleMicroWorks")
        self.measurements_received = 0
        self.events_received = 0
        
    async def handle_measurement(self, measurement: dict):
        """Handle measurement data from simulated devices"""
        self.measurements_received += 1
        
        # Log interesting measurements
        if measurement.get("device_type") == "phasor_measurement_unit":
            self.logger.info(f"PMU Data: {measurement['device_id']} - "
                           f"Freq: {measurement.get('frequency', 0):.3f} Hz")
        elif measurement.get("quality") != "GOOD":
            self.logger.warning(f"Poor quality measurement from {measurement['device_id']}")
            
    async def handle_event(self, event: dict):
        """Handle events from simulated devices"""
        self.events_received += 1
        self.logger.info(f"Event: {event.get('type', 'unknown')} on {event.get('device_id', 'unknown')}")
        
    async def handle_file_notification(self, file_info: dict):
        """Handle file generation notifications"""
        self.logger.info(f"New {file_info['type']} file: {file_info['filename']}")

class STTPPublisher:
    """Mock STTP publisher for demonstration"""
    
    def __init__(self):
        self.logger = logging.getLogger("STTPPublisher")
        self.published_measurements = 0
        
    async def publish_measurement(self, measurement: dict):
        """Publish measurement to STTP stream"""
        self.published_measurements += 1
        
        # Simulate STTP publishing
        if self.published_measurements % 100 == 0:
            self.logger.info(f"Published {self.published_measurements} measurements to STTP")

async def create_demonstration_substation():
    """Create a realistic substation for demonstration"""
    
    # Initialize the simulation coordinator
    coordinator = SimulationCoordinator()
    
    # Create Triangle MicroWorks and STTP integrations
    tmw_integration = TriangleMicroWorksIntegration()
    sttp_publisher = STTPPublisher()
    
    # Register handlers
    coordinator.add_measurement_handler(tmw_integration.handle_measurement)
    coordinator.add_measurement_handler(sttp_publisher.publish_measurement)
    coordinator.add_event_handler(tmw_integration.handle_event)
    coordinator.file_coordinator.add_file_handler(tmw_integration.handle_file_notification)
    
    # Create substation devices using device factory
    substation_config = {
        "name": "DEMO_SUBSTATION",
        "devices": [
            {
                "device_id": "RELAY_LINE_1",
                "profile": "schweitzer_sel351",
                "config": {"nominal_voltage": 138000.0, "nominal_current": 1200.0}
            },
            {
                "device_id": "RELAY_TRANSFORMER",
                "profile": "abb_red670",
                "config": {"nominal_voltage": 138000.0, "nominal_current": 2000.0}
            },
            {
                "device_id": "DFR_MAIN",
                "profile": "qualitrol_dfr",
                "config": {"sampling_rate": 15360, "channels": 16}
            },
            {
                "device_id": "METER_FEEDER_1",
                "profile": "schneider_pm8000",
                "config": {"nominal_voltage": 4160.0, "nominal_current": 600.0}
            },
            {
                "device_id": "METER_FEEDER_2", 
                "profile": "cooper_form6",
                "config": {"nominal_voltage": 4160.0, "nominal_current": 400.0}
            },
            {
                "device_id": "PMU_BUS_1",
                "profile": "arbiter_pmu",
                "config": {"nominal_voltage": 138000.0, "update_rate": 30.0}
            }
        ]
    }
    
    coordinator.create_substation_from_config(substation_config)
    
    return coordinator, tmw_integration, sttp_publisher

async def demonstrate_event_scenarios(coordinator: SimulationCoordinator):
    """Demonstrate various power system event scenarios"""
    
    logger = logging.getLogger("EventDemo")
    
    # Wait for devices to start up
    await asyncio.sleep(5.0)
    
    logger.info("=== Starting Event Scenario Demonstrations ===")
    
    # Scenario 1: Transmission line fault
    logger.info("Triggering transmission line fault...")
    await coordinator.trigger_scenario("transmission_line_fault", "DEMO_SUBSTATION")
    await asyncio.sleep(10.0)
    
    # Scenario 2: Capacitor bank switching
    logger.info("Triggering capacitor bank switching...")
    await coordinator.trigger_scenario("capacitor_bank_switching", "DEMO_SUBSTATION")
    await asyncio.sleep(15.0)
    
    # Scenario 3: Generator trip (system-wide event)
    logger.info("Triggering generator trip...")
    await coordinator.trigger_scenario("generator_trip")
    await asyncio.sleep(20.0)
    
    # Scenario 4: Harmonic disturbance
    logger.info("Triggering harmonic disturbance...")
    await coordinator.trigger_scenario("harmonic_disturbance", "DEMO_SUBSTATION")
    await asyncio.sleep(10.0)
    
    logger.info("=== Event Demonstrations Complete ===")

async def monitor_simulation_performance(coordinator: SimulationCoordinator, 
                                       tmw_integration: TriangleMicroWorksIntegration,
                                       sttp_publisher: STTPPublisher):
    """Monitor and report simulation performance"""
    
    logger = logging.getLogger("PerformanceMonitor")
    
    while coordinator.running:
        try:
            # Get simulation status
            status = coordinator.get_simulation_status()
            
            # Log performance metrics
            logger.info("=== Simulation Performance ===")
            logger.info(f"Uptime: {status['performance']['simulation_uptime']:.1f} seconds")
            logger.info(f"Devices Online: {status['devices']['online']}/{status['devices']['total']}")
            logger.info(f"Total Measurements: {status['performance']['total_measurements']}")
            logger.info(f"Total Events: {status['performance']['total_events']}")
            logger.info(f"Files Generated: {status['files']['generated']}")
            logger.info(f"TMW Measurements: {tmw_integration.measurements_received}")
            logger.info(f"TMW Events: {tmw_integration.events_received}")
            logger.info(f"STTP Published: {sttp_publisher.published_measurements}")
            
            # Device health summary
            device_list = coordinator.get_device_list()
            healthy_devices = sum(1 for d in device_list if d['health'] > 80.0)
            logger.info(f"Healthy Devices: {healthy_devices}/{len(device_list)}")
            
            # Log any unhealthy devices
            for device in device_list:
                if device['health'] < 80.0 or not device['communication_status']:
                    logger.warning(f"Device {device['device_id']}: Health={device['health']:.1f}%, "
                                 f"Comm={'OK' if device['communication_status'] else 'FAIL'}")
            
            await asyncio.sleep(60.0)  # Report every minute
            
        except Exception as e:
            logger.error(f"Performance monitoring error: {e}")
            await asyncio.sleep(60.0)

async def save_simulation_results(coordinator: SimulationCoordinator):
    """Save simulation results and configuration"""
    
    logger = logging.getLogger("ResultsSaver")
    
    try:
        # Create results directory
        results_dir = Path("simulation_results")
        results_dir.mkdir(exist_ok=True)
        
        # Save configuration
        config_file = results_dir / f"simulation_config_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        coordinator.save_configuration(str(config_file))
        
        # Save final status
        status = coordinator.get_simulation_status()
        status_file = results_dir / f"simulation_status_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(status_file, 'w') as f:
            json.dump(status, f, indent=2, default=str)
            
        # Save device information
        device_list = coordinator.get_device_list()
        devices_file = results_dir / f"device_list_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(devices_file, 'w') as f:
            json.dump(device_list, f, indent=2)
            
        logger.info(f"Saved simulation results to {results_dir}")
        
    except Exception as e:
        logger.error(f"Failed to save results: {e}")

async def main():
    """Main demonstration function"""
    
    logger = logging.getLogger("Main")
    logger.info("=== Power System Simulation Demo Starting ===")
    
    try:
        # Create demonstration substation
        coordinator, tmw_integration, sttp_publisher = await create_demonstration_substation()
        
        # Start performance monitoring
        monitor_task = asyncio.create_task(
            monitor_simulation_performance(coordinator, tmw_integration, sttp_publisher)
        )
        
        # Start event demonstrations
        event_task = asyncio.create_task(demonstrate_event_scenarios(coordinator))
        
        # Start the main simulation
        simulation_task = asyncio.create_task(coordinator.start_simulation())
        
        # Run for demonstration period (5 minutes)
        demo_duration = 300.0  # seconds
        logger.info(f"Running demonstration for {demo_duration/60:.1f} minutes...")
        
        # Wait for demo duration or simulation completion
        done, pending = await asyncio.wait(
            [simulation_task, monitor_task, event_task],
            timeout=demo_duration,
            return_when=asyncio.FIRST_COMPLETED
        )
        
        # Cancel remaining tasks
        for task in pending:
            task.cancel()
            
        # Stop simulation gracefully
        await coordinator.stop_simulation()
        
        # Save results
        await save_simulation_results(coordinator)
        
        # Final summary
        status = coordinator.get_simulation_status()
        logger.info("=== Demonstration Complete ===")
        logger.info(f"Total Runtime: {status['performance']['simulation_uptime']:.1f} seconds")
        logger.info(f"Measurements Generated: {status['performance']['total_measurements']}")
        logger.info(f"Events Triggered: {status['performance']['total_events']}")
        logger.info(f"Files Created: {status['files']['generated']}")
        logger.info(f"Triangle MicroWorks Integration: {tmw_integration.measurements_received} measurements, "
                   f"{tmw_integration.events_received} events")
        logger.info(f"STTP Publisher: {sttp_publisher.published_measurements} measurements published")
        
    except KeyboardInterrupt:
        logger.info("Demonstration interrupted by user")
    except Exception as e:
        logger.error(f"Demonstration error: {e}")
        raise
    finally:
        logger.info("=== Power System Simulation Demo Complete ===")

if __name__ == "__main__":
    # Run the demonstration
    asyncio.run(main())
