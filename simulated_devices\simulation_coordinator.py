"""
Simulation Coordinator - Orchestrates multi-device power system simulation
Coordinates devices, events, file generation, and performance monitoring
"""

import asyncio
import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from pathlib import Path
import numpy as np

from .power_system_simulator import PowerSystemSimulator, DeviceConfig, DeviceType
from .device_factory import DeviceFactory
from .event_generator import EventGenerator, EventScenario, EventSeverity
from .file_generators import FileCoordinator

class SimulationCoordinator:
    """
    Master coordinator for power system simulation
    Manages multiple devices, coordinated events, and system-wide testing scenarios
    """
    
    def __init__(self, config_file: Optional[str] = None):
        self.logger = logging.getLogger("SimulationCoordinator")
        
        # Core components
        self.device_factory = DeviceFactory()
        self.event_generator = EventGenerator()
        self.file_coordinator = FileCoordinator()
        
        # Simulation state
        self.devices: Dict[str, PowerSystemSimulator] = {}
        self.substations: Dict[str, List[str]] = {}  # substation -> device_ids
        self.running = False
        self.start_time: Optional[datetime] = None
        
        # Performance monitoring
        self.performance_metrics = {
            "total_measurements": 0,
            "total_events": 0,
            "total_files_generated": 0,
            "devices_online": 0,
            "simulation_uptime": 0.0,
            "last_update": datetime.utcnow()
        }
        
        # External handlers
        self.measurement_handlers: List[Callable] = []
        self.event_handlers: List[Callable] = []
        self.status_handlers: List[Callable] = []
        
        # Load configuration if provided
        if config_file:
            self.load_configuration(config_file)
            
    def load_configuration(self, config_file: str):
        """Load simulation configuration from JSON file"""
        try:
            with open(config_file, 'r') as f:
                config = json.load(f)
                
            # Load substations and devices
            for substation_config in config.get("substations", []):
                self.create_substation_from_config(substation_config)
                
            # Load custom scenarios
            for scenario_config in config.get("scenarios", []):
                self.add_custom_scenario_from_config(scenario_config)
                
            self.logger.info(f"Loaded configuration from {config_file}")
            
        except Exception as e:
            self.logger.error(f"Failed to load configuration: {e}")
            
    def create_substation_from_config(self, substation_config: Dict[str, Any]):
        """Create a substation with devices from configuration"""
        substation_name = substation_config["name"]
        device_configs = substation_config["devices"]
        
        devices = self.device_factory.create_substation(substation_name, device_configs)
        
        # Register devices
        device_ids = []
        for device in devices:
            self.register_device(device)
            device_ids.append(device.config.device_id)
            
        self.substations[substation_name] = device_ids
        
    def add_custom_scenario_from_config(self, scenario_config: Dict[str, Any]):
        """Add custom scenario from configuration"""
        scenario = EventScenario(
            name=scenario_config["name"],
            description=scenario_config.get("description", ""),
            event_type=scenario_config["event_type"],
            severity=EventSeverity(scenario_config.get("severity", 0.5)),
            duration_range=tuple(scenario_config.get("duration_range", [1.0, 10.0])),
            affected_devices=scenario_config.get("affected_devices", []),
            probability=scenario_config.get("probability", 0.1),
            prerequisites=scenario_config.get("prerequisites"),
            cascading_events=scenario_config.get("cascading_events")
        )
        
        self.event_generator.add_custom_scenario(scenario_config["name"], scenario)
        
    def register_device(self, device: PowerSystemSimulator):
        """Register a device with the coordinator"""
        device_id = device.config.device_id
        self.devices[device_id] = device
        
        # Register with event generator
        self.event_generator.register_device(device)
        
        # Add measurement handler
        device.add_measurement_handler(self._handle_device_measurement)
        device.add_event_handler(self._handle_device_event)
        
        self.logger.info(f"Registered device: {device_id}")
        
    async def _handle_device_measurement(self, measurement: Dict[str, Any]):
        """Handle measurement from a device"""
        self.performance_metrics["total_measurements"] += 1
        
        # Forward to external handlers
        for handler in self.measurement_handlers:
            try:
                await handler(measurement)
            except Exception as e:
                self.logger.error(f"Measurement handler error: {e}")
                
    async def _handle_device_event(self, event: Dict[str, Any]):
        """Handle event from a device"""
        self.performance_metrics["total_events"] += 1
        
        # Generate files if appropriate
        device_id = event.get("device_id")
        if device_id and device_id in self.devices:
            device = self.devices[device_id]
            
            # Get current measurements for file generation
            measurements = await self._get_device_measurements(device)
            
            # Generate waveform data for COMTRADE files
            waveform_data = None
            if device.config.device_type in [DeviceType.DFR, DeviceType.RELAY]:
                waveform_data = await self._generate_waveform_data(device, event)
                
            # Generate files
            files = await self.file_coordinator.generate_event_files(
                event, device.config.__dict__, measurements, waveform_data
            )
            
            self.performance_metrics["total_files_generated"] += len(files)
            
        # Forward to external handlers
        for handler in self.event_handlers:
            try:
                await handler(event)
            except Exception as e:
                self.logger.error(f"Event handler error: {e}")
                
    async def _get_device_measurements(self, device: PowerSystemSimulator) -> Dict[str, Any]:
        """Get current measurements from a device"""
        # This would typically get the latest measurements
        # For now, return the device's current system state
        return {
            "voltage_a": device.system_state.voltage_magnitude[0],
            "voltage_b": device.system_state.voltage_magnitude[1],
            "voltage_c": device.system_state.voltage_magnitude[2],
            "current_a": device.system_state.current_magnitude[0],
            "current_b": device.system_state.current_magnitude[1],
            "current_c": device.system_state.current_magnitude[2],
            "frequency": device.system_state.frequency,
            "active_power": device.system_state.active_power,
            "reactive_power": device.system_state.reactive_power
        }
        
    async def _generate_waveform_data(self, device: PowerSystemSimulator, 
                                    event: Dict[str, Any]) -> Dict[str, np.ndarray]:
        """Generate realistic waveform data for COMTRADE files"""
        sampling_rate = device.config.sampling_rate
        duration = 1.0  # 1 second of data
        samples = int(sampling_rate * duration)
        
        # Time array
        t = np.linspace(0, duration, samples)
        
        # Generate realistic fault waveforms
        waveforms = {}
        
        # Voltage waveforms (3-phase)
        for i, phase in enumerate(['A', 'B', 'C']):
            phase_angle = i * 120.0  # degrees
            amplitude = device.system_state.voltage_magnitude[i] * np.sqrt(2)
            
            # Apply fault effects
            if event.get("event_type") == "line_fault":
                if i == 0:  # Phase A fault
                    amplitude *= 0.1  # Severe voltage drop
                    
            waveform = amplitude * np.sin(2 * np.pi * 60 * t + np.radians(phase_angle))
            
            # Add noise and harmonics
            waveform += 0.02 * amplitude * np.random.normal(0, 1, samples)  # 2% noise
            waveform += 0.05 * amplitude * np.sin(2 * np.pi * 180 * t)  # 3rd harmonic
            
            waveforms[f"VA{phase}"] = waveform
            
        # Current waveforms (3-phase)
        for i, phase in enumerate(['A', 'B', 'C']):
            phase_angle = i * 120.0 - 30.0  # Current lags voltage by 30 degrees
            amplitude = device.system_state.current_magnitude[i] * np.sqrt(2)
            
            # Apply fault effects
            if event.get("event_type") == "line_fault":
                if i == 0:  # Phase A fault
                    amplitude *= 10.0  # High fault current
                    
            waveform = amplitude * np.sin(2 * np.pi * 60 * t + np.radians(phase_angle))
            
            # Add noise
            waveform += 0.02 * amplitude * np.random.normal(0, 1, samples)
            
            waveforms[f"IA{phase}"] = waveform
            
        # Digital status signals
        waveforms["TRIP"] = np.zeros(samples)
        if event.get("event_type") == "line_fault":
            # Trip signal after 3 cycles (50ms at 60Hz)
            trip_start = int(samples * 0.05)
            waveforms["TRIP"][trip_start:] = 1
            
        return waveforms
        
    async def start_simulation(self):
        """Start the complete power system simulation"""
        if self.running:
            self.logger.warning("Simulation already running")
            return
            
        self.running = True
        self.start_time = datetime.utcnow()
        
        self.logger.info(f"Starting simulation with {len(self.devices)} devices")
        
        # Start all devices
        device_tasks = []
        for device in self.devices.values():
            task = asyncio.create_task(device.start_simulation())
            device_tasks.append(task)
            
        # Start event generation
        event_task = asyncio.create_task(self.event_generator.start_event_generation())
        
        # Start performance monitoring
        monitor_task = asyncio.create_task(self._monitor_performance())
        
        # Start file cleanup
        cleanup_task = asyncio.create_task(self._periodic_file_cleanup())
        
        try:
            # Wait for all tasks
            all_tasks = device_tasks + [event_task, monitor_task, cleanup_task]
            await asyncio.gather(*all_tasks)
            
        except asyncio.CancelledError:
            self.logger.info("Simulation cancelled")
        except Exception as e:
            self.logger.error(f"Simulation error: {e}")
        finally:
            self.running = False
            
    async def stop_simulation(self):
        """Stop the power system simulation"""
        self.running = False
        
        # Stop all devices
        for device in self.devices.values():
            await device.stop_simulation()
            
        # Stop event generation
        await self.event_generator.stop_event_generation()
        
        self.logger.info("Simulation stopped")
        
    async def _monitor_performance(self):
        """Monitor simulation performance and update metrics"""
        while self.running:
            try:
                current_time = datetime.utcnow()
                
                # Update uptime
                if self.start_time:
                    self.performance_metrics["simulation_uptime"] = (
                        current_time - self.start_time
                    ).total_seconds()
                    
                # Count online devices
                online_devices = 0
                for device in self.devices.values():
                    if device.running:
                        online_devices += 1
                        
                self.performance_metrics["devices_online"] = online_devices
                self.performance_metrics["last_update"] = current_time
                
                # Collect device metrics
                device_metrics = {}
                for device_id, device in self.devices.items():
                    device_metrics[device_id] = device.get_metrics()
                    
                # Notify status handlers
                status_update = {
                    "timestamp": current_time.isoformat(),
                    "simulation_metrics": self.performance_metrics,
                    "device_metrics": device_metrics,
                    "event_statistics": self.event_generator.get_statistics()
                }
                
                for handler in self.status_handlers:
                    try:
                        await handler(status_update)
                    except Exception as e:
                        self.logger.error(f"Status handler error: {e}")
                        
                await asyncio.sleep(30.0)  # Update every 30 seconds
                
            except Exception as e:
                self.logger.error(f"Performance monitoring error: {e}")
                await asyncio.sleep(30.0)
                
    async def _periodic_file_cleanup(self):
        """Periodically clean up old generated files"""
        while self.running:
            try:
                await asyncio.sleep(3600.0)  # Every hour
                await self.file_coordinator.cleanup_old_files(max_age_hours=24)
            except Exception as e:
                self.logger.error(f"File cleanup error: {e}")
                
    def add_measurement_handler(self, handler: Callable):
        """Add a measurement data handler"""
        self.measurement_handlers.append(handler)
        
    def add_event_handler(self, handler: Callable):
        """Add an event handler"""
        self.event_handlers.append(handler)
        
    def add_status_handler(self, handler: Callable):
        """Add a status update handler"""
        self.status_handlers.append(handler)
        
    async def trigger_scenario(self, scenario_name: str, substation: Optional[str] = None):
        """Manually trigger a scenario on specific substation or all devices"""
        if substation and substation in self.substations:
            device_ids = self.substations[substation]
            await self.event_generator.trigger_manual_event(scenario_name, device_ids)
        else:
            await self.event_generator.trigger_manual_event(scenario_name)
            
    def get_simulation_status(self) -> Dict[str, Any]:
        """Get comprehensive simulation status"""
        return {
            "running": self.running,
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "devices": {
                "total": len(self.devices),
                "online": self.performance_metrics["devices_online"],
                "by_type": self._count_devices_by_type()
            },
            "substations": {name: len(devices) for name, devices in self.substations.items()},
            "performance": self.performance_metrics,
            "events": self.event_generator.get_statistics(),
            "files": {
                "generated": self.performance_metrics["total_files_generated"],
                "recent": len(self.file_coordinator.get_generated_files(limit=100))
            }
        }
        
    def _count_devices_by_type(self) -> Dict[str, int]:
        """Count devices by type"""
        counts = {}
        for device in self.devices.values():
            device_type = device.config.device_type.value
            counts[device_type] = counts.get(device_type, 0) + 1
        return counts
        
    def get_device_list(self) -> List[Dict[str, Any]]:
        """Get list of all devices with their status"""
        device_list = []
        for device in self.devices.values():
            device_info = {
                "device_id": device.config.device_id,
                "device_type": device.config.device_type.value,
                "station_name": device.config.station_name,
                "manufacturer": device.config.manufacturer,
                "model": device.config.model,
                "running": device.running,
                "health": device.device_health,
                "communication_status": device.communication_status
            }
            device_list.append(device_info)
            
        return device_list
        
    def save_configuration(self, output_file: str):
        """Save current simulation configuration to file"""
        config = {
            "substations": [],
            "scenarios": []
        }
        
        # Save substation configurations
        for substation_name, device_ids in self.substations.items():
            substation_config = {
                "name": substation_name,
                "devices": []
            }
            
            for device_id in device_ids:
                if device_id in self.devices:
                    device = self.devices[device_id]
                    device_config = {
                        "device_id": device_id.replace(f"{substation_name}_", ""),
                        "profile": f"{device.config.manufacturer.lower()}_{device.config.model.lower()}",
                        "config": {
                            "nominal_voltage": device.config.nominal_voltage,
                            "nominal_current": device.config.nominal_current,
                            "sampling_rate": device.config.sampling_rate,
                            "channels": device.config.channels
                        }
                    }
                    substation_config["devices"].append(device_config)
                    
            config["substations"].append(substation_config)
            
        try:
            with open(output_file, 'w') as f:
                json.dump(config, f, indent=2)
            self.logger.info(f"Saved configuration to {output_file}")
        except Exception as e:
            self.logger.error(f"Failed to save configuration: {e}")
