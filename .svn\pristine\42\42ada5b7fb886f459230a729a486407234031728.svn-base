# Protocol Server Connection Guide

## **🖥️ Server Information**
- **Server IP Address**: `************`
- **Network**: Wi-Fi (solar.local.lan)
- **Status**: Active and ready for external connections

## **🔗 Protocol Server Endpoints**

### **DNP3 Servers (Outstation Mode)**
| Device   | Address              | Outstation ID | Master ID | Device Type | Voltage Level |
|----------|---------------------|---------------|-----------|-------------|---------------|
| RELAY_01 | `************:20001` | 1            | 100       | Protective Relay | 138 kV |
| METER_01 | `************:20011` | 2            | 100       | Power Meter | 138 kV |
| MULTI_01 | `************:20030` | 1            | 100       | Multi-Protocol Device | 69 kV |
| CONFIG_01| `************:20040` | 5            | 100       | Configurable Device | 13.8 kV |

### **Modbus TCP Servers (Slave Mode)**
| Device   | Address              | Slave ID | Device Type | Voltage Level |
|----------|---------------------|----------|-------------|---------------|
| METER_01 | `************:20010` | 1        | Power Meter | 138 kV |
| MULTI_01 | `************:20031` | 1        | Multi-Protocol Device | 69 kV |
| CONFIG_01| `************:20041` | 5        | Configurable Device | 13.8 kV |

### **IEC 61850 MMS Servers**
| Device   | Address              | IED Name        | Device Type | Voltage Level |
|----------|---------------------|-----------------|-------------|---------------|
| RELAY_01 | `************:20101` | RELAY_RELAY_01  | Protective Relay | 138 kV |
| PMU_01   | `************:20020` | PMU_PMU_01      | Phasor Measurement Unit | 138 kV |
| MULTI_01 | `************:20032` | IED_MULTI_01    | Multi-Protocol Device | 69 kV |

## **🚀 Quick Start**

### **1. Start Protocol Servers**
On the server machine (************):
```bash
cd C:\work-repos\openMIC
python examples\unified_protocol_server_demo.py
```

### **2. Test Connectivity**
From your client machine:
```bash
# Test DNP3 connection
telnet ************ 20001

# Test Modbus connection  
telnet ************ 20010

# Test IEC 61850 connection
telnet ************ 20101
```

### **3. Configure Your Protocol Client**

#### **DNP3 Master Settings:**
```
Host: ************
Port: 20001 (or other DNP3 ports)
Master Address: 100
Outstation Address: 1 (see table above)
```

#### **Modbus TCP Settings:**
```
Host: ************
Port: 20010 (or other Modbus ports)
Slave ID: 1 (see table above)
```

#### **IEC 61850 Settings:**
```
Host: ************
Port: 20101 (or other IEC 61850 ports)
IED Name: RELAY_RELAY_01 (see table above)
```

## **📊 Available Data Points**

### **DNP3 Data Points**

#### **Analog Inputs (Group 30, Variation 1)**
| Point | Address | Name           | Description                    | Units | Range        |
|-------|---------|----------------|--------------------------------|-------|--------------|
| AI0   | 0       | voltage_a      | Phase A Voltage                | V     | 120-145 kV   |
| AI1   | 1       | voltage_b      | Phase B Voltage                | V     | 120-145 kV   |
| AI2   | 2       | voltage_c      | Phase C Voltage                | V     | 120-145 kV   |
| AI3   | 3       | current_a      | Phase A Current                | A     | 0-1000 A     |
| AI4   | 4       | current_b      | Phase B Current                | A     | 0-1000 A     |
| AI5   | 5       | current_c      | Phase C Current                | A     | 0-1000 A     |
| AI6   | 6       | power_total    | Total Real Power               | MW    | 0-500 MW     |
| AI7   | 7       | power_reactive | Total Reactive Power           | MVAr  | -200-200 MVAr|
| AI8   | 8       | frequency      | System Frequency               | Hz    | 59.5-60.5 Hz |
| AI9   | 9       | power_factor   | Power Factor                   | -     | 0.8-1.0      |

#### **Binary Inputs (Group 1, Variation 2)**
| Point | Address | Name              | Description                    |
|-------|---------|-------------------|--------------------------------|
| BI0   | 0       | breaker_status    | Circuit Breaker Position       |
| BI1   | 1       | protection_trip   | Protection System Trip         |
| BI2   | 2       | alarm_active      | General Alarm Status           |
| BI3   | 3       | maintenance_mode  | Maintenance Mode Active        |
| BI4   | 4       | communication_ok  | Communication Health           |

#### **Counters (Group 20, Variation 1)**
| Point | Address | Name           | Description                    | Units |
|-------|---------|----------------|--------------------------------|-------|
| C0    | 0       | energy_total   | Total Energy Counter           | MWh   |
| C1    | 1       | energy_import  | Import Energy Counter          | MWh   |
| C2    | 2       | energy_export  | Export Energy Counter          | MWh   |
| C3    | 3       | event_count    | Event Counter                  | -     |
| C4    | 4       | operation_count| Operation Counter              | -     |

### **Modbus TCP Data Points**

#### **Holding Registers (Function Code 03 - Read, 06 - Write)**
| Register | Address | Name           | Description                    | Scale | Units |
|----------|---------|----------------|--------------------------------|-------|-------|
| 40001    | 0       | voltage_a      | Phase A Voltage                | x10   | V     |
| 40002    | 1       | voltage_b      | Phase B Voltage                | x10   | V     |
| 40003    | 2       | voltage_c      | Phase C Voltage                | x10   | V     |
| 40011    | 10      | current_a      | Phase A Current                | x10   | A     |
| 40012    | 11      | current_b      | Phase B Current                | x10   | A     |
| 40013    | 12      | current_c      | Phase C Current                | x10   | A     |
| 40021    | 20      | power_total    | Total Real Power               | x10   | MW    |
| 40022    | 21      | power_reactive | Total Reactive Power           | x10   | MVAr  |
| 40031    | 30      | frequency      | System Frequency               | x100  | Hz    |
| 40032    | 31      | power_factor   | Power Factor                   | x1000 | -     |

#### **Input Registers (Function Code 04 - Read Only)**
| Register | Address | Name           | Description                    | Scale | Units |
|----------|---------|----------------|--------------------------------|-------|-------|
| 30001    | 0       | voltage_rms_a  | Phase A RMS Voltage            | x10   | V     |
| 30002    | 1       | voltage_rms_b  | Phase B RMS Voltage            | x10   | V     |
| 30003    | 2       | voltage_rms_c  | Phase C RMS Voltage            | x10   | V     |
| 30011    | 10      | current_rms_a  | Phase A RMS Current            | x10   | A     |
| 30012    | 11      | current_rms_b  | Phase B RMS Current            | x10   | A     |
| 30013    | 12      | current_rms_c  | Phase C RMS Current            | x10   | A     |

#### **Coils (Function Code 01 - Read, 05 - Write)**
| Coil  | Address | Name              | Description                    |
|-------|---------|-------------------|--------------------------------|
| 00001 | 0       | breaker_control   | Circuit Breaker Control        |
| 00002 | 1       | reset_alarms      | Reset Alarm Command            |
| 00003 | 2       | maintenance_mode  | Maintenance Mode Control       |
| 00004 | 3       | test_mode         | Test Mode Control              |
| 00005 | 4       | enable_protection | Protection Enable Control      |

#### **Discrete Inputs (Function Code 02 - Read Only)**
| Input | Address | Name              | Description                    |
|-------|---------|-------------------|--------------------------------|
| 10001 | 0       | breaker_status    | Circuit Breaker Position       |
| 10002 | 1       | protection_trip   | Protection System Trip         |
| 10003 | 2       | alarm_active      | General Alarm Status           |
| 10004 | 3       | maintenance_mode  | Maintenance Mode Active        |
| 10005 | 4       | communication_ok  | Communication Health           |

### **IEC 61850 Data Objects**

#### **MMXU1 (Measurement Unit)**
| Data Object | Attribute | Name           | Description                    | Type  |
|-------------|-----------|----------------|--------------------------------|-------|
| PPV.phsA    | mag.f     | voltage_a      | Phase A Voltage Magnitude      | FLOAT |
| PPV.phsB    | mag.f     | voltage_b      | Phase B Voltage Magnitude      | FLOAT |
| PPV.phsC    | mag.f     | voltage_c      | Phase C Voltage Magnitude      | FLOAT |
| A.phsA      | mag.f     | current_a      | Phase A Current Magnitude      | FLOAT |
| A.phsB      | mag.f     | current_b      | Phase B Current Magnitude      | FLOAT |
| A.phsC      | mag.f     | current_c      | Phase C Current Magnitude      | FLOAT |
| TotW        | mag.f     | power_total    | Total Real Power               | FLOAT |
| TotVAr      | mag.f     | power_reactive | Total Reactive Power           | FLOAT |
| Hz          | mag.f     | frequency      | System Frequency               | FLOAT |

#### **XCBR1 (Circuit Breaker)**
| Data Object | Attribute | Name           | Description                    | Type    |
|-------------|-----------|----------------|--------------------------------|---------|
| Pos         | stVal     | breaker_status | Circuit Breaker Position       | BOOLEAN |
| BlkOpn      | stVal     | block_open     | Block Open Command             | BOOLEAN |
| BlkCls      | stVal     | block_close    | Block Close Command            | BOOLEAN |

#### **PTRC1 (Protection)**
| Data Object | Attribute | Name           | Description                    | Type    |
|-------------|-----------|----------------|--------------------------------|---------|
| Op          | general   | protection_trip| Protection Operation           | BOOLEAN |
| Str         | general   | protection_start| Protection Start              | BOOLEAN |

#### **LLN0 (Logical Node Zero)**
| Data Object | Attribute | Name           | Description                    | Type |
|-------------|-----------|----------------|--------------------------------|------|
| Health      | stVal     | alarm_active   | Device Health Status           | INT  |
| Beh         | stVal     | behavior       | Device Behavior                | INT  |
| Mod         | stVal     | mode           | Device Mode                    | INT  |

### **Data Updates:**
- **Update Rate**: 1 second
- **Simulation**: Realistic power system behavior
- **Events**: Fault conditions, load changes
- **Quality**: IEEE standards compliant (DNP3 quality flags, Modbus exception codes, IEC 61850 quality attributes)

## **🏭 Device-Specific Data Points**

### **RELAY_01 - Protective Relay (138 kV)**
**Protocols**: DNP3 (`************:20001`) + IEC 61850 (`************:20101`)

#### **Available Measurements:**
| Point Name     | DNP3 Address | IEC 61850 Path | Typical Value | Description |
|----------------|--------------|----------------|---------------|-------------|
| voltage_a      | AI0          | MMXU1.PPV.phsA | 138,000 V     | Phase A Voltage |
| voltage_b      | AI1          | MMXU1.PPV.phsB | 138,000 V     | Phase B Voltage |
| voltage_c      | AI2          | MMXU1.PPV.phsC | 138,000 V     | Phase C Voltage |
| current_a      | AI3          | MMXU1.A.phsA   | 150 A         | Phase A Current |
| current_b      | AI4          | MMXU1.A.phsB   | 150 A         | Phase B Current |
| current_c      | AI5          | MMXU1.A.phsC   | 150 A         | Phase C Current |
| power_total    | AI6          | MMXU1.TotW     | 36 MW         | Total Real Power |
| frequency      | AI8          | MMXU1.Hz       | 60.0 Hz       | System Frequency |

#### **Available Status Points:**
| Point Name        | DNP3 Address | IEC 61850 Path | Description |
|-------------------|--------------|----------------|-------------|
| breaker_status    | BI0          | XCBR1.Pos      | Circuit Breaker Position |
| protection_trip   | BI1          | PTRC1.Op       | Protection Trip Status |
| alarm_active      | BI2          | LLN0.Health    | General Alarm Status |

### **METER_01 - Power Meter (138 kV)**
**Protocols**: Modbus TCP (`************:20010`) + DNP3 (`************:20011`)

#### **Available Measurements:**
| Point Name     | Modbus Address | DNP3 Address | Typical Value | Description |
|----------------|----------------|--------------|---------------|-------------|
| voltage_a      | 40001 (0)      | AI0          | 138,000 V     | Phase A Voltage |
| voltage_b      | 40002 (1)      | AI1          | 138,000 V     | Phase B Voltage |
| voltage_c      | 40003 (2)      | AI2          | 138,000 V     | Phase C Voltage |
| current_a      | 40011 (10)     | AI3          | 100 A         | Phase A Current |
| current_b      | 40012 (11)     | AI4          | 100 A         | Phase B Current |
| current_c      | 40013 (12)     | AI5          | 100 A         | Phase C Current |
| power_total    | 40021 (20)     | AI6          | 24 MW         | Total Real Power |
| frequency      | 40031 (30)     | AI8          | 60.0 Hz       | System Frequency |

#### **Available Status Points:**
| Point Name        | Modbus Address | DNP3 Address | Description |
|-------------------|----------------|--------------|-------------|
| breaker_status    | 10001 (0)      | BI0          | Circuit Breaker Position |
| alarm_active      | 10003 (2)      | BI2          | General Alarm Status |
| maintenance_mode  | 10004 (3)      | BI3          | Maintenance Mode Status |

### **PMU_01 - Phasor Measurement Unit (138 kV)**
**Protocols**: IEC 61850 (`************:20020`)

#### **Available Measurements:**
| Point Name     | IEC 61850 Path | Typical Value | Description |
|----------------|----------------|---------------|-------------|
| voltage_a      | MMXU1.PPV.phsA | 138,000 V     | Phase A Voltage Phasor |
| voltage_b      | MMXU1.PPV.phsB | 138,000 V     | Phase B Voltage Phasor |
| voltage_c      | MMXU1.PPV.phsC | 138,000 V     | Phase C Voltage Phasor |
| current_a      | MMXU1.A.phsA   | 150 A         | Phase A Current Phasor |
| current_b      | MMXU1.A.phsB   | 150 A         | Phase B Current Phasor |
| current_c      | MMXU1.A.phsC   | 150 A         | Phase C Current Phasor |
| frequency      | MMXU1.Hz       | 60.000 Hz     | High-Precision Frequency |

**Note**: PMU provides high-precision, time-synchronized measurements with GPS timestamps.

### **MULTI_01 - Multi-Protocol Device (69 kV)**
**Protocols**: DNP3 (`************:20030`) + Modbus TCP (`************:20031`) + IEC 61850 (`************:20032`)

#### **Available Measurements:**
| Point Name     | DNP3 | Modbus | IEC 61850 Path | Typical Value | Description |
|----------------|------|--------|----------------|---------------|-------------|
| voltage_a      | AI0  | 40001  | MMXU1.PPV.phsA | 69,000 V      | Phase A Voltage |
| voltage_b      | AI1  | 40002  | MMXU1.PPV.phsB | 69,000 V      | Phase B Voltage |
| voltage_c      | AI2  | 40003  | MMXU1.PPV.phsC | 69,000 V      | Phase C Voltage |
| current_a      | AI3  | 40011  | MMXU1.A.phsA   | 200 A         | Phase A Current |
| current_b      | AI4  | 40012  | MMXU1.A.phsB   | 200 A         | Phase B Current |
| current_c      | AI5  | 40013  | MMXU1.A.phsC   | 200 A         | Phase C Current |
| power_total    | AI6  | 40021  | MMXU1.TotW     | 24 MW         | Total Real Power |
| frequency      | AI8  | 40031  | MMXU1.Hz       | 60.0 Hz       | System Frequency |

#### **Available Status Points:**
| Point Name        | DNP3 | Modbus | IEC 61850 Path | Description |
|-------------------|------|--------|----------------|-------------|
| breaker_status    | BI0  | 10001  | XCBR1.Pos      | Circuit Breaker Position |
| protection_trip   | BI1  | 10002  | PTRC1.Op       | Protection Trip Status |
| alarm_active      | BI2  | 10003  | LLN0.Health    | General Alarm Status |

### **CONFIG_01 - Configurable Device (13.8 kV)**
**Protocols**: DNP3 (`************:20040`) + Modbus TCP (`************:20041`)

#### **Available Measurements:**
| Point Name     | DNP3 Address | Modbus Address | Typical Value | Description |
|----------------|--------------|----------------|---------------|-------------|
| voltage_a      | AI0          | 40001 (0)      | 13,800 V      | Phase A Voltage |
| voltage_b      | AI1          | 40002 (1)      | 13,800 V      | Phase B Voltage |
| voltage_c      | AI2          | 40003 (2)      | 13,800 V      | Phase C Voltage |
| current_a      | AI3          | 40011 (10)     | 300 A         | Phase A Current |
| current_b      | AI4          | 40012 (11)     | 300 A         | Phase B Current |
| current_c      | AI5          | 40013 (12)     | 300 A         | Phase C Current |
| power_total    | AI6          | 40021 (20)     | 72 MW         | Total Real Power |
| frequency      | AI8          | 40031 (30)     | 60.0 Hz       | System Frequency |

#### **Available Status Points:**
| Point Name        | DNP3 Address | Modbus Address | Description |
|-------------------|--------------|----------------|-------------|
| breaker_status    | BI0          | 10001 (0)      | Circuit Breaker Position |
| protection_trip   | BI1          | 10002 (1)      | Protection Trip Status |
| alarm_active      | BI2          | 10003 (2)      | General Alarm Status |
| maintenance_mode  | BI3          | 10004 (3)      | Maintenance Mode Status |

## **🔥 Firewall Ports**
Ensure these ports are accessible from your client network:
```
DNP3:     20001, 20011, 20030, 20040
Modbus:   20010, 20031, 20041  
IEC61850: 20101, 20020, 20032
```

## **📋 Data Access Examples**

### **DNP3 Read Commands:**
```
Read Analog Inputs:  Group 30, Variation 1, Points 0-9
Read Binary Inputs:  Group 1, Variation 2, Points 0-4
Read Counters:       Group 20, Variation 1, Points 0-4
```

### **Modbus TCP Read Commands:**
```
Read Holding Registers:  FC 03, Start Address 0, Quantity 10 (Voltages/Currents)
Read Input Registers:    FC 04, Start Address 0, Quantity 20 (RMS Values)
Read Coils:             FC 01, Start Address 0, Quantity 5 (Control Points)
Read Discrete Inputs:   FC 02, Start Address 0, Quantity 5 (Status Points)
```

### **IEC 61850 MMS Requests:**
```
Read Variables:     CTRL/MMXU1$MX$PPV$phsA$mag$f (Phase A Voltage)
                   CTRL/MMXU1$MX$A$phsA$mag$f (Phase A Current)
                   CTRL/XCBR1$ST$Pos$stVal (Breaker Status)
```

## **⚖️ Scaling and Units**

### **DNP3 Scaling:**
- **Voltages**: Direct values in Volts (e.g., 138000.0 = 138 kV)
- **Currents**: Direct values in Amperes (e.g., 150.0 = 150 A)
- **Power**: Direct values in Watts/VAr (e.g., 50000000.0 = 50 MW)
- **Frequency**: Direct values in Hz (e.g., 60.0 = 60 Hz)

### **Modbus TCP Scaling:**
- **High Voltages (>10kV)**: No scaling (e.g., 138000 = 138,000 V = 138 kV)
- **Low Voltages (<10kV)**: Scaled by 10 (e.g., 1380 = 138.0 V)
- **Currents**: Scaled by 10 (e.g., 1500 = 150.0 A)
- **Power**: Scaled by 0.1 (e.g., 2400 = 24,000 kW = 24 MW)
- **Frequency**: Scaled by 100 (e.g., 6000 = 60.00 Hz)
- **Power Factor**: Scaled by 1000 (e.g., 950 = 0.950)

### **IEC 61850 Scaling:**
- **All values**: Direct engineering units (same as DNP3)
- **Quality**: Standard IEC 61850 quality attributes
- **Timestamps**: UTC timestamps with millisecond precision

## **🧪 Recommended Test Tools**

### **DNP3:**
- Triangle MicroWorks Test Harness
- DNP3 Simulator/Master tools
- SCADA systems with DNP3 support

### **Modbus TCP:**
- ModbusPoll
- QModMaster
- Industrial HMI/SCADA systems

### **IEC 61850:**
- IEDScout
- Triangle MicroWorks IEC 61850 tools
- Substation automation systems

## **📞 Connection Status**
When servers are running, you'll see:
```
✅ Protocol servers started successfully
✅ Listening on ************
✅ Ready for external client connections
```

## **🔍 Troubleshooting**
1. **Can't connect**: Check firewall and network connectivity
2. **No data**: Verify addressing matches table above  
3. **Timeouts**: Ensure servers are running and responsive
4. **Protocol errors**: Check client configuration against settings above

## **🎯 Quick Device Selection Guide**

| If you need... | Connect to... | Protocols Available |
|----------------|---------------|-------------------|
| **Protective Relay Testing** | RELAY_01 | DNP3 + IEC 61850 |
| **Power Meter Testing** | METER_01 | Modbus TCP + DNP3 |
| **PMU/Synchrophasor Testing** | PMU_01 | IEC 61850 only |
| **Multi-Protocol Testing** | MULTI_01 | DNP3 + Modbus + IEC 61850 |
| **Custom Configuration** | CONFIG_01 | DNP3 + Modbus TCP |

### **Voltage Level Summary:**
- **138 kV**: RELAY_01, METER_01, PMU_01 (transmission level)
- **69 kV**: MULTI_01 (sub-transmission level)
- **13.8 kV**: CONFIG_01 (distribution level)

### **Protocol Combinations:**
- **DNP3 Only**: All devices support DNP3
- **Modbus TCP Only**: METER_01, MULTI_01, CONFIG_01
- **IEC 61850 Only**: RELAY_01, PMU_01, MULTI_01
- **All Three Protocols**: MULTI_01 (best for comprehensive testing)

---
**Server Ready**: Connect your protocol clients to `************` using the endpoints above!
