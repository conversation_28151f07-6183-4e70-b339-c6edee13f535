"""
Wire-Level Protocol Coordinator
Coordinates multiple wire-level protocol simulators for comprehensive testing
- Manages DNP3, Modbus, and IEC 61850 servers
- Provides unified interface for protocol testing
- Handles protocol-specific data updates and events
- Integrates with power system simulation
"""

import asyncio
import logging
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional, Set
from enum import Enum
import random
import time

from .wire_protocol_simulators import (
    WireLevelDNP3Simulator,
    WireLevelModbusSimulator, 
    WireLevelIEC61850Simulator
)

class ProtocolType(Enum):
    DNP3 = "dnp3"
    MODBUS = "modbus"
    IEC61850 = "iec61850"

class WireProtocolCoordinator:
    """Coordinates multiple wire-level protocol simulators"""
    
    def __init__(self, device_id: str):
        self.logger = logging.getLogger(f"WireProtocolCoord_{device_id}")
        self.device_id = device_id
        
        # Protocol simulators
        self.dnp3_simulator: Optional[WireLevelDNP3Simulator] = None
        self.modbus_simulator: Optional[WireLevelModbusSimulator] = None
        self.iec61850_simulator: Optional[WireLevelIEC61850Simulator] = None
        
        # Protocol configuration
        self.enabled_protocols: Set[ProtocolType] = set()
        self.port_config: Dict[ProtocolType, int] = {
            ProtocolType.DNP3: 20000,
            ProtocolType.MODBUS: 502,
            ProtocolType.IEC61850: 102
        }
        
        # Data synchronization
        self.measurement_data: Dict[str, Any] = {}
        self.status_data: Dict[str, bool] = {}
        
        # Update tasks
        self.update_tasks: List[asyncio.Task] = []
        self.running = False
        
    def enable_protocol(self, protocol: ProtocolType, port: Optional[int] = None):
        """Enable a protocol simulator"""
        self.enabled_protocols.add(protocol)
        
        if port:
            self.port_config[protocol] = port
            
        # Create simulator instance
        if protocol == ProtocolType.DNP3 and not self.dnp3_simulator:
            self.dnp3_simulator = WireLevelDNP3Simulator(
                self.device_id, 
                outstation_address=1
            )
            
        elif protocol == ProtocolType.MODBUS and not self.modbus_simulator:
            self.modbus_simulator = WireLevelModbusSimulator(
                self.device_id,
                slave_address=1
            )
            
        elif protocol == ProtocolType.IEC61850 and not self.iec61850_simulator:
            self.iec61850_simulator = WireLevelIEC61850Simulator(
                self.device_id,
                ied_name=f"IED_{self.device_id}"
            )
            
        self.logger.info(f"Enabled protocol: {protocol.value} on port {self.port_config[protocol]}")
        
    def disable_protocol(self, protocol: ProtocolType):
        """Disable a protocol simulator"""
        if protocol in self.enabled_protocols:
            self.enabled_protocols.remove(protocol)
            self.logger.info(f"Disabled protocol: {protocol.value}")
            
    async def start_all_servers(self):
        """Start all enabled protocol servers"""
        self.running = True
        
        # Start DNP3 server
        if ProtocolType.DNP3 in self.enabled_protocols and self.dnp3_simulator:
            await self.dnp3_simulator.start_server(self.port_config[ProtocolType.DNP3])
            
        # Start Modbus server
        if ProtocolType.MODBUS in self.enabled_protocols and self.modbus_simulator:
            await self.modbus_simulator.start_server(self.port_config[ProtocolType.MODBUS])
            
        # Start IEC 61850 server
        if ProtocolType.IEC61850 in self.enabled_protocols and self.iec61850_simulator:
            await self.iec61850_simulator.start_server(self.port_config[ProtocolType.IEC61850])
            
        # Start data update tasks
        self.update_tasks = [
            asyncio.create_task(self._measurement_update_loop()),
            asyncio.create_task(self._status_update_loop()),
            asyncio.create_task(self._event_simulation_loop())
        ]
        
        self.logger.info("All protocol servers started")
        
    async def stop_all_servers(self):
        """Stop all protocol servers"""
        self.running = False
        
        # Cancel update tasks
        for task in self.update_tasks:
            task.cancel()
            
        try:
            await asyncio.gather(*self.update_tasks, return_exceptions=True)
        except Exception:
            pass
            
        self.update_tasks.clear()
        
        # Stop protocol servers
        if self.dnp3_simulator:
            await self.dnp3_simulator.stop_server()
            
        if self.modbus_simulator:
            await self.modbus_simulator.stop_server()
            
        if self.iec61850_simulator:
            await self.iec61850_simulator.stop_server()
            
        self.logger.info("All protocol servers stopped")
        
    def update_measurement(self, measurement_name: str, value: float, quality: int = 1):
        """Update measurement value across all protocols"""
        self.measurement_data[measurement_name] = {
            'value': value,
            'quality': quality,
            'timestamp': datetime.now(timezone.utc)
        }
        
        # Update DNP3 analog inputs
        if self.dnp3_simulator:
            # Map measurement names to DNP3 points
            point_mapping = {
                'voltage_a': 0,
                'voltage_b': 1, 
                'voltage_c': 2,
                'current_a': 3,
                'current_b': 4,
                'current_c': 5,
                'power_total': 6,
                'frequency': 7
            }
            
            if measurement_name in point_mapping:
                self.dnp3_simulator.update_analog_input(
                    point_mapping[measurement_name], 
                    value, 
                    quality
                )
                
        # Update Modbus registers
        if self.modbus_simulator:
            # Map measurements to Modbus registers
            register_mapping = {
                'voltage_a': 0,
                'voltage_b': 1,
                'voltage_c': 2,
                'current_a': 10,
                'current_b': 11,
                'current_c': 12,
                'power_total': 20,
                'frequency': 21
            }
            
            if measurement_name in register_mapping:
                # Scale value for 16-bit register
                scaled_value = int(value * 10) & 0xFFFF
                self.modbus_simulator.update_holding_register(
                    register_mapping[measurement_name],
                    scaled_value
                )
                
        # Update IEC 61850 data attributes
        if self.iec61850_simulator:
            # Map measurements to IEC 61850 data attributes
            if measurement_name == 'voltage_a':
                self.iec61850_simulator.update_data_attribute(
                    "CTRL", "MMXU1", "PPV", "phsA", value
                )
            elif measurement_name == 'voltage_b':
                self.iec61850_simulator.update_data_attribute(
                    "CTRL", "MMXU1", "PPV", "phsB", value
                )
            elif measurement_name == 'voltage_c':
                self.iec61850_simulator.update_data_attribute(
                    "CTRL", "MMXU1", "PPV", "phsC", value
                )
            elif measurement_name == 'current_a':
                self.iec61850_simulator.update_data_attribute(
                    "CTRL", "MMXU1", "A", "phsA", value
                )
            elif measurement_name == 'current_b':
                self.iec61850_simulator.update_data_attribute(
                    "CTRL", "MMXU1", "A", "phsB", value
                )
            elif measurement_name == 'current_c':
                self.iec61850_simulator.update_data_attribute(
                    "CTRL", "MMXU1", "A", "phsC", value
                )
            elif measurement_name == 'power_total':
                self.iec61850_simulator.update_data_attribute(
                    "CTRL", "MMXU1", "TotW", None, value
                )
            elif measurement_name == 'frequency':
                self.iec61850_simulator.update_data_attribute(
                    "CTRL", "MMXU1", "Hz", None, value
                )
                
    def update_status(self, status_name: str, value: bool, quality: int = 1):
        """Update status value across all protocols"""
        self.status_data[status_name] = {
            'value': value,
            'quality': quality,
            'timestamp': datetime.now(timezone.utc)
        }
        
        # Update DNP3 binary inputs
        if self.dnp3_simulator:
            status_mapping = {
                'breaker_status': 0,
                'protection_trip': 1,
                'alarm_active': 2,
                'maintenance_mode': 3
            }
            
            if status_name in status_mapping:
                self.dnp3_simulator.update_binary_input(
                    status_mapping[status_name],
                    value,
                    quality
                )
                
        # Update Modbus coils/discrete inputs
        if self.modbus_simulator:
            coil_mapping = {
                'breaker_status': 0,
                'protection_trip': 1,
                'alarm_active': 2,
                'maintenance_mode': 3
            }
            
            if status_name in coil_mapping:
                self.modbus_simulator.update_discrete_input(
                    coil_mapping[status_name],
                    value
                )
                
        # Update IEC 61850 status attributes
        if self.iec61850_simulator:
            if status_name == 'breaker_status':
                self.iec61850_simulator.update_data_attribute(
                    "CTRL", "XCBR1", "Pos", None, value
                )
            elif status_name == 'protection_trip':
                self.iec61850_simulator.update_data_attribute(
                    "CTRL", "PTRC1", "Op", None, value
                )
                
    async def _measurement_update_loop(self):
        """Continuously update measurement values with realistic variations"""
        base_values = {
            'voltage_a': 120000.0,
            'voltage_b': 120000.0,
            'voltage_c': 120000.0,
            'current_a': 100.0,
            'current_b': 100.0,
            'current_c': 100.0,
            'power_total': 1500.0,
            'frequency': 60.0
        }
        
        while self.running:
            try:
                # Add realistic variations
                for name, base_value in base_values.items():
                    if name == 'frequency':
                        # Frequency varies slightly
                        variation = random.uniform(-0.1, 0.1)
                    elif 'voltage' in name:
                        # Voltage varies ±5%
                        variation = random.uniform(-0.05, 0.05) * base_value
                    elif 'current' in name:
                        # Current varies ±10%
                        variation = random.uniform(-0.1, 0.1) * base_value
                    else:
                        # Power varies ±15%
                        variation = random.uniform(-0.15, 0.15) * base_value
                        
                    new_value = base_value + variation
                    self.update_measurement(name, new_value)
                    
                await asyncio.sleep(1.0)  # Update every second
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Measurement update error: {e}")
                await asyncio.sleep(1.0)
                
    async def _status_update_loop(self):
        """Periodically update status values"""
        while self.running:
            try:
                # Simulate occasional status changes
                if random.random() < 0.01:  # 1% chance per update
                    status_name = random.choice([
                        'breaker_status', 'protection_trip', 
                        'alarm_active', 'maintenance_mode'
                    ])
                    
                    # Toggle status (except protection trip which should be rare)
                    if status_name == 'protection_trip':
                        new_value = random.random() < 0.001  # Very rare
                    else:
                        current = self.status_data.get(status_name, {'value': False})
                        new_value = not current['value']
                        
                    self.update_status(status_name, new_value)
                    
                await asyncio.sleep(5.0)  # Check every 5 seconds
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Status update error: {e}")
                await asyncio.sleep(5.0)
                
    async def _event_simulation_loop(self):
        """Simulate power system events"""
        while self.running:
            try:
                # Simulate events occasionally
                if random.random() < 0.005:  # 0.5% chance per update
                    await self._simulate_power_system_event()
                    
                await asyncio.sleep(10.0)  # Check every 10 seconds
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Event simulation error: {e}")
                await asyncio.sleep(10.0)
                
    async def _simulate_power_system_event(self):
        """Simulate a power system event"""
        event_types = [
            'voltage_sag',
            'voltage_swell', 
            'frequency_deviation',
            'load_change',
            'capacitor_switching'
        ]
        
        event_type = random.choice(event_types)
        self.logger.info(f"Simulating power system event: {event_type}")
        
        if event_type == 'voltage_sag':
            # Simulate voltage sag for 2 seconds
            for phase in ['voltage_a', 'voltage_b', 'voltage_c']:
                sag_value = 120000.0 * 0.7  # 30% sag
                self.update_measurement(phase, sag_value, quality=0x02)  # Questionable quality
                
            await asyncio.sleep(2.0)
            
            # Restore normal voltage
            for phase in ['voltage_a', 'voltage_b', 'voltage_c']:
                normal_value = 120000.0 + random.uniform(-1000, 1000)
                self.update_measurement(phase, normal_value, quality=0x01)  # Good quality
                
        elif event_type == 'frequency_deviation':
            # Simulate frequency deviation
            deviation_freq = 60.0 + random.uniform(-0.5, 0.5)
            self.update_measurement('frequency', deviation_freq, quality=0x02)
            
            await asyncio.sleep(5.0)
            
            # Restore normal frequency
            normal_freq = 60.0 + random.uniform(-0.05, 0.05)
            self.update_measurement('frequency', normal_freq, quality=0x01)
            
        # Trigger GOOSE message for significant events
        if self.iec61850_simulator and event_type in ['voltage_sag', 'frequency_deviation']:
            await self.iec61850_simulator.publish_goose_message(
                "CTRL/LLN0$GO$gcb01",
                "CTRL/LLN0$dataset01", 
                [event_type, time.time()]
            )
            
    def get_protocol_statistics(self) -> Dict[str, Any]:
        """Get statistics for all enabled protocols"""
        stats = {
            'enabled_protocols': [p.value for p in self.enabled_protocols],
            'port_configuration': {p.value: port for p, port in self.port_config.items()},
            'measurement_count': len(self.measurement_data),
            'status_count': len(self.status_data),
            'running': self.running
        }
        
        # Add protocol-specific statistics
        if self.dnp3_simulator:
            stats['dnp3'] = {
                'connections': len(self.dnp3_simulator.connections),
                'analog_inputs': len(self.dnp3_simulator.analog_inputs),
                'binary_inputs': len(self.dnp3_simulator.binary_inputs),
                'unsolicited_enabled': self.dnp3_simulator.unsolicited_enabled
            }
            
        if self.modbus_simulator:
            stats['modbus'] = {
                'connections': len(self.modbus_simulator.connections),
                'holding_registers': len(self.modbus_simulator.holding_registers),
                'input_registers': len(self.modbus_simulator.input_registers),
                'coils': len(self.modbus_simulator.coils)
            }
            
        if self.iec61850_simulator:
            stats['iec61850'] = {
                'mms_connections': len(self.iec61850_simulator.mms_connections),
                'logical_devices': len(self.iec61850_simulator.logical_devices),
                'goose_enabled': self.iec61850_simulator.goose_enabled
            }
            
        return stats
