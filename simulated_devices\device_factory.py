"""
Device Factory - Creates configured power system device simulators
Supports multiple manufacturer profiles and device configurations
"""

import json
import logging
from typing import Dict, List, Any, Optional
from pathlib import Path
from .power_system_simulator import PowerSystemSimulator, DeviceConfig, DeviceType

class DeviceProfile:
    """Device profile containing manufacturer-specific configurations"""
    
    def __init__(self, profile_data: Dict[str, Any]):
        self.manufacturer = profile_data.get("manufacturer", "Generic")
        self.model = profile_data.get("model", "SIM-1000")
        self.firmware_version = profile_data.get("firmware_version", "1.0.0")
        self.device_type = DeviceType(profile_data.get("device_type", "power_meter"))
        self.default_config = profile_data.get("default_config", {})
        self.capabilities = profile_data.get("capabilities", [])
        self.communication_protocols = profile_data.get("communication_protocols", ["DNP3"])
        
class DeviceFactory:
    """
    Factory for creating power system device simulators with manufacturer profiles
    Supports configurable device types and realistic manufacturer characteristics
    """
    
    def __init__(self, profiles_path: Optional[str] = None):
        self.logger = logging.getLogger("DeviceFactory")
        self.profiles: Dict[str, DeviceProfile] = {}
        
        # Load device profiles
        if profiles_path:
            self._load_profiles_from_file(profiles_path)
        else:
            self._load_default_profiles()
            
        self.logger.info(f"Loaded {len(self.profiles)} device profiles")
        
    def _load_default_profiles(self):
        """Load default manufacturer device profiles"""
        default_profiles = {
            "schweitzer_sel351": {
                "manufacturer": "Schweitzer Engineering Laboratories",
                "model": "SEL-351",
                "firmware_version": "R104-V0-Z001001-D20200101",
                "device_type": "protective_relay",
                "default_config": {
                    "nominal_voltage": 120.0,
                    "nominal_current": 5.0,
                    "sampling_rate": 960,  # 16 samples per cycle
                    "channels": 6,
                    "update_rate": 4.0,
                    "health_check_interval": 15.0
                },
                "capabilities": [
                    "overcurrent_protection",
                    "distance_protection", 
                    "frequency_protection",
                    "voltage_protection",
                    "power_protection",
                    "synchrocheck",
                    "fault_location"
                ],
                "communication_protocols": ["DNP3", "Modbus", "IEC61850"]
            },
            "abb_red670": {
                "manufacturer": "ABB",
                "model": "RED670",
                "firmware_version": "2.2.1",
                "device_type": "protective_relay",
                "default_config": {
                    "nominal_voltage": 120.0,
                    "nominal_current": 1.0,
                    "sampling_rate": 4000,
                    "channels": 12,
                    "update_rate": 10.0,
                    "health_check_interval": 30.0
                },
                "capabilities": [
                    "line_differential_protection",
                    "distance_protection",
                    "overcurrent_protection",
                    "autoreclosing",
                    "power_swing_detection",
                    "fault_location",
                    "oscillography"
                ],
                "communication_protocols": ["IEC61850", "DNP3", "IEC60870-5-103"]
            },
            "ge_d60": {
                "manufacturer": "General Electric",
                "model": "D60",
                "firmware_version": "6.04",
                "device_type": "protective_relay",
                "default_config": {
                    "nominal_voltage": 120.0,
                    "nominal_current": 5.0,
                    "sampling_rate": 1024,
                    "channels": 8,
                    "update_rate": 2.0,
                    "health_check_interval": 60.0
                },
                "capabilities": [
                    "line_protection",
                    "transformer_protection",
                    "motor_protection",
                    "generator_protection",
                    "arc_flash_detection"
                ],
                "communication_protocols": ["DNP3", "Modbus", "IEC61850"]
            },
            "siemens_7sa522": {
                "manufacturer": "Siemens",
                "model": "7SA522",
                "firmware_version": "4.90",
                "device_type": "protective_relay",
                "default_config": {
                    "nominal_voltage": 120.0,
                    "nominal_current": 5.0,
                    "sampling_rate": 800,
                    "channels": 16,
                    "update_rate": 1.0,
                    "health_check_interval": 45.0
                },
                "capabilities": [
                    "distance_protection",
                    "overcurrent_protection",
                    "earth_fault_protection",
                    "power_swing_blocking",
                    "load_encroachment"
                ],
                "communication_protocols": ["IEC61850", "DNP3", "PROFIBUS"]
            },
            "cooper_form6": {
                "manufacturer": "Cooper Power Systems",
                "model": "Form 6",
                "firmware_version": "3.21",
                "device_type": "power_meter",
                "default_config": {
                    "nominal_voltage": 120.0,
                    "nominal_current": 5.0,
                    "sampling_rate": 256,
                    "channels": 4,
                    "update_rate": 1.0,
                    "health_check_interval": 300.0
                },
                "capabilities": [
                    "revenue_metering",
                    "power_quality_monitoring",
                    "demand_recording",
                    "load_profiling",
                    "harmonic_analysis"
                ],
                "communication_protocols": ["DNP3", "Modbus", "C12.22"]
            },
            "schneider_pm8000": {
                "manufacturer": "Schneider Electric",
                "model": "PowerLogic PM8000",
                "firmware_version": "2.0.4",
                "device_type": "power_meter",
                "default_config": {
                    "nominal_voltage": 480.0,
                    "nominal_current": 5.0,
                    "sampling_rate": 1024,
                    "channels": 8,
                    "update_rate": 2.0,
                    "health_check_interval": 120.0
                },
                "capabilities": [
                    "advanced_metering",
                    "power_quality_analysis",
                    "waveform_capture",
                    "event_logging",
                    "alarm_management"
                ],
                "communication_protocols": ["Modbus", "DNP3", "IEC61850", "BACnet"]
            },
            "qualitrol_dfr": {
                "manufacturer": "Qualitrol",
                "model": "DFR-1800",
                "firmware_version": "5.12",
                "device_type": "digital_fault_recorder",
                "default_config": {
                    "nominal_voltage": 120.0,
                    "nominal_current": 5.0,
                    "sampling_rate": 15360,  # 256 samples per cycle
                    "channels": 32,
                    "update_rate": 0.1,
                    "health_check_interval": 60.0
                },
                "capabilities": [
                    "high_speed_recording",
                    "fault_analysis",
                    "sequence_of_events",
                    "waveform_analysis",
                    "comtrade_export",
                    "pqdif_export"
                ],
                "communication_protocols": ["DNP3", "IEC61850", "FTP", "HTTP"]
            },
            "arbiter_pmu": {
                "manufacturer": "Arbiter Systems",
                "model": "1133A",
                "firmware_version": "2.4.1",
                "device_type": "phasor_measurement_unit",
                "default_config": {
                    "nominal_voltage": 120.0,
                    "nominal_current": 5.0,
                    "sampling_rate": 1920,  # 32 samples per cycle
                    "channels": 6,
                    "update_rate": 30.0,  # 30 fps
                    "health_check_interval": 30.0
                },
                "capabilities": [
                    "synchrophasor_measurement",
                    "frequency_measurement",
                    "rocof_measurement",
                    "ieee_c37118_compliance",
                    "gps_synchronization",
                    "pdc_communication"
                ],
                "communication_protocols": ["IEEE_C37.118", "DNP3", "IEC61850"]
            }
        }
        
        for profile_name, profile_data in default_profiles.items():
            self.profiles[profile_name] = DeviceProfile(profile_data)
            
    def _load_profiles_from_file(self, profiles_path: str):
        """Load device profiles from JSON file"""
        try:
            with open(profiles_path, 'r') as f:
                profiles_data = json.load(f)
                
            for profile_name, profile_data in profiles_data.items():
                self.profiles[profile_name] = DeviceProfile(profile_data)
                
        except Exception as e:
            self.logger.error(f"Failed to load profiles from {profiles_path}: {e}")
            self._load_default_profiles()
            
    def create_device(self, device_id: str, profile_name: str, 
                     station_name: str, custom_config: Optional[Dict[str, Any]] = None) -> PowerSystemSimulator:
        """Create a power system device simulator with specified profile"""
        
        if profile_name not in self.profiles:
            available_profiles = list(self.profiles.keys())
            raise ValueError(f"Profile '{profile_name}' not found. Available: {available_profiles}")
            
        profile = self.profiles[profile_name]
        
        # Create device configuration
        config = DeviceConfig(
            device_id=device_id,
            device_type=profile.device_type,
            station_name=station_name,
            manufacturer=profile.manufacturer,
            model=profile.model,
            firmware_version=profile.firmware_version,
            communication_protocols=profile.communication_protocols.copy()
        )
        
        # Apply profile defaults
        for key, value in profile.default_config.items():
            if hasattr(config, key):
                setattr(config, key, value)
                
        # Apply custom configuration overrides
        if custom_config:
            for key, value in custom_config.items():
                if hasattr(config, key):
                    setattr(config, key, value)
                    
        # Create and return simulator
        simulator = PowerSystemSimulator(config)
        
        self.logger.info(f"Created {profile.manufacturer} {profile.model} simulator: {device_id}")
        
        return simulator
        
    def create_substation(self, substation_name: str, device_configs: List[Dict[str, Any]]) -> List[PowerSystemSimulator]:
        """Create multiple devices for a complete substation simulation"""
        devices = []
        
        for device_config in device_configs:
            device_id = f"{substation_name}_{device_config['device_id']}"
            profile_name = device_config['profile']
            custom_config = device_config.get('config', {})
            
            try:
                device = self.create_device(device_id, profile_name, substation_name, custom_config)
                devices.append(device)
            except Exception as e:
                self.logger.error(f"Failed to create device {device_id}: {e}")
                
        self.logger.info(f"Created substation '{substation_name}' with {len(devices)} devices")
        return devices
        
    def get_available_profiles(self) -> Dict[str, Dict[str, Any]]:
        """Get information about available device profiles"""
        profile_info = {}
        
        for name, profile in self.profiles.items():
            profile_info[name] = {
                "manufacturer": profile.manufacturer,
                "model": profile.model,
                "device_type": profile.device_type.value,
                "capabilities": profile.capabilities,
                "protocols": profile.communication_protocols
            }
            
        return profile_info
        
    def save_profiles(self, output_path: str):
        """Save current profiles to JSON file"""
        profiles_data = {}
        
        for name, profile in self.profiles.items():
            profiles_data[name] = {
                "manufacturer": profile.manufacturer,
                "model": profile.model,
                "firmware_version": profile.firmware_version,
                "device_type": profile.device_type.value,
                "default_config": profile.default_config,
                "capabilities": profile.capabilities,
                "communication_protocols": profile.communication_protocols
            }
            
        try:
            with open(output_path, 'w') as f:
                json.dump(profiles_data, f, indent=2)
            self.logger.info(f"Saved profiles to {output_path}")
        except Exception as e:
            self.logger.error(f"Failed to save profiles: {e}")
