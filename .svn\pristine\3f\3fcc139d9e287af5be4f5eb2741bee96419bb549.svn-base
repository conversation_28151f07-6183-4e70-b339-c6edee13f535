---
swagger: "2.0"
info:
  description: "SDG Configuration API"
  version: "1.0.0"
  title: "SDG Config"
#host: "localhost:58090"
basePath: "/rest"
schemes:
  - http
  - https
securityDefinitions:
  Bearer:
    type: apiKey
    name: Authorization
    in: header
paths:
  /about:
    get:
      security:
      - Bearer: []
      tags:
      - "misc"
      summary: "Get About info"
      description: "Get About info"
      operationId: "getAbout"
      produces:
      - "application/json"
      parameters: []
      responses:
        200:
          description: "Get About info"
          schema:
            $ref: '#/definitions/SDGAboutDTO'
        400:
          description: "Bad Request"
  /mon_health:
    get:
      security:
      - Bearer: []
      tags:
      - "manage"
      summary: "Get health of Monitor."
      description: "Get health of Monitor. Used by redundancy to obtain health of SDG monitor"
      operationId: "monHealthGet"
      produces:
      - "application/json"
      parameters: []
      responses:
        200:
          description: "Health GET"
          schema:
            $ref: '#/definitions/MONHealthObjectDTO'
        400:
          description: "Bad Request"
  /get_sdg_status:
    get:
      security:
      - Bearer: []
      tags:
      - "config"
      summary: "Get SDG health"
      description: "Get SDG health"
      operationId: "getSdgStatus"
      produces:
      - "application/json"
      parameters: []
      responses:
        200:
          description: "Get sdg health status"
          schema:
            $ref: '#/definitions/HealthObjectDTO'
  /get_config:
    get:
      security:
      - Bearer: []
      tags:
      - "config"
      summary: "Get SDG configuration"
      description: ""
      operationId: "getConfig"
      produces:
      - "application/json"
      parameters: []
      responses:
        200:
          description: "Get sdg config"
          schema:
            $ref: '#/definitions/SDGConfigDTO'
        401:
          description: "Unauthorized"
        400:
          description: "Bad Request"
  /net_interfaces:
    get:
      security:
      - Bearer: []
      tags:
      - "config"
      summary: "Get network interfaces."
      description: ""
      operationId: "getNetInterfaces"
      produces:
      - "application/json"
      parameters:
      - name: "interfaceFilter"
        in: "query"
        description: "network interface filter (i.e. interface name must contain this, empty string returns all)"
        required: false
        type: "string"
      responses:
        200:
          description: "OK"
          schema:
            type: array
            items:
              $ref: '#/definitions/NetInterfaceObjectDTO'
        204:
          description: "No Content"
        400:
          description: "Bad Request"
  /set_config_json:
    post:
      security:
      - Bearer: []
      tags:
      - "config"
      summary: "Configure SDG"
      description: ""
      operationId: "setConfigJson"
      consumes:
      - application/json
      produces:
      - "application/json"
      parameters:
      - name: "body"
        in: "body"
        required: true
        schema:
           $ref:  '#/definitions/SDGConfigDTO'
      - name: "restartFlag"
        in: "query"
        description: "restart monitor and engine flag (true to resart)"
        required: false
        type: boolean
      - name: "validateFlag"
        in: "query"
        description: "validate config setting on server (true to validate)"
        required: false
        type: boolean
      responses:
        200:
          description: "Success"
          schema:
            $ref: '#/definitions/SDGConfigDTO'
        400:
          description: "Bad Request"
  /process_broadcast_event:
    post:
      security:
      - Bearer: []
      tags:
      - "config"
      summary: "Process a broadcast event"
      description: "messageLogMaskEnum:   NONE = 0, EVENT_LOG = 1,ALERT_POPUP = 2,STATUS_BAR = 4  messageTypeEnum: refresh_ui, refresh_dirty_flag, refresh_log_parameter, force_log_off_user, message_info, message_warning, message_error, message_debug, message_success"
      operationId: "processBroadcastEvent"
      consumes:
      - application/json
      parameters:
      - name: "body"
        in: "body"
        required: true
        schema:
           $ref:  '#/definitions/BroadcastEventDTO'
      responses:
        200:
          description: "Success"
        400:
          description: "Bad Request"
  /stopMon:
    post:
      security:
      - Bearer: []
      tags:
      - "config"
      summary: "Stop Monitor"
      description: "Stops the Monitor"
      operationId: "stopMon"
      consumes:
      - application/x-www-form-urlencoded
      produces:
      - "application/json"
      parameters:
      - name: "bothFlag"
        in: "query"
        description: "stop monitor and engine flag (true to stop both)"
        required: false
        type: boolean
      responses:
        200:
          description: "Success"
        400:
          description: "Bad Request"
  /newWorkSpace:
    post:
      security:
      - Bearer: []
      tags:
      - "config"
      summary: "Create new SDG work space configuration"
      description: "Create new work space and re-start SDG"
      operationId: "newWorkSpace"
      consumes:
      - application/x-www-form-urlencoded
      produces:
      - "application/json"
      parameters:
      - name: "workSpaceName"
        in: "formData"
        description: "name of the work space to create"
        required: false
        type: "string"
      responses:
        200:
          description: "Success"
        400:
          description: "Bad Request"
  /selectWorkSpace:
    post:
      security:
      - Bearer: []
      tags:
      - "config"
      summary: "Select SDG work space configuration to run it"
      description: "Select work space and re-start SDG to run it"
      operationId: "selectWorkSpace"
      consumes:
      - application/x-www-form-urlencoded
      produces:
      - "application/json"
      parameters:
      - name: "workSpaceName"
        in: "formData"
        description: "name of the work space to run"
        required: false
        type: "string"
      responses:
        200:
          description: "Success"
        400:
          description: "Bad Request"
  /startEngine:
    post:
      security:
      - Bearer: []
      tags:
      - "config"
      summary: "Start Engine"
      description: "Starts Engine with arguments, if no args are specified it will start the configured sdg engine with the configured engine ini file"
      operationId: "startEngine"
      consumes:
      - application/x-www-form-urlencoded
      produces:
      - "application/json"
      parameters:
      - name: "arg"
        in: "formData"
        description: "command line argument (argument to pass to engine executable, typically the ini file)"
        required: false
        type: "string"
      responses:
        200:
          description: "Success"
        400:
          description: "Bad Request"
  /stopEngine:
    post:
      security:
      - Bearer: []
      tags:
      - "config"
      summary: "Stop the Engine"
      description: "Stops the Engine, the O/S will try to re-start it"
      operationId: "stopEngine"
      consumes:
      - application/x-www-form-urlencoded
      produces:
      - "application/json"
      parameters:
      - name: "stayDown"
        in: "query"
        description: "Just stop it do not try to restart"
        required: false
        type: "boolean"
      responses:
        200:
          description: "Success"
        400:
          description: "Bad Request"
  /get_license:
    get:
      security:
      - Bearer: []
      tags:
      - "config"
      summary: "Get license information."
      description: "Get license information."
      operationId: "getLicense"
      produces:
      - "application/json"
      parameters: []
      consumes:
      - "multipart/form-data"
      responses:
        200:
          description: "OK"
        204:
          description: "Bad Request"
        401:
          description: "Unauthorized"
  /sendV2CLicense:
    post:
      security:
      - Bearer: []
      tags:
      - "config"
      summary: "Send V2C file license."
      description: "Send V2C file license."
      operationId: "sendV2CLicense"
      consumes:
      - "multipart/form-data"
      parameters:
      - name: "file"
        in: "formData"
        description: "file"
        required: true
        type: "file"
      responses:
        200:
          description: "Upload file"
        400:
          description: "Bad Request"
  /generate_license_transfer:
    post:
      security:
      - Bearer: []
      tags:
      - "config"
      summary: "Send license Recipient ID get h2h file."
      description: "Send license Recipient ID get h2h file."
      operationId: "generateLicenseTransfer"
      produces: 
      - "application/octet-stream"
      consumes:
      - "multipart/form-data"
      parameters:
      - name: "file"
        in: "formData"
        description: "file"
        required: true
        type: "file"
      responses:
        200:
          description: "Download file"
          schema:
            type: file
        400:
          description: "Bad Request"
  /get_recipient_id:
    get:
      security:
      - Bearer: []
      tags:
      - "config"
      summary: "Get Recipient ID."
      description: "Get Recipient ID."
      operationId: "getRecipientID"
      produces: 
      - "application/octet-stream"
      consumes:
      - "application/json"
      responses:
        200:
          description: "Download file"
          schema:
            type: file
        400:
          description: "Bad Request"
  /save_license:
    get:
      security:
      - Bearer: []
      tags:
      - "config"
      summary: "Save license information."
      description: "Save license information."
      operationId: "saveLicense"
      consumes:
      - "application/json"
      parameters:
      - name: "action_type"
        in: "query"
        description: "action type"
        required: true
        type: "string"
      - name: "product_key"
        in: "query"
        description: "product key"
        required: false
        type: "string"
      - name: "is_new_license"
        in: "query"
        description: "license is new"
        required: true
        type: "boolean"
      responses:
        200:
          description: "OK"
        204:
          description: "No Content"
        400:
          description: "Bad Request"
        501:
          description: "Not Implemented"
  /file:
    get:
      security:
      - Bearer: []
      tags:
      - "file"
      summary: "download a file of a specific type"
      description: ""
      operationId: "fileGet"
      produces: 
      - "application/octet-stream"
      consumes:
      - "application/json"
      parameters:
      - name: "fileName"
        in: "query"
        description: "file name/path"
        required: true
        type: "string"
      - name: "fileType"
        in: "query"
        description: "file type"
        required: true
        type: "string"
      responses:
        200:
          description: "Download file"
          schema:
            type: file
        400:
          description: "Bad Request"
    post:
      security:
      - Bearer: []
      tags:
      - "file"
      summary: "upload a file for a work space"
      description: ""
      operationId: "filePost"
      produces:
      - "application/json"
      consumes:
      - "multipart/form-data"
      parameters:
      - name: "file"
        in: "formData"
        description: "file"
        required: true
        type: "file"
      - name: "fileType"
        in: "query"
        description: "file type"
        required: false
        type: "string"
      - name: "workspaceName"
        in: "query"
        description: "workspace name if blank its the current work space"
        required: false
        type: "string"
      responses:
        200:
          description: "Upload file"
        400:
          description: "Bad Request"
    delete:
      security:
      - Bearer: []
      tags:
      - "file"
      summary: "delete a file of a specific type"
      description: ""
      operationId: "fileDelete"
      produces: 
      - "application/octet-stream"
      consumes:
      - "application/json"
      parameters:
      - name: "fileName"
        in: "query"
        description: "file name/path"
        required: true
        type: "string"
      - name: "fileType"
        in: "query"
        description: "file type"
        required: false
        type: "string"
      - name: "workspaceName"
        in: "query"
        description: "workspace name if blank its the current work space"
        required: false
        type: "string"
      responses:
        200:
          description: "Delete file"
          schema:
            type: file
        400:
          description: "Bad Request"
  /files:
    get:
      security:
      - Bearer: []
      tags:
      - "file"
      summary: "list files of a requested type"
      description: ""
      operationId: "filesGet"
      produces:
      - "application/json"
      consumes:
      - "application/json"
      parameters:
      - name: "fileExtensions"
        in: "query"
        description: "file extensions"
        required: false
        type: "string"
      - name: "fileType"
        in: "query"
        description: "file type"
        required: true
        type: "string"
      - name: "workspaceName"
        in: "query"
        description: "workspace name"
        required: false
        type: "string"
      responses:
        200:
          description: "OK"
        400:
          description: "Bad Request"
  /work_spaces:
    get:
      security:
      - Bearer: []
      tags:
      - "file"
      summary: "list work spaces"
      description: ""
      operationId: "workSpacesGet"
      produces:
      - "application/json"
      parameters: []
      responses:
        200:
          description: "OK"
        400:
          description: "Bad Request"
  /workspace_backups:
    get:
      security:
      - Bearer: []
      tags:
      - "workspace"
      summary: "Get workspace backup files"
      description: "Get the list of backup files for a specified workspace"
      operationId: "workSpaceBackupsGet"
      produces:
      - "application/json"
      parameters:
      - name: "workspaceFile"
        in: "query"
        description: "Name of the workspace, blank for current"
        required: false
        type: "string"
      responses:
        200:
          description: "Successfully retrieved backup files"
          schema:
            $ref: '#/definitions/BackupObjectDTO'
        400:
          description: "Bad Request - Invalid workspace name or other parameter error"
  /workspace_backup_restore:
    get:
      security:
      - Bearer: []
      tags:
      - "workspace"
      summary: "Restore workspace backup file"
      description: "Restore the passed workspace backup file name"
      operationId: "workSpaceBackupsRestore"
      produces:
      - "application/json"
      parameters:
      - name: "workspaceFile"
        in: "query"
        description: "Name of the workspace, blank for current"
        required: true
        type: "string"
      responses:
        200:
          description: "Successfully restored backup file"
        400:
          description: "Bad Request - Invalid workspace name or other parameter error"
  /work_space:
    get:
      security:
      - Bearer: []
      tags:
      - "workspace"
      summary: "download a workspace as zip file"
      description: ""
      operationId: "workspaceGet"
      produces:
      - "application/octet-stream"
      consumes:
      - "application/json"
      parameters:
      - name: "workspaceName"
        in: "query"
        description: "workspace name"
        required: false
        type: "string"
      responses:
        200:
          description: "Download workspace"
        400:
          description: "Bad Request"
    post:
      security:
      - Bearer: []
      tags:
      - "workspace"
      summary: "upload a zip for a work space"
      description: ""
      operationId: "workspacePost"
      produces:
      - "application/json"
      consumes:
      - "multipart/form-data"
      parameters:
      - name: "file"
        in: "formData"
        description: "file"
        required: true
        type: "file"
      responses:
        200:
          description: "Upload workspace"
        400:
          description: "Bad Request"
    delete:
      security:
      - Bearer: []
      tags:
      - "workspace"
      summary: "Delete a workspace."
      description: "Delete a workspace."
      operationId: "workspaceDelete"
      produces:
      - "application/json"    
      consumes:
      - "application/json"
      parameters:
      - name: "workspaceName"
        in: "query"
        description: "workspace name"
        required: true
        type: "string"
      responses:
        200:
          description: "Delete workspace"
        400:
          description: "Bad Request"
  /login_user:
    post:
      security:
      - Bearer: []
      tags:
      - "auth"
      summary: "Login to SDG."
      description: "Login to SDG."
      operationId: "loginUser"
      consumes:
      - application/x-www-form-urlencoded
      produces:
      - "application/json"
      parameters:
      - name: "username"
        in: "formData"
        description: "user name"
        required: true
        type: "string"
      - name: "password"
        in: "formData"
        description: "user password"
        required: true
        type: "string"
        format: "password"
      - name: "isForcingUsersLogoff"
        in: "formData"
        description: "is forcing other users with same or lesser role to logoff"
        required: false
        type: "boolean"
      responses:
        200:
          description: "OK"
          schema:
            $ref: '#/definitions/SDGCheckAuthDTO'
        400:
          description: "Bad Request"
        401:
          description: "Unauthorized"
        403:
          description: "Forbidden"
        409:
          description: "Conflict"
        419:
          description: "Authentication Timeout"
  /logoff_user:
    post:
      security:
      - Bearer: []
      tags:
      - "auth"
      summary: "Log off from SDG."
      description: "Logoff from SDG."
      operationId: "logoffUser"
      consumes:
      - "application/x-www-form-urlencoded"
      responses:
        200:
          description: "OK"
        400:
          description: "Bad Request"
        401:
          description: "Unauthorized"
  /logoff_user_force:
    post:
      security:
      - Bearer: []
      tags:
      - "auth"
      summary: "Force a user Log off from SDG."
      description: "Force Logoff from SDG."
      operationId: "logoffUserForce"
      consumes:
      - "application/x-www-form-urlencoded"
      parameters:
      - name: "session"
        in: "formData"
        description: "session guid"
        required: true
        type: "string"
      responses:
        200:
          description: "OK"
        400:
          description: "Bad Request"
        401:
          description: "Unauthorized"
  /logoff_remote_user:
    post:
      security:
      - Bearer: []
      tags:
      - "auth"
      summary: "Log off remote user from SDG."
      description: "Log off remote user from SDG."
      operationId: "logoffRemoteUser"
      consumes:
      - "application/x-www-form-urlencoded"
      parameters:
      - name: "remoteUsername"
        in: "formData"
        description: "user name"
        required: true
        type: "string"
      responses:
        200:
          description: "OK"
        400:
          description: "Bad Request"
        401:
          description: "Unauthorized"
  /check_auth:
    post:
      tags:
      - "auth"
      operationId: "checkAuth"
      produces:
      - "application/json"
      consumes:
      - "application/x-www-form-urlencoded"
      parameters:
      - name: "token"
        in: "formData"
        description: "session token"
        required: true
        type: "string"
      responses:
        200:
          description: "No Auth"
          schema:
            $ref: '#/definitions/SDGCheckAuthDTO'
        401:
          description: "Unauthorized"
          schema:
            $ref: '#/definitions/SDGCheckAuthDTO'
        400:
          description: "Bad Request"
  /is_token_valid/{token}:
    get:
      tags:
      - "auth"
      summary: "Ask user db if token is valid"
      description: "Ask user db if token is valid."
      operationId: "isTokenValid"
      consumes:
      - text/plain
      produces:
      - "application/json"
      parameters:
      - name: "token"
        in: "path"
        description: "token GUID"
        required: true
        type: "string"
      responses:
        200:
          description: "OK"
        400:
          description: "Bad Request"
  /change_user_password:
    post:
      security:
      - Bearer: []
      tags:
      - "auth"
      summary: "Change password of user."
      description: "Change password of user."
      operationId: "changeUserPassword"
      consumes:
      - application/x-www-form-urlencoded
      parameters:
      - name: "username"
        in: "formData"
        description: "user name"
        required: true
        type: "string"
      - name: "oldpassword"
        in: "formData"
        description: "old user password"
        required: true
        type: "string"
        format: "password"
      - name: "password"
        in: "formData"
        description: "user password"
        required: true
        type: "string"
        format: "password"
      responses:
        200:
          description: "OK"
        204:
          description: "No Content"
        400:
          description: "Bad Request"
        401:
          description: "Unauthorized"
  /reset_user_password:
    post:
      security:
      - Bearer: []
      tags:
      - "auth"
      summary: "Reset password of user."
      description: "Reset password of user."
      operationId: "resetUserPassword"
      consumes:
      - application/x-www-form-urlencoded
      parameters:
      - name: "username"
        in: "formData"
        description: "user name"
        required: true
        type: "string"
      - name: "password"
        in: "formData"
        description: "user password"
        required: true
        type: "string"
        format: "password"
      responses:
        200:
          description: "OK"
        204:
          description: "No Content"
        400:
          description: "Bad Request"
        401:
          description: "Unauthorized"
  /users:
    get:
      security:
      - Bearer: []
      tags:
      - "auth"
      summary: "Get all users."
      description: ""
      operationId: "getUsers"
      produces:
      - "application/json"
      parameters:
      - name: "userFilter"
        in: "query"
        description: "user filter (i.e. user name must contain this, empty string returns all)"
        required: false
        type: "string"
      responses:
        200:
          description: "OK"
          schema:
            type: array
            items:
              $ref: '#/definitions/UserObjectDTO'
        204:
          description: "No Content"
        400:
          description: "Bad Request"
  /user/{username}:
    post:
      security:
      - Bearer: []
      tags:
      - "auth"
      summary: "Add a user."
      description: "Add a user."
      operationId: "addUser"
      produces:
      - "application/json"
      consumes:
      - application/x-www-form-urlencoded
      parameters:
      - name: "username"
        in: "path"
        description: "user name"
        required: true
        type: "string"
      - name: "password"
        in: "formData"
        description: "user password"
        required: true
        type: "string"
        format: "password"
      - name: "email"
        in: "formData"
        description: "user email"
        required: false
        type: "string"
      - name: "isactive"
        in: "formData"
        description: "user is active"
        required: true
        type: "boolean"
      - name: "role"
        in: "formData"
        description: "user role"
        required: true
        type: "integer"
      responses:
        200:
          description: "Success"
        400:
          description: "Bad Request"
        409:
          description: "Conflict"
    delete:
      security:
      - Bearer: []
      tags:
      - "auth"
      summary: "Delete a user."
      description: "Delete a user."
      operationId: "deleteUser"
      consumes:
      - text/plain
      produces:
      - "application/json"
      parameters:
      - name: "username"
        in: "path"
        description: "user name"
        required: true
        type: "string"
      responses:
        200:
          description: "Success"
        204:
          description: "No Content"
        400:
          description: "Bad Request"
    put:
      security:
      - Bearer: []
      tags:
      - "auth"
      summary: "Update a user."
      description: "Update a user."
      operationId: "updateUser"
      produces:
      - "application/json"
      consumes:
      - application/x-www-form-urlencoded
      parameters:
      - name: "username"
        in: "path"
        description: "user name"
        required: true
        type: "string"
      - name: "email"
        in: "formData"
        description: "user email"
        required: false
        type: "string"
      - name: "isactive"
        in: "formData"
        description: "user is active"
        required: true
        type: "boolean"
      - name: "role"
        in: "formData"
        description: "user role"
        required: true
        type: "integer"
      responses:
        200:
          description: "Success"
        204:
          description: "No Content"
        400:
          description: "Bad Request"
    get:
      security:
      - Bearer: []
      tags:
      - "auth"
      summary: "Get a user."
      description: "Get a user."
      operationId: "getUser"
      consumes:
      - text/plain
      produces:
      - "application/json"
      parameters:
      - name: "username"
        in: "path"
        description: "user name"
        required: true
        type: "string"
      responses:
        200:
          description: "OK"
          schema:
            $ref: '#/definitions/UserObjectDTO'
        204:
          description: "No Content"
        400:
          description: "Bad Request"
  /auditlogentries:
    get:
      security:
      - Bearer: []
      tags:
      - "audit"
      summary: "Get all log entries."
      description: "filters are all ored together, time is a C language time_t representing the number of seconds elapsed since 00:00 hours, Jan 1, 1970 UTC"
      operationId: "getLogEntries"
      produces:
      - "application/json"
      parameters:
      - name: "userFilter"
        in: "query"
        description: "name filter for username field in AuditLogEntryDTO, blank to disable"
        required: false
        type: "string"
      - name: "startDateFilter"
        in: "query"
        description: "start date filter for log_time field in AuditLogEntryDTO, 0 to disable, must also spec endDate"
        required: false
        type: "number"
      - name: "endDateFilter"
        in: "query"
        description: "end date filter for log_time field in AuditLogEntryDTO, 0 to disable, must also spec startDate"
        required: false
        type: "number"
      responses:
        200:
          description: "OK"
          schema:
            type: array
            items:
              $ref: '#/definitions/AuditLogEntryDTO'
        204:
          description: "No Content"
        400:
          description: "Bad Request"
  /get_log_entries_range:
    get:
      security:
      - Bearer: []
      tags:
      - "log"
      summary: "Get log items."
      description: "Get protocol data object items as array"
      operationId: "getLogEntriesRange"
      produces:
      - "application/json"
      parameters:
      - name: "startEntryID"
        in: "query"
        description: "Retreive log entries starting with startEntryID"
        required: false
        type: "number"
      - name: "endEntryID"
        in: "query"
        description: "Retreive log entries ending with endEntryID"
        required: false
        type: "number"
      responses:
        200:
          description: "OK"
          schema:
            type: array
            items:
              $ref: '#/definitions/LogEntryDTO'
        204:
          description: "No Content"
        400:
          description: "Bad Request"
  /get_num_log_entries:
    get:
      security:
      - Bearer: []
      tags:
      - "log"
      summary: "Retrieve number of lines in protocol buffer"
      description: "Get number of lines"
      operationId: "getNumLogEntries"
      produces:
      - "application/json"
      parameters: []
      responses:
        200:
          schema:
            type: number
          description: "Number of lines"
        400:
          description: "Bad Request"
  /get_mirror_all_to_log_file:
    get:
      security:
      - Bearer: []
      tags:
      - "log"
      summary: "Retrieve MirrorAllToLogFile setting for logger"
      description: "Get MirrorAllToLogFile setting"
      operationId: "getMirrorAllToLogFile"
      produces:
      - "application/json"
      parameters: []
      responses:
        200:
          description: "Get MirrorAllToLogFile setting"
        400:
          description: "Bad Request"
  /set_mirror_all_to_log_file:
    post:
      security:
      - Bearer: []
      tags:
      - "log"
      summary: "Set MirrorAllToLogFile setting for logger"
      description: "Set MirrorAllToLogFile setting"
      operationId: "putMirrorAllToLogFile"
      produces:
      - "application/json"
      consumes:
      - application/x-www-form-urlencoded
      parameters:
      - name: "mirrorAllToLogFile"
        in: "query"
        description: "True to Mirror All To Log File"
        required: true
        type: "boolean"
      responses:
        200:
          description: "Success"
        400:
          description: "Bad Request"
  /set_pause_logging:
    post:
      security:
      - Bearer: []
      tags:
      - "log"
      summary: "Set Pause setting for logger"
      description: "Set true to pause logging"
      operationId: "setPauseLogging"
      produces:
      - "application/json"
      consumes:
      - application/x-www-form-urlencoded
      parameters:
      - name: "pauseLogging"
        in: "query"
        description: "True to pause logging"
        required: true
        type: "boolean"
      responses:
        200:
          description: "Success"
        400:
          description: "Bad Request"
  /get_last_log_entry_id:
    get:
      security:
      - Bearer: []
      tags:
      - "log"
      summary: "Retrieve last log entry id"
      description: "Get last log entry id"
      operationId: "getLastLogEntryID"
      produces:
      - "application/json"
      parameters: []
      responses:
        200:
          schema:
            type: number
          description: "Last log entry id"
        400:
          description: "Bad Request"
  /get_max_log_entries:
    get:
      security:
      - Bearer: []
      tags:
      - "log"
      summary: "Retrieve max log entries to store on server"
      description: "Get max log entries"
      operationId: "getMaxLogEntries"
      produces:
      - "application/json"
      parameters: []
      responses:
        200:
          schema:
            type: number
          description: "Max log entries setting"
        400:
          description: "Bad Request"
  /set_max_log_entries:
    post:
      security:
      - Bearer: []
      tags:
      - "log"
      summary: "Set max log entries"
      description: "Set max log entries"
      operationId: "putMaxLogEntries"
      produces:
      - "application/json"
      consumes:
      - application/x-www-form-urlencoded
      parameters:
      - name: "maxLogEntries"
        in: "query"
        description: "Max log entries to store on server"
        required: true
        type: "integer"
        format: "int64"
      responses:
        200:
          description: "Success"
        400:
          description: "Bad Request"  
  /get_log_filter_config:
    get:
      security:
      - Bearer: []
      tags:
      - "log"
      summary: "Retrieve severity and category masks for each of source: SDG=0, SCL=1, 6T=2"
      description: "Retrieve log filter masks"
      operationId: "getLogFilterConfig"
      produces:
      - "application/json"
      parameters: []
      responses:
        200:
          description: "Retrieve severity and category masks for each of source: SDG=0, SCL=1, 6T=2"
          schema:
            type: array
            items:
              $ref: '#/definitions/LogConfigMaskDTO'
        400:
          description: "Bad Request"
  /set_log_filter_config:
    post:
      security:
      - Bearer: []
      tags:
      - "log"
      summary: "Set severity mask"
      description: "Set severity mask"
      operationId: "putLogFilterConfig"
      produces:
      - "application/json"
      consumes:
      - application/json
      parameters:
      - name: "logconfigmasks"
        in: "body"
        description: "Severity and category mask for each source"
        required: true
        schema:
          type: array
          items:
            $ref: '#/definitions/LogConfigMaskDTO'
      responses:
        200:
          description: "Set Severity and category mask for each source"
        400:
          description: "Bad Request"
  /set_max_suppression_time:
    post:
      security:
      - Bearer: []
      tags:
      - "log"
      summary: "Set max log suppression time"
      description: "Set log suppresion time in seconds, 0 disables"
      operationId: "setMaxSuppressionTime"
      produces:
      - "application/json"
      consumes:
      - application/x-www-form-urlencoded
      parameters:
      - name: "maxSuppressionTime"
        in: "query"
        description: "How long to suppress duplicate log messages in seconds"
        required: true
        type: "integer"
        format: "int64"
      responses:
        200:
          description: "Success"
        400:
          description: "Bad Request"
  /get_max_suppression_time:
    get:
      security:
      - Bearer: []
      tags:
      - "log"
      summary: "Retrieve max log suppression time"
      description: "Get  log suppression time in seconds"
      operationId: "getMaxSuppressionTime"
      produces:
      - "application/json"
      parameters: []
      responses:
        200:
          schema:
            type: number
          description: "Max log suppresion time"
        400:
          description: "Bad Request"
definitions: 
  LogConfigMask:
    description: "Log configuration mask"
    type: "object"
    properties:
      source:
        type: "string"
      severitymask:
        type: "integer"
      categorymask:
        type: "integer"
  LogEntry:
    description: "Sent by /getLogEnties web socket endpoint"
    type: "object"
    properties:
      source:
        type: "string"
      name:
        type: "string"
      id:
        type: "integer"
      category:
        type: "string"
      severity:
        type: "string"
      timeStamp:
        type: "string"
      message:
        type: "string"
  UserObjectDTO:
    type: "object"
    properties:
      username:
        type: "string"
      password:
        type: "string"
      email:
        type: "string"
      authenticated:
        type: "boolean"
      isactive:
        type: "boolean"
      role:
        type: "integer"
  IpAddressObjectDTO:
    type: "object"
    properties:
      ipAddress:
        type: "string"
  NetInterfaceObjectDTO:
    type: "object"
    properties:
      name:
        type: "string"
      description:
        type: "string"
      ipAddresses:
        description: "A collection of ip addresses bound to this interface"
        type: array
        items:
          $ref: '#/definitions/IpAddressObjectDTO'
  SDGConfigDTO:
    description: "represents the data stored in the gtw_config.json file"
    type: "object"
    properties:
      gtwDoValidateConfig:
        type: "boolean"
      gtwDoValidateIntegrity:
        type: "boolean"
      gtwDoWorkSpaceMD5Verification:
        type: "boolean"
#      gtwExeName:
#        type: "string"
      gtwHttpPort:
        type: "integer"
      gtwHost:
        type: "string"
      monHttpPort:
        type: "integer"
      monHost:
        type: "string"
      redHttpPort:
        type: "integer"
      redHost:
        type: "string"
      redIpNic1:
        type: "string"
      redNic1:
        type: "string"
      redIpNic2:
        type: "string"
      redNic2:
        type: "string"
      redPeerHost:
        type: "string"
      redPrimary:
        type: "boolean"
      redEnable:
        type: "boolean"
      redFavorPrimary:
        type: "boolean"
      redEnableSyncConfig:
        type: "boolean"
      redEnableAutoFailover:
        type: "boolean"
      redSlaveTolerancePercentage:
        type: "number"
      redMasterTolerancePercentage:
        type: "number"
      redCheckLocalEngineOnlineTimeout:
        type: "number"
      redCheckRemoteEngineOnlineTimeout:
        type: "number"
      redMonitorReStartRetryLimit:
        type: "number"
      redEngineReStartRetryLimit:
        type: "number"
      gtwWebDir:
        type: "string"
      gtwTzPath:
        type: "string"
      gtwAllowedIPs:
        type: "string"
      #httpsPrivateKeyFile:
      #  type: "string"
      #httpsPrivateKeyPassPhrase:
      #  type: "string"
      #httpsCertificateFile:
      #  type: "string"
      gtwDoAuth:
        type: "boolean"
      gtwPasswordComplexity:
        type: "number"
      gtwDoAudit:
        type: "boolean"
      gtwUseWebSSL:
        type: "boolean"
      gtwHttpsCertIsTmwSigned:
        type: "boolean"
      gtwUseLocalHostForEngineAndMonitorComms:
        type: "boolean"
      gtwEnableHttpDeflate:
        type: "boolean"
      gtwAuthExpVIEWER_ROLE:
        type: "integer"
      gtwAuthExpOPERATOR_ROLE:
        type: "integer"
      gtwAuthExpCONFIGURATOR_ROLE:
        type: "integer"
      gtwAuthExpSU_ROLE:
        type: "integer"
      gtwWsUpdateRate:
        type: "integer"
      gtwWsUpdateBlockSize:
        type: "integer"
      gtwHttpPageBlockSize:
        type: "integer"
      currentWorkSpaceName:
        type: "string"
      gtwMaxLogFiles:
        type: "integer"
      gtwMaxBackupFiles:
        type: "integer"
      fullLogOnRestart:
        type: "boolean"
      mirrorAllToLog:
        type: "boolean"
  TraceMaskEnumDTO:
    type: "object"
    properties:
      maskValue:
        type: "integer"
        enum:
        - value: 0x00000000
          description: TRACE_MASK_TRACE_NONE
        - value: 0x00000001
          description: TRACE_MASK_TRACE_STATUS
        - value: 0x00000002
          description: TRACE_MASK_APP_START_STOP
        - value: 0x00000004
          description: TRACE_MASK_APP_STATUS
        - value: 0x00000008
          description: TRACE_MASK_APP_ERROR
        - value: 0x00000010
          description: TRACE_MASK_APP_HTTP
        - value: 0x00000020
          description: TRACE_MASK_LICENSE
        - value: 0x00000040
          description: TRACE_MASK_OPC
        - value: 0x00000080
          description: TRACE_MASK_SERVICE_PROTO
        - value: 0x00000100
          description: TRACE_MASK_CUSTOM
        - value: 0x00000200
          description: TRACE_MASK_TARGET
        - value: 0x00000400
          description: TRACE_MASK_OPC_SU
        - value: 0x00000800
          description: TRACE_MASK_OPC_UA
  SDGAboutDTO:
    type: "object"
    properties:
      aboutContent:
        type: "string"
      currentVersion:
        type: "string"
      mpExpires:
        type: "string"
  SDGCheckAuthDTO:
    type: "object"
    properties:
      username:
        type: "string"
      token:
        type: "string"
      role:
        type: "integer"
  HealthObjectDTO:
    description: "Sent by /getHealth web socket endpoint"
    type: "object"
    properties:
      activeUsers:
        type: "array"
        items:
          type: "string"
      engineState:
        type: "string"
      isIniCsvDirty:
        type: "boolean"
      engineExitFailState:
        type: "string"
      monitorOk:
        type: "boolean"
      sysCpu:
        type: "number"
      sysMem:
        type: "number"
      sysDiskFree:
        type: "number"
      engineCpu:
        type: "number"
      engineMem:
        type: "number"
      redundancyOk: # i.e. is it enabled and can we talk to its service
        type: "boolean"
      redundancyIsPrimary: # from gtw_config is this one primary? if so true else false
        type: "boolean"
      redundancyLocalState: # SET_ACTIVE_RESULT enum
        type: "string"
      redundancyPeerState: # SET_ACTIVE_RESULT enum
        type: "string"
      redundancySyncState: # DO_SYNC_RESULT enum
        type: "string"
  MONHealthObjectDTO:
    description: "response to /mon_health api"
    type: "object"
    properties:
      monitorState:
        type: "string"
      monitorCpu:
        type: "number"
      monitorMem:
        type: "number"
  BackupObjectDTO:
    type: object
    description: "Response object containing the list of workspace backup files and operation status"
    properties:
      status:
        type: string
        description: "Operation status. Returns 'success' when backups are successfully retrieved"
        example: "success"
      error:
        type: string
        description: "Error message if the operation fails. Only present in error responses"
        example: "Backup directory does not exist"
      backups:
        type: array
        description: "Array of backup files found for the workspace. Empty array if no backups exist or in case of error"
        items:
          $ref: '#/definitions/BackupFileInfo'
  BackupFileInfo:
    type: object
    description: "Information about a single workspace backup file"
    properties:
      filename:
        type: string
        description: "Name of the backup file in format 'workspaceName_YYYYMMDD_HHMMSS.gws'"
        example: "myWorkspace_20241203_143000.gws"
      path:
        type: string
        description: "Full filesystem path to the backup file"
        example: "/path/to/backups/myWorkspace_20241203_143000.gws"
      size:
        type: integer
        format: int64
        description: "Size of the backup file in bytes"
        example: 1234567
      lastModified:
        type: string
        format: date-time
        description: "Timestamp when the backup file was last modified in format 'YYYY-MM-DD HH:MM:SS'"
        example: "2024-12-03 14:30:00"
    required:
      - filename
      - path
      - size
      - lastModified
  SET_ACTIVE_RESULTEnumDTO:
    type: string
    enum:
    - SET_ACTIVE_RESULT_OK_ACTIVE
    - SET_ACTIVE_RESULT_OK_INACTIVE
    - SET_ACTIVE_RESULT_OK_TIMEOUT_NO_PAIR
    - SET_ACTIVE_RESULT_TIMEOUT_ENGINE_START
    - SET_ACTIVE_RESULT_NO_CHANGE
  DO_SYNC_RESULTEnumDTO:
    type: string
    enum:
    - DO_SYNC_OK,
    - DO_SYNC_FAILED,
    - DO_SYNC_NOT_READY,
    - DO_SYNC_FAILED_IS_ACTIVE,
    - DO_SYNC_FAILED_NO_WS
  EngineStateEnumDTO:
    type: string
    enum:
    - ENG_NOT_RUNNING
    - ENG_RUNNING
    - ENG_STARTUP_STARTING
    - ENG_STARTUP_CSV_LOADED
    - ENG_STARTUP_INI_LOADED
    - ENG_STARTUP_DONE
    - ENG_SAVING_INI_CSV
    - ENG_SHUTTING_DOWN
    - ENG_ERROR_IN_INI
    - ENG_STARTUP_UNKNOWN
    - ENG_RUNNING_NO_LICENSE
  EngineExitFailStateEnumDTO:
    type: string
    enum:
    - SUCCESS
    - EXCEPTION
    - VALIDATE
    - HTTP_START
    - HTTP_CREATE
    - HTTPS_START
    - HTTPS_CREATE
    - CREATE_WORKSPACE_DIR
    - INI_LOAD
    - SAVE_POINTMAP
    - INIT_BEFORE_CSV
    - OPC_CLASSIC_START
    - LOAD_POINTMAP
    - CREATE_TRIAL_LICENSE
    - NO_LICENSE
    - NO_INI_FILE
    - INVALID_MONITOR_LOCATION
    - KEY_REJECTED
  BroadcastEventLogMaskEnumDTO:
    type: string
    enum:
    - refresh_tag
    - refresh_log_parameter
    - message_info
    - message_warning
    - message_error
    - message_debug
    - message_success
  BroadcastEventDTO:
    description: "Describes a broadcast event"
    type: "object"
    properties:
      messageLogMask:
        type: "number"
      messageType:
        type: "string"
      messageKey:
        type: "string"
      messageText:
        type: "string"
      messageTime:
        type: "string"
      parameters:
        type: "object"
  LogConfigMaskDTO:
    description: "Log configuration mask"
    type: "object"
    properties:
      source:
        type: "string"
      severitymask:
        type: "integer"
      categorymask:
        type: "integer"
  LogEntryDTO:
    description: "Sent by /getLogEnties web socket endpoint"
    type: "object"
    properties:
      source:
        type: "string"
      name:
        type: "string"
      id:
        type: "integer"
      category:
        type: "string"
      severity:
        type: "string"
      timeStamp:
        type: "string"
      message:
        type: "string"
  AuditLogEntryDTO:
    type: "object"
    properties:
      username:
        type: "string"
      role:
        type: "integer"
      ipAddr:
        type: "string"
      log_time:
        type: "number"
      action:
        type: "string"
      oldValue:
        type: "string"
      newValue:
        type: "string"