"""
Triangle MicroWorks SCADA Gateway Integration
Comprehensive integration with Triangle MicroWorks SCADA Data Gateway REST APIs
Supports Configuration API (58090), Runtime API (58080), and Redundancy API (58070)
"""

import asyncio
import logging
import json
import time
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional, Tuple, Set, Callable
import aiohttp
from aiohttp import ClientSession, ClientTimeout
from dataclasses import dataclass, field
import ssl
import certifi

@dataclass
class TMWConfig:
    """Triangle MicroWorks configuration"""
    base_url: str = "http://localhost"
    config_port: int = 58090
    runtime_port: int = 58080
    redundancy_port: int = 58070
    username: str = "admin"
    password: str = "password"
    timeout_seconds: int = 30
    ssl_verify: bool = False
    max_retries: int = 3
    retry_delay: float = 1.0

@dataclass
class DeviceConnection:
    """Device connection configuration for TMW"""
    device_id: str
    protocol: str  # DNP3, MODBUS, IEC61850
    connection_string: str
    enabled: bool = True
    polling_interval: int = 1000  # milliseconds
    timeout: int = 5000  # milliseconds
    retries: int = 3
    tags: List[Dict[str, Any]] = field(default_factory=list)

class TriangleMicroWorksClient:
    """Client for Triangle MicroWorks SCADA Data Gateway APIs"""
    
    def __init__(self, config: TMWConfig):
        self.logger = logging.getLogger("TMWClient")
        self.config = config
        self.session: Optional[ClientSession] = None
        self.auth_token: Optional[str] = None
        self.token_expires: Optional[datetime] = None
        
        # API endpoints
        self.config_api = f"{config.base_url}:{config.config_port}"
        self.runtime_api = f"{config.base_url}:{config.runtime_port}"
        self.redundancy_api = f"{config.base_url}:{config.redundancy_port}"
        
        # Connection tracking
        self.device_connections: Dict[str, DeviceConnection] = {}
        self.active_subscriptions: Set[str] = set()
        
        # Statistics
        self.stats = {
            "api_calls": 0,
            "successful_calls": 0,
            "failed_calls": 0,
            "authentication_attempts": 0,
            "devices_configured": 0,
            "tags_subscribed": 0,
            "measurements_received": 0,
            "events_received": 0
        }
        
    async def __aenter__(self):
        """Async context manager entry"""
        await self.connect()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.disconnect()
        
    async def connect(self):
        """Connect to Triangle MicroWorks SCADA Gateway"""
        timeout = ClientTimeout(total=self.config.timeout_seconds)
        connector = aiohttp.TCPConnector(ssl=ssl.create_default_context(cafile=certifi.where()) if self.config.ssl_verify else False)
        
        self.session = ClientSession(timeout=timeout, connector=connector)
        
        # Authenticate
        await self.authenticate()
        
        self.logger.info("Connected to Triangle MicroWorks SCADA Gateway")
        
    async def disconnect(self):
        """Disconnect from Triangle MicroWorks SCADA Gateway"""
        if self.auth_token:
            try:
                await self.logout()
            except Exception as e:
                self.logger.warning(f"Logout error: {e}")
                
        if self.session:
            await self.session.close()
            
        self.logger.info("Disconnected from Triangle MicroWorks SCADA Gateway")
        
    async def authenticate(self) -> bool:
        """Authenticate with Triangle MicroWorks"""
        self.stats["authentication_attempts"] += 1
        
        try:
            login_data = {
                "username": self.config.username,
                "password": self.config.password
            }
            
            async with self.session.post(f"{self.config_api}/login_user", json=login_data) as response:
                if response.status == 200:
                    result = await response.json()
                    self.auth_token = result.get("token")
                    
                    # Set token expiration (assume 1 hour if not provided)
                    expires_in = result.get("expires_in", 3600)
                    self.token_expires = datetime.now(timezone.utc) + timedelta(seconds=expires_in)
                    
                    self.logger.info("Authentication successful")
                    return True
                else:
                    self.logger.error(f"Authentication failed: HTTP {response.status}")
                    return False
                    
        except Exception as e:
            self.logger.error(f"Authentication error: {e}")
            return False
            
    async def logout(self):
        """Logout from Triangle MicroWorks"""
        if not self.auth_token:
            return
            
        try:
            headers = {"Authorization": f"Bearer {self.auth_token}"}
            async with self.session.post(f"{self.config_api}/logoff_user", headers=headers) as response:
                if response.status == 200:
                    self.logger.info("Logout successful")
                else:
                    self.logger.warning(f"Logout failed: HTTP {response.status}")
                    
        except Exception as e:
            self.logger.error(f"Logout error: {e}")
        finally:
            self.auth_token = None
            self.token_expires = None
            
    async def check_auth(self) -> bool:
        """Check if authentication is still valid"""
        if not self.auth_token or not self.token_expires:
            return False
            
        # Check if token is expired
        if datetime.now(timezone.utc) >= self.token_expires:
            self.logger.info("Token expired, re-authenticating")
            return await self.authenticate()
            
        try:
            headers = {"Authorization": f"Bearer {self.auth_token}"}
            async with self.session.get(f"{self.config_api}/check_auth", headers=headers) as response:
                return response.status == 200
                
        except Exception as e:
            self.logger.error(f"Auth check error: {e}")
            return False
            
    async def _make_api_call(self, method: str, url: str, **kwargs) -> Tuple[bool, Optional[Dict]]:
        """Make authenticated API call with retry logic"""
        self.stats["api_calls"] += 1
        
        # Ensure authentication
        if not await self.check_auth():
            if not await self.authenticate():
                self.stats["failed_calls"] += 1
                return False, None
                
        headers = kwargs.get("headers", {})
        headers["Authorization"] = f"Bearer {self.auth_token}"
        kwargs["headers"] = headers
        
        # Retry logic
        for attempt in range(self.config.max_retries):
            try:
                async with self.session.request(method, url, **kwargs) as response:
                    if response.status < 400:
                        self.stats["successful_calls"] += 1
                        if response.content_type == 'application/json':
                            return True, await response.json()
                        else:
                            return True, {"status": "success", "data": await response.text()}
                    else:
                        self.logger.warning(f"API call failed: {method} {url} - HTTP {response.status}")
                        
            except Exception as e:
                self.logger.error(f"API call error (attempt {attempt + 1}): {e}")
                
            if attempt < self.config.max_retries - 1:
                await asyncio.sleep(self.config.retry_delay * (attempt + 1))
                
        self.stats["failed_calls"] += 1
        return False, None
        
    async def configure_device_connection(self, device_connection: DeviceConnection) -> bool:
        """Configure device connection in Triangle MicroWorks"""
        config_data = {
            "device_id": device_connection.device_id,
            "protocol": device_connection.protocol,
            "connection_string": device_connection.connection_string,
            "enabled": device_connection.enabled,
            "polling_interval": device_connection.polling_interval,
            "timeout": device_connection.timeout,
            "retries": device_connection.retries
        }
        
        success, result = await self._make_api_call(
            "POST", 
            f"{self.config_api}/devices",
            json=config_data
        )
        
        if success:
            self.device_connections[device_connection.device_id] = device_connection
            self.stats["devices_configured"] += 1
            self.logger.info(f"Configured device: {device_connection.device_id}")
            return True
        else:
            self.logger.error(f"Failed to configure device: {device_connection.device_id}")
            return False
            
    async def configure_device_tags(self, device_id: str, tags: List[Dict[str, Any]]) -> bool:
        """Configure tags for a device"""
        if device_id not in self.device_connections:
            self.logger.error(f"Device not configured: {device_id}")
            return False
            
        tag_config = {
            "device_id": device_id,
            "tags": tags
        }
        
        success, result = await self._make_api_call(
            "POST",
            f"{self.config_api}/devices/{device_id}/tags",
            json=tag_config
        )
        
        if success:
            self.device_connections[device_id].tags = tags
            self.stats["tags_subscribed"] += len(tags)
            self.logger.info(f"Configured {len(tags)} tags for device: {device_id}")
            return True
        else:
            self.logger.error(f"Failed to configure tags for device: {device_id}")
            return False
            
    async def get_device_status(self, device_id: str) -> Optional[Dict[str, Any]]:
        """Get device status from runtime API"""
        success, result = await self._make_api_call(
            "GET",
            f"{self.runtime_api}/devices/{device_id}/status"
        )
        
        if success:
            return result
        else:
            return None
            
    async def get_tag_values(self, device_id: str, tag_names: Optional[List[str]] = None) -> Optional[Dict[str, Any]]:
        """Get current tag values from runtime API"""
        url = f"{self.runtime_api}/devices/{device_id}/tags"
        
        params = {}
        if tag_names:
            params["tags"] = ",".join(tag_names)
            
        success, result = await self._make_api_call("GET", url, params=params)
        
        if success:
            self.stats["measurements_received"] += len(result.get("tags", []))
            return result
        else:
            return None
            
    async def subscribe_to_tag_updates(self, device_id: str, tag_names: List[str], 
                                     callback: Callable[[Dict[str, Any]], None]) -> bool:
        """Subscribe to real-time tag updates"""
        subscription_data = {
            "device_id": device_id,
            "tags": tag_names,
            "callback_url": "http://localhost:8888/tag_updates"  # Would be actual callback endpoint
        }
        
        success, result = await self._make_api_call(
            "POST",
            f"{self.runtime_api}/subscriptions",
            json=subscription_data
        )
        
        if success:
            subscription_id = result.get("subscription_id")
            self.active_subscriptions.add(subscription_id)
            self.logger.info(f"Subscribed to {len(tag_names)} tags for device: {device_id}")
            return True
        else:
            return False
            
    async def send_control_command(self, device_id: str, tag_name: str, value: Any) -> bool:
        """Send control command to device"""
        command_data = {
            "device_id": device_id,
            "tag_name": tag_name,
            "value": value,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
        success, result = await self._make_api_call(
            "POST",
            f"{self.runtime_api}/devices/{device_id}/control",
            json=command_data
        )
        
        if success:
            self.logger.info(f"Sent control command: {device_id}.{tag_name} = {value}")
            return True
        else:
            self.logger.error(f"Failed to send control command: {device_id}.{tag_name}")
            return False
            
    async def get_system_health(self) -> Optional[Dict[str, Any]]:
        """Get system health status"""
        success, result = await self._make_api_call("GET", f"{self.runtime_api}/health")
        return result if success else None
        
    async def get_redundancy_status(self) -> Optional[Dict[str, Any]]:
        """Get redundancy status from redundancy API"""
        success, result = await self._make_api_call("GET", f"{self.redundancy_api}/status")
        return result if success else None
        
    async def trigger_redundancy_switchover(self) -> bool:
        """Trigger redundancy switchover"""
        success, result = await self._make_api_call("POST", f"{self.redundancy_api}/switchover")
        
        if success:
            self.logger.info("Redundancy switchover triggered")
            return True
        else:
            self.logger.error("Failed to trigger redundancy switchover")
            return False
            
    async def upload_configuration_file(self, file_path: str, config_type: str) -> bool:
        """Upload configuration file"""
        try:
            with open(file_path, 'rb') as f:
                file_data = f.read()
                
            form_data = aiohttp.FormData()
            form_data.add_field('file', file_data, filename=Path(file_path).name)
            form_data.add_field('config_type', config_type)
            
            headers = {"Authorization": f"Bearer {self.auth_token}"}
            
            async with self.session.post(
                f"{self.config_api}/upload",
                data=form_data,
                headers=headers
            ) as response:
                if response.status == 200:
                    self.logger.info(f"Uploaded configuration file: {file_path}")
                    return True
                else:
                    self.logger.error(f"Failed to upload file: HTTP {response.status}")
                    return False
                    
        except Exception as e:
            self.logger.error(f"File upload error: {e}")
            return False
            
    async def download_configuration_file(self, config_type: str, output_path: str) -> bool:
        """Download configuration file"""
        try:
            params = {"config_type": config_type}
            headers = {"Authorization": f"Bearer {self.auth_token}"}
            
            async with self.session.get(
                f"{self.config_api}/download",
                params=params,
                headers=headers
            ) as response:
                if response.status == 200:
                    with open(output_path, 'wb') as f:
                        async for chunk in response.content.iter_chunked(8192):
                            f.write(chunk)
                    
                    self.logger.info(f"Downloaded configuration file: {output_path}")
                    return True
                else:
                    self.logger.error(f"Failed to download file: HTTP {response.status}")
                    return False
                    
        except Exception as e:
            self.logger.error(f"File download error: {e}")
            return False
            
    def get_statistics(self) -> Dict[str, Any]:
        """Get client statistics"""
        return {
            **self.stats,
            "connected": self.auth_token is not None,
            "token_expires": self.token_expires.isoformat() if self.token_expires else None,
            "configured_devices": len(self.device_connections),
            "active_subscriptions": len(self.active_subscriptions)
        }

class TMWIntegrationCoordinator:
    """Coordinates Triangle MicroWorks integration with simulated devices"""
    
    def __init__(self, tmw_config: TMWConfig):
        self.logger = logging.getLogger("TMWIntegrationCoordinator")
        self.tmw_config = tmw_config
        self.tmw_client: Optional[TriangleMicroWorksClient] = None
        
        # Device mapping
        self.device_mappings: Dict[str, DeviceConnection] = {}
        self.measurement_handlers: List[Callable] = []
        self.event_handlers: List[Callable] = []
        
        # Integration statistics
        self.integration_stats = {
            "devices_integrated": 0,
            "measurements_forwarded": 0,
            "events_forwarded": 0,
            "integration_errors": 0,
            "last_sync": None
        }
        
    async def start_integration(self):
        """Start Triangle MicroWorks integration"""
        try:
            self.tmw_client = TriangleMicroWorksClient(self.tmw_config)
            await self.tmw_client.connect()
            
            self.logger.info("Triangle MicroWorks integration started")
            
        except Exception as e:
            self.logger.error(f"Failed to start TMW integration: {e}")
            raise
            
    async def stop_integration(self):
        """Stop Triangle MicroWorks integration"""
        if self.tmw_client:
            await self.tmw_client.disconnect()
            self.tmw_client = None
            
        self.logger.info("Triangle MicroWorks integration stopped")
        
    async def integrate_simulated_device(self, device_simulator, protocol_mapping: Dict[str, Any]) -> bool:
        """Integrate a simulated device with Triangle MicroWorks"""
        if not self.tmw_client:
            self.logger.error("TMW client not connected")
            return False
            
        device_id = device_simulator.config.device_id
        
        # Create device connection configuration
        device_connection = DeviceConnection(
            device_id=device_id,
            protocol=protocol_mapping.get("protocol", "DNP3"),
            connection_string=protocol_mapping.get("connection_string", f"tcp://localhost:20000"),
            polling_interval=protocol_mapping.get("polling_interval", 1000),
            timeout=protocol_mapping.get("timeout", 5000)
        )
        
        # Configure device in TMW
        success = await self.tmw_client.configure_device_connection(device_connection)
        if not success:
            return False
            
        # Configure tags
        tags = self._generate_device_tags(device_simulator)
        success = await self.tmw_client.configure_device_tags(device_id, tags)
        if not success:
            return False
            
        # Store mapping
        self.device_mappings[device_id] = device_connection
        self.integration_stats["devices_integrated"] += 1
        
        # Register measurement handler
        device_simulator.add_measurement_handler(self._handle_device_measurement)
        device_simulator.add_event_handler(self._handle_device_event)
        
        self.logger.info(f"Integrated device with TMW: {device_id}")
        return True
        
    def _generate_device_tags(self, device_simulator) -> List[Dict[str, Any]]:
        """Generate tag configuration for device"""
        tags = []
        device_type = device_simulator.config.device_type.value
        
        if device_type in ["power_meter", "phasor_measurement_unit"]:
            # Voltage tags
            for phase in ["A", "B", "C"]:
                tags.append({
                    "name": f"V{phase}_MAG",
                    "description": f"Phase {phase} voltage magnitude",
                    "data_type": "FLOAT32",
                    "address": len(tags),
                    "scaling": 1.0,
                    "units": "V"
                })
                
            # Current tags
            for phase in ["A", "B", "C"]:
                tags.append({
                    "name": f"I{phase}_MAG",
                    "description": f"Phase {phase} current magnitude",
                    "data_type": "FLOAT32",
                    "address": len(tags),
                    "scaling": 1.0,
                    "units": "A"
                })
                
            # Power tags
            tags.extend([
                {
                    "name": "ACTIVE_POWER",
                    "description": "Total active power",
                    "data_type": "FLOAT32",
                    "address": len(tags),
                    "scaling": 1.0,
                    "units": "W"
                },
                {
                    "name": "REACTIVE_POWER",
                    "description": "Total reactive power",
                    "data_type": "FLOAT32",
                    "address": len(tags),
                    "scaling": 1.0,
                    "units": "VAR"
                },
                {
                    "name": "FREQUENCY",
                    "description": "System frequency",
                    "data_type": "FLOAT32",
                    "address": len(tags),
                    "scaling": 1.0,
                    "units": "Hz"
                }
            ])
            
        elif device_type == "protective_relay":
            # Protection status tags
            tags.extend([
                {
                    "name": "TRIP_STATUS",
                    "description": "Trip status",
                    "data_type": "BOOLEAN",
                    "address": len(tags)
                },
                {
                    "name": "ALARM_STATUS",
                    "description": "Alarm status",
                    "data_type": "BOOLEAN",
                    "address": len(tags)
                },
                {
                    "name": "PICKUP_STATUS",
                    "description": "Pickup status",
                    "data_type": "BOOLEAN",
                    "address": len(tags)
                }
            ])
            
        elif device_type == "digital_fault_recorder":
            # DFR status tags
            tags.extend([
                {
                    "name": "RECORDING_STATUS",
                    "description": "Recording status",
                    "data_type": "BOOLEAN",
                    "address": len(tags)
                },
                {
                    "name": "TRIGGER_STATUS",
                    "description": "Trigger status",
                    "data_type": "BOOLEAN",
                    "address": len(tags)
                },
                {
                    "name": "MEMORY_USAGE",
                    "description": "Memory usage percentage",
                    "data_type": "FLOAT32",
                    "address": len(tags),
                    "scaling": 1.0,
                    "units": "%"
                }
            ])
            
        return tags
        
    async def _handle_device_measurement(self, measurement: Dict[str, Any]):
        """Handle measurement from simulated device"""
        try:
            # Forward measurement to Triangle MicroWorks
            device_id = measurement.get("device_id")
            if device_id in self.device_mappings:
                # Update tag values in TMW
                tag_updates = self._convert_measurement_to_tags(measurement)
                
                # This would typically update the TMW tag values
                # For now, we'll just log the measurement
                self.integration_stats["measurements_forwarded"] += 1
                self.integration_stats["last_sync"] = datetime.now(timezone.utc).isoformat()
                
            # Forward to registered handlers
            for handler in self.measurement_handlers:
                await handler(measurement)
                
        except Exception as e:
            self.logger.error(f"Measurement handling error: {e}")
            self.integration_stats["integration_errors"] += 1
            
    async def _handle_device_event(self, event: Dict[str, Any]):
        """Handle event from simulated device"""
        try:
            # Forward event to Triangle MicroWorks
            device_id = event.get("device_id")
            if device_id in self.device_mappings:
                # This would typically send event to TMW
                self.integration_stats["events_forwarded"] += 1
                
            # Forward to registered handlers
            for handler in self.event_handlers:
                await handler(event)
                
        except Exception as e:
            self.logger.error(f"Event handling error: {e}")
            self.integration_stats["integration_errors"] += 1
            
    def _convert_measurement_to_tags(self, measurement: Dict[str, Any]) -> Dict[str, Any]:
        """Convert device measurement to TMW tag format"""
        tag_updates = {}
        
        # Map measurement fields to tag names
        field_mapping = {
            "voltage_a": "VA_MAG",
            "voltage_b": "VB_MAG", 
            "voltage_c": "VC_MAG",
            "current_a": "IA_MAG",
            "current_b": "IB_MAG",
            "current_c": "IC_MAG",
            "active_power": "ACTIVE_POWER",
            "reactive_power": "REACTIVE_POWER",
            "frequency": "FREQUENCY"
        }
        
        for field, tag_name in field_mapping.items():
            if field in measurement:
                tag_updates[tag_name] = measurement[field]
                
        return tag_updates
        
    def add_measurement_handler(self, handler: Callable):
        """Add measurement handler"""
        self.measurement_handlers.append(handler)
        
    def add_event_handler(self, handler: Callable):
        """Add event handler"""
        self.event_handlers.append(handler)
        
    def get_integration_statistics(self) -> Dict[str, Any]:
        """Get integration statistics"""
        stats = {
            **self.integration_stats,
            "tmw_connected": self.tmw_client is not None and self.tmw_client.auth_token is not None
        }
        
        if self.tmw_client:
            stats["tmw_stats"] = self.tmw_client.get_statistics()
            
        return stats
