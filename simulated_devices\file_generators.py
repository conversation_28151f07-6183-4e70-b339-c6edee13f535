"""
File Generators - IEEE C37.111 COMTRADE and IEEE 1159.3 PQDIF file generation
Creates realistic power system event files for testing
"""

import os
import struct
import numpy as np
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
import uuid

class COMTRADEGenerator:
    """
    IEEE C37.111 COMTRADE file generator
    Creates configuration (.cfg) and data (.dat) files for fault events
    """
    
    def __init__(self, output_directory: str = "comtrade_files"):
        self.logger = logging.getLogger("COMTRADEGenerator")
        self.output_dir = Path(output_directory)
        self.output_dir.mkdir(exist_ok=True)
        
    async def generate_fault_recording(self, event_data: Dict[str, Any], 
                                     waveform_data: Dict[str, np.ndarray],
                                     device_config: Dict[str, Any]) -> str:
        """Generate COMTRADE files for a fault event"""
        
        # Create unique filename
        timestamp = datetime.fromisoformat(event_data["timestamp"])
        filename_base = f"{device_config['device_id']}_{timestamp.strftime('%Y%m%d_%H%M%S')}"
        
        # Generate configuration file
        cfg_path = self.output_dir / f"{filename_base}.cfg"
        await self._generate_cfg_file(cfg_path, event_data, device_config, waveform_data)
        
        # Generate data file
        dat_path = self.output_dir / f"{filename_base}.dat"
        await self._generate_dat_file(dat_path, waveform_data, device_config)
        
        # Generate header file (optional)
        hdr_path = self.output_dir / f"{filename_base}.hdr"
        await self._generate_hdr_file(hdr_path, event_data, device_config)
        
        self.logger.info(f"Generated COMTRADE files: {filename_base}")
        return filename_base
        
    async def _generate_cfg_file(self, cfg_path: Path, event_data: Dict[str, Any],
                                device_config: Dict[str, Any], waveform_data: Dict[str, np.ndarray]):
        """Generate COMTRADE configuration file"""
        
        station_name = device_config.get("station_name", "SUBSTATION")
        device_id = device_config["device_id"]
        rev_year = 2013  # COMTRADE revision year
        
        # Count channels
        analog_channels = len([k for k in waveform_data.keys() if k.startswith(('V', 'I'))])
        digital_channels = len([k for k in waveform_data.keys() if k.startswith('D')])
        total_channels = analog_channels + digital_channels
        
        # Sampling rate info
        sampling_rate = device_config.get("sampling_rate", 4000)
        samples_count = len(next(iter(waveform_data.values())))
        
        # Trigger time
        trigger_time = datetime.fromisoformat(event_data["timestamp"])
        
        cfg_content = []
        
        # Line 1: Station name, device ID, revision year
        cfg_content.append(f"{station_name},{device_id},{rev_year}")
        
        # Line 2: Total channels, analog channels, digital channels
        cfg_content.append(f"{total_channels},{analog_channels},{digital_channels}")
        
        # Analog channel definitions
        channel_num = 1
        for channel_name, data in waveform_data.items():
            if channel_name.startswith(('V', 'I')):
                # Channel number, name, phase, circuit component, units, multiplier, offset, skew, min, max, primary, secondary, PS/S
                if channel_name.startswith('V'):
                    units = "V"
                    primary = 138000.0  # Primary voltage
                    secondary = 120.0   # Secondary voltage
                    multiplier = primary / secondary
                else:  # Current
                    units = "A"
                    primary = 1200.0    # Primary current
                    secondary = 5.0     # Secondary current
                    multiplier = primary / secondary
                    
                phase = channel_name[-1] if channel_name[-1] in 'ABC' else ''
                cfg_content.append(f"{channel_num},{channel_name},{phase},,{units},{multiplier},0,0,-32767,32767,{primary},{secondary},S")
                channel_num += 1
                
        # Digital channel definitions
        for channel_name, data in waveform_data.items():
            if channel_name.startswith('D'):
                # Channel number, name, phase, circuit component, normal state
                cfg_content.append(f"{channel_num},{channel_name},,,0")
                channel_num += 1
                
        # Line frequency
        cfg_content.append("60.0")
        
        # Sampling rate information
        cfg_content.append("1")  # Number of sampling rates
        cfg_content.append(f"{sampling_rate},{samples_count}")
        
        # First data point time and trigger time
        first_time = trigger_time - timedelta(seconds=0.25)  # 250ms pre-trigger
        cfg_content.append(f"{first_time.strftime('%d/%m/%Y,%H:%M:%S.%f')[:-3]}")
        cfg_content.append(f"{trigger_time.strftime('%d/%m/%Y,%H:%M:%S.%f')[:-3]}")
        
        # Data file type and time multiplication factor
        cfg_content.append("ASCII")  # or BINARY
        cfg_content.append("1.0")
        
        # Write configuration file
        with open(cfg_path, 'w') as f:
            f.write('\n'.join(cfg_content))
            
    async def _generate_dat_file(self, dat_path: Path, waveform_data: Dict[str, np.ndarray],
                                device_config: Dict[str, Any]):
        """Generate COMTRADE data file (ASCII format)"""
        
        # Get sample count
        samples_count = len(next(iter(waveform_data.values())))
        
        # Create time stamps (microseconds)
        sampling_rate = device_config.get("sampling_rate", 4000)
        time_step = 1000000 // sampling_rate  # microseconds
        
        with open(dat_path, 'w') as f:
            for sample_idx in range(samples_count):
                line_data = [str(sample_idx + 1), str(sample_idx * time_step)]
                
                # Add analog channel data
                for channel_name, data in waveform_data.items():
                    if channel_name.startswith(('V', 'I')):
                        # Convert to integer representation
                        value = int(data[sample_idx] * 100)  # Scale for better resolution
                        line_data.append(str(value))
                        
                # Add digital channel data
                for channel_name, data in waveform_data.items():
                    if channel_name.startswith('D'):
                        value = int(data[sample_idx])
                        line_data.append(str(value))
                        
                f.write(','.join(line_data) + '\n')
                
    async def _generate_hdr_file(self, hdr_path: Path, event_data: Dict[str, Any],
                                device_config: Dict[str, Any]):
        """Generate COMTRADE header file with event information"""
        
        header_content = [
            f"Station: {device_config.get('station_name', 'SUBSTATION')}",
            f"Device: {device_config['device_id']}",
            f"Event Type: {event_data.get('event_type', 'FAULT')}",
            f"Event Time: {event_data['timestamp']}",
            f"Severity: {event_data.get('severity', 1.0)}",
            f"Duration: {event_data.get('duration', 0.0)} seconds",
            f"Generated by: OpenMIC Simulator",
            f"Generation Time: {datetime.utcnow().isoformat()}"
        ]
        
        with open(hdr_path, 'w') as f:
            f.write('\n'.join(header_content))

class PQDIFGenerator:
    """
    IEEE 1159.3 PQDIF file generator
    Creates power quality disturbance files for power quality events
    """
    
    def __init__(self, output_directory: str = "pqdif_files"):
        self.logger = logging.getLogger("PQDIFGenerator")
        self.output_dir = Path(output_directory)
        self.output_dir.mkdir(exist_ok=True)
        
    async def generate_power_quality_file(self, event_data: Dict[str, Any],
                                        measurements: Dict[str, Any],
                                        device_config: Dict[str, Any]) -> str:
        """Generate PQDIF file for power quality event"""
        
        # Create unique filename
        timestamp = datetime.fromisoformat(event_data["timestamp"])
        filename = f"{device_config['device_id']}_PQ_{timestamp.strftime('%Y%m%d_%H%M%S')}.pqd"
        file_path = self.output_dir / filename
        
        # Generate PQDIF file (simplified binary format)
        await self._generate_pqdif_binary(file_path, event_data, measurements, device_config)
        
        self.logger.info(f"Generated PQDIF file: {filename}")
        return filename
        
    async def _generate_pqdif_binary(self, file_path: Path, event_data: Dict[str, Any],
                                   measurements: Dict[str, Any], device_config: Dict[str, Any]):
        """Generate binary PQDIF file"""
        
        with open(file_path, 'wb') as f:
            # PQDIF Header
            await self._write_pqdif_header(f, device_config)
            
            # Container Record
            await self._write_container_record(f, event_data, device_config)
            
            # Monitor Settings Record
            await self._write_monitor_settings(f, device_config)
            
            # Observation Record
            await self._write_observation_record(f, event_data, measurements)
            
    async def _write_pqdif_header(self, f, device_config: Dict[str, Any]):
        """Write PQDIF file header"""
        # Simplified PQDIF header structure
        header = struct.pack('<4s', b'PQDIF')  # Signature
        header += struct.pack('<I', 1)  # Version
        header += struct.pack('<I', 0)  # Reserved
        f.write(header)
        
    async def _write_container_record(self, f, event_data: Dict[str, Any], device_config: Dict[str, Any]):
        """Write container record with file information"""
        # Record header
        record_type = 1  # Container record
        record_size = 256
        
        f.write(struct.pack('<II', record_type, record_size))
        
        # Container information
        creation_time = datetime.utcnow()
        f.write(struct.pack('<Q', int(creation_time.timestamp() * 1000000)))  # Microseconds since epoch
        
        # Device information
        device_name = device_config['device_id'].encode('utf-8')[:32]
        f.write(device_name.ljust(32, b'\x00'))
        
        # Vendor information
        vendor_name = device_config.get('manufacturer', 'OpenMIC Simulator').encode('utf-8')[:32]
        f.write(vendor_name.ljust(32, b'\x00'))
        
        # Model information
        model_name = device_config.get('model', 'SIM-1000').encode('utf-8')[:32]
        f.write(model_name.ljust(32, b'\x00'))
        
        # Pad to record size
        remaining = record_size - 8 - 8 - 32 - 32 - 32
        f.write(b'\x00' * remaining)
        
    async def _write_monitor_settings(self, f, device_config: Dict[str, Any]):
        """Write monitor settings record"""
        record_type = 2  # Monitor settings
        record_size = 128
        
        f.write(struct.pack('<II', record_type, record_size))
        
        # Sampling rate
        sampling_rate = device_config.get('sampling_rate', 4000)
        f.write(struct.pack('<I', sampling_rate))
        
        # Nominal frequency
        nominal_freq = device_config.get('nominal_frequency', 60.0)
        f.write(struct.pack('<f', nominal_freq))
        
        # Nominal voltage
        nominal_voltage = device_config.get('nominal_voltage', 120.0)
        f.write(struct.pack('<f', nominal_voltage))
        
        # Pad to record size
        remaining = record_size - 8 - 4 - 4 - 4
        f.write(b'\x00' * remaining)
        
    async def _write_observation_record(self, f, event_data: Dict[str, Any], measurements: Dict[str, Any]):
        """Write observation record with measurement data"""
        record_type = 3  # Observation record
        
        # Calculate record size based on measurements
        base_size = 64
        measurement_data_size = len(measurements) * 8  # 8 bytes per measurement
        record_size = base_size + measurement_data_size
        
        f.write(struct.pack('<II', record_type, record_size))
        
        # Event timestamp
        event_time = datetime.fromisoformat(event_data["timestamp"])
        f.write(struct.pack('<Q', int(event_time.timestamp() * 1000000)))
        
        # Event type
        event_type_code = self._get_pqdif_event_type(event_data.get("event_type", "unknown"))
        f.write(struct.pack('<I', event_type_code))
        
        # Event severity
        severity = event_data.get("severity", 1.0)
        f.write(struct.pack('<f', severity))
        
        # Duration
        duration = event_data.get("duration", 0.0)
        f.write(struct.pack('<f', duration))
        
        # Number of measurements
        f.write(struct.pack('<I', len(measurements)))
        
        # Pad base record
        remaining_base = base_size - 8 - 8 - 4 - 4 - 4 - 4
        f.write(b'\x00' * remaining_base)
        
        # Write measurement data
        for name, value in measurements.items():
            if isinstance(value, (int, float)):
                f.write(struct.pack('<f', float(value)))
                f.write(struct.pack('<I', hash(name) & 0xFFFFFFFF))  # Simple name hash
                
    def _get_pqdif_event_type(self, event_type: str) -> int:
        """Map event type to PQDIF event type code"""
        event_type_map = {
            "voltage_sag": 1,
            "voltage_swell": 2,
            "voltage_interruption": 3,
            "frequency_deviation": 4,
            "harmonic_distortion": 5,
            "transient": 6,
            "flicker": 7,
            "imbalance": 8,
            "unknown": 0
        }
        return event_type_map.get(event_type.lower(), 0)

class FileCoordinator:
    """
    Coordinates file generation across multiple devices and events
    Manages file storage, cleanup, and notification
    """
    
    def __init__(self, base_output_dir: str = "simulation_files"):
        self.logger = logging.getLogger("FileCoordinator")
        self.base_dir = Path(base_output_dir)
        self.base_dir.mkdir(exist_ok=True)
        
        # Initialize generators
        self.comtrade_gen = COMTRADEGenerator(str(self.base_dir / "comtrade"))
        self.pqdif_gen = PQDIFGenerator(str(self.base_dir / "pqdif"))
        
        # File tracking
        self.generated_files = []
        self.file_handlers = []
        
    async def generate_event_files(self, event_data: Dict[str, Any], 
                                 device_config: Dict[str, Any],
                                 measurements: Dict[str, Any],
                                 waveform_data: Optional[Dict[str, np.ndarray]] = None):
        """Generate appropriate files based on event type and device"""
        
        generated_files = []
        event_type = event_data.get("event_type", "unknown")
        device_type = device_config.get("device_type", "unknown")
        
        try:
            # Generate COMTRADE files for fault events on DFRs and relays
            if event_type in ["line_fault", "transformer_trip"] and device_type in ["digital_fault_recorder", "protective_relay"]:
                if waveform_data:
                    filename = await self.comtrade_gen.generate_fault_recording(event_data, waveform_data, device_config)
                    generated_files.append({"type": "COMTRADE", "filename": filename, "path": self.comtrade_gen.output_dir})
                    
            # Generate PQDIF files for power quality events on meters
            if event_type in ["voltage_sag", "harmonic_distortion", "frequency_deviation"] and device_type in ["power_meter"]:
                filename = await self.pqdif_gen.generate_power_quality_file(event_data, measurements, device_config)
                generated_files.append({"type": "PQDIF", "filename": filename, "path": self.pqdif_gen.output_dir})
                
            # Track generated files
            self.generated_files.extend(generated_files)
            
            # Notify handlers
            for handler in self.file_handlers:
                for file_info in generated_files:
                    try:
                        await handler(file_info)
                    except Exception as e:
                        self.logger.error(f"File handler error: {e}")
                        
            if generated_files:
                self.logger.info(f"Generated {len(generated_files)} files for {event_type} event")
                
        except Exception as e:
            self.logger.error(f"File generation error: {e}")
            
        return generated_files
        
    def add_file_handler(self, handler):
        """Add a file generation handler"""
        self.file_handlers.append(handler)
        
    def get_generated_files(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """Get list of generated files"""
        if limit:
            return self.generated_files[-limit:]
        return self.generated_files.copy()
        
    async def cleanup_old_files(self, max_age_hours: int = 24):
        """Clean up old generated files"""
        cutoff_time = datetime.utcnow() - timedelta(hours=max_age_hours)
        cleaned_count = 0
        
        try:
            # Clean COMTRADE files
            for file_path in self.comtrade_gen.output_dir.glob("*"):
                if file_path.stat().st_mtime < cutoff_time.timestamp():
                    file_path.unlink()
                    cleaned_count += 1
                    
            # Clean PQDIF files
            for file_path in self.pqdif_gen.output_dir.glob("*"):
                if file_path.stat().st_mtime < cutoff_time.timestamp():
                    file_path.unlink()
                    cleaned_count += 1
                    
            if cleaned_count > 0:
                self.logger.info(f"Cleaned up {cleaned_count} old files")
                
        except Exception as e:
            self.logger.error(f"File cleanup error: {e}")
            
        return cleaned_count
