"""
Power System Device Simulator - Core simulation engine for power system devices
Implements realistic power system behavior with IEEE standards compliance
"""

import asyncio
import numpy as np
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum
import random
import json
import math

class DeviceType(Enum):
    DFR = "digital_fault_recorder"
    METER = "power_meter"
    RELAY = "protective_relay"
    PMU = "phasor_measurement_unit"

class EventType(Enum):
    LINE_FAULT = "line_fault"
    TRANSFORMER_TRIP = "transformer_trip"
    CAPACITOR_SWITCH = "capacitor_switch"
    VOLTAGE_SAG = "voltage_sag"
    FREQUENCY_DEVIATION = "frequency_deviation"
    HARMONIC_DISTORTION = "harmonic_distortion"
    POWER_SWING = "power_swing"

@dataclass
class DeviceConfig:
    """Configuration for simulated power system device"""
    device_id: str
    device_type: DeviceType
    station_name: str
    nominal_voltage: float = 120.0  # Volts RMS
    nominal_current: float = 5.0    # Amps RMS
    nominal_frequency: float = 60.0  # Hz
    sampling_rate: int = 4000       # Hz
    channels: int = 8               # Number of analog channels
    update_rate: float = 1.0        # Measurements per second
    manufacturer: str = "Generic"
    model: str = "SIM-1000"
    firmware_version: str = "1.0.0"
    communication_protocols: List[str] = field(default_factory=lambda: ["DNP3", "Modbus"])
    health_check_interval: float = 30.0  # seconds
    
@dataclass
class PowerSystemState:
    """Current state of the power system"""
    voltage_magnitude: List[float] = field(default_factory=lambda: [120.0, 120.0, 120.0])
    voltage_angle: List[float] = field(default_factory=lambda: [0.0, -120.0, 120.0])
    current_magnitude: List[float] = field(default_factory=lambda: [5.0, 5.0, 5.0])
    current_angle: List[float] = field(default_factory=lambda: [-30.0, -150.0, 90.0])
    frequency: float = 60.0
    power_factor: float = 0.95
    total_harmonic_distortion: float = 2.0  # Percent
    active_power: float = 1800.0  # Watts
    reactive_power: float = 600.0  # VAR
    apparent_power: float = 1897.4  # VA
    
class PowerSystemSimulator:
    """
    Comprehensive power system device simulator with IEEE standards compliance
    Supports DFRs, meters, relays, and PMUs with realistic behavior
    """
    
    def __init__(self, config: DeviceConfig):
        self.config = config
        self.logger = logging.getLogger(f"PowerSimulator.{config.device_id}")
        
        # Simulation state
        self.running = False
        self.system_state = PowerSystemState()
        self.current_event: Optional[Dict[str, Any]] = None
        self.device_health = 100.0  # Percentage
        self.communication_status = True
        
        # Performance metrics
        self.metrics = {
            "measurements_generated": 0,
            "events_triggered": 0,
            "files_created": 0,
            "uptime_seconds": 0.0,
            "last_update": datetime.utcnow()
        }
        
        # Event handlers
        self.event_handlers = []
        self.measurement_handlers = []
        
        # Waveform generation parameters
        self.time_base = 0.0
        self.noise_level = 0.01  # 1% noise
        self.harmonic_content = self._initialize_harmonics()
        
        self.logger.info(f"Initialized {config.device_type.value} simulator: {config.device_id}")
        
    def _initialize_harmonics(self) -> Dict[int, float]:
        """Initialize harmonic content for realistic waveforms"""
        return {
            3: 0.02,   # 2% 3rd harmonic
            5: 0.015,  # 1.5% 5th harmonic
            7: 0.01,   # 1% 7th harmonic
            11: 0.005, # 0.5% 11th harmonic
            13: 0.003  # 0.3% 13th harmonic
        }
        
    async def start_simulation(self):
        """Start the power system device simulation"""
        if self.running:
            self.logger.warning("Simulation already running")
            return
            
        self.running = True
        self.metrics["last_update"] = datetime.utcnow()
        
        self.logger.info(f"Starting {self.config.device_type.value} simulation")
        
        # Start concurrent simulation tasks
        tasks = [
            asyncio.create_task(self._generate_measurements()),
            asyncio.create_task(self._monitor_device_health()),
            asyncio.create_task(self._handle_events()),
            asyncio.create_task(self._update_metrics())
        ]
        
        # Add device-specific tasks
        if self.config.device_type == DeviceType.DFR:
            tasks.append(asyncio.create_task(self._dfr_waveform_capture()))
        elif self.config.device_type == DeviceType.PMU:
            tasks.append(asyncio.create_task(self._pmu_synchrophasor_stream()))
        elif self.config.device_type == DeviceType.RELAY:
            tasks.append(asyncio.create_task(self._relay_protection_logic()))
            
        try:
            await asyncio.gather(*tasks)
        except asyncio.CancelledError:
            self.logger.info("Simulation tasks cancelled")
        except Exception as e:
            self.logger.error(f"Simulation error: {e}")
        finally:
            self.running = False
            
    async def stop_simulation(self):
        """Stop the power system device simulation"""
        self.running = False
        self.logger.info("Stopping simulation")
        
    async def _generate_measurements(self):
        """Generate realistic power system measurements"""
        while self.running:
            try:
                timestamp = datetime.utcnow()
                
                # Generate measurements based on device type
                measurements = await self._create_measurements(timestamp)
                
                # Apply current event effects
                if self.current_event:
                    measurements = self._apply_event_effects(measurements)
                    
                # Add noise and variations
                measurements = self._add_realistic_variations(measurements)
                
                # Notify handlers
                for handler in self.measurement_handlers:
                    try:
                        await handler(measurements)
                    except Exception as e:
                        self.logger.error(f"Measurement handler error: {e}")
                        
                self.metrics["measurements_generated"] += 1
                
                # Wait for next measurement cycle
                await asyncio.sleep(1.0 / self.config.update_rate)
                
            except Exception as e:
                self.logger.error(f"Measurement generation error: {e}")
                await asyncio.sleep(1.0)
                
    async def _create_measurements(self, timestamp: datetime) -> Dict[str, Any]:
        """Create device-specific measurements"""
        base_measurements = {
            "device_id": self.config.device_id,
            "timestamp": timestamp.isoformat(),
            "device_type": self.config.device_type.value,
            "quality": "GOOD" if self.communication_status else "COMM_FAIL"
        }
        
        if self.config.device_type == DeviceType.METER:
            return {**base_measurements, **self._generate_meter_measurements()}
        elif self.config.device_type == DeviceType.DFR:
            return {**base_measurements, **self._generate_dfr_measurements()}
        elif self.config.device_type == DeviceType.RELAY:
            return {**base_measurements, **self._generate_relay_measurements()}
        elif self.config.device_type == DeviceType.PMU:
            return {**base_measurements, **self._generate_pmu_measurements()}
        else:
            return base_measurements
            
    def _generate_meter_measurements(self) -> Dict[str, Any]:
        """Generate power meter measurements"""
        return {
            "voltage_a": self.system_state.voltage_magnitude[0],
            "voltage_b": self.system_state.voltage_magnitude[1], 
            "voltage_c": self.system_state.voltage_magnitude[2],
            "current_a": self.system_state.current_magnitude[0],
            "current_b": self.system_state.current_magnitude[1],
            "current_c": self.system_state.current_magnitude[2],
            "frequency": self.system_state.frequency,
            "active_power": self.system_state.active_power,
            "reactive_power": self.system_state.reactive_power,
            "apparent_power": self.system_state.apparent_power,
            "power_factor": self.system_state.power_factor,
            "thd_voltage": self.system_state.total_harmonic_distortion,
            "energy_delivered": self.metrics["measurements_generated"] * 0.5  # kWh
        }
        
    def _generate_dfr_measurements(self) -> Dict[str, Any]:
        """Generate Digital Fault Recorder measurements"""
        return {
            "trigger_status": "ARMED" if not self.current_event else "TRIGGERED",
            "memory_usage": min(95.0, 10.0 + (self.metrics["measurements_generated"] * 0.001)),
            "pre_trigger_samples": 960,  # 16 cycles at 60 Hz
            "post_trigger_samples": 1920,  # 32 cycles at 60 Hz
            "sampling_rate": self.config.sampling_rate,
            "channels_active": self.config.channels,
            "last_trigger_time": self.current_event["timestamp"] if self.current_event else None
        }
        
    def _generate_relay_measurements(self) -> Dict[str, Any]:
        """Generate protective relay measurements"""
        # Calculate protection elements
        voltage_unbalance = self._calculate_voltage_unbalance()
        current_unbalance = self._calculate_current_unbalance()
        
        return {
            "protection_status": "NORMAL" if not self.current_event else "ALARM",
            "trip_status": False,  # Would be True during fault
            "voltage_unbalance": voltage_unbalance,
            "current_unbalance": current_unbalance,
            "overcurrent_pickup": 6.0,  # Amps
            "overvoltage_pickup": 132.0,  # Volts
            "undervoltage_pickup": 108.0,  # Volts
            "frequency_high_limit": 60.5,  # Hz
            "frequency_low_limit": 59.5,   # Hz
            "last_trip_time": None
        }
        
    def _generate_pmu_measurements(self) -> Dict[str, Any]:
        """Generate Phasor Measurement Unit measurements (IEEE C37.118)"""
        return {
            "voltage_phasors": [
                {"magnitude": self.system_state.voltage_magnitude[i], 
                 "angle": self.system_state.voltage_angle[i]} 
                for i in range(3)
            ],
            "current_phasors": [
                {"magnitude": self.system_state.current_magnitude[i],
                 "angle": self.system_state.current_angle[i]}
                for i in range(3)
            ],
            "frequency": self.system_state.frequency,
            "rocof": 0.0,  # Rate of Change of Frequency
            "sync_status": "LOCKED",
            "time_quality": "GOOD",
            "pmu_id": int(self.config.device_id.split('_')[-1]) if '_' in self.config.device_id else 1
        }

    def _calculate_voltage_unbalance(self) -> float:
        """Calculate voltage unbalance percentage"""
        voltages = self.system_state.voltage_magnitude
        avg_voltage = sum(voltages) / len(voltages)
        max_deviation = max(abs(v - avg_voltage) for v in voltages)
        return (max_deviation / avg_voltage) * 100.0 if avg_voltage > 0 else 0.0

    def _calculate_current_unbalance(self) -> float:
        """Calculate current unbalance percentage"""
        currents = self.system_state.current_magnitude
        avg_current = sum(currents) / len(currents)
        max_deviation = max(abs(i - avg_current) for i in currents)
        return (max_deviation / avg_current) * 100.0 if avg_current > 0 else 0.0

    def _apply_event_effects(self, measurements: Dict[str, Any]) -> Dict[str, Any]:
        """Apply current event effects to measurements"""
        if not self.current_event:
            return measurements

        event_type = EventType(self.current_event["type"])
        severity = self.current_event.get("severity", 1.0)

        if event_type == EventType.LINE_FAULT:
            # Simulate line fault effects
            measurements = self._apply_line_fault_effects(measurements, severity)
        elif event_type == EventType.VOLTAGE_SAG:
            # Simulate voltage sag
            measurements = self._apply_voltage_sag_effects(measurements, severity)
        elif event_type == EventType.FREQUENCY_DEVIATION:
            # Simulate frequency deviation
            measurements = self._apply_frequency_deviation_effects(measurements, severity)
        elif event_type == EventType.HARMONIC_DISTORTION:
            # Simulate harmonic distortion
            measurements = self._apply_harmonic_distortion_effects(measurements, severity)

        return measurements

    def _apply_line_fault_effects(self, measurements: Dict[str, Any], severity: float) -> Dict[str, Any]:
        """Apply line fault effects to measurements"""
        # Increase current dramatically, reduce voltage
        fault_current_multiplier = 2.0 + (severity * 8.0)  # 2x to 10x normal
        voltage_reduction = 0.3 + (severity * 0.6)  # 30% to 90% reduction

        if "current_a" in measurements:
            measurements["current_a"] *= fault_current_multiplier
            measurements["voltage_a"] *= (1.0 - voltage_reduction)

        if "current_phasors" in measurements:
            for phasor in measurements["current_phasors"]:
                phasor["magnitude"] *= fault_current_multiplier

        if "voltage_phasors" in measurements:
            for phasor in measurements["voltage_phasors"]:
                phasor["magnitude"] *= (1.0 - voltage_reduction)

        return measurements

    def _apply_voltage_sag_effects(self, measurements: Dict[str, Any], severity: float) -> Dict[str, Any]:
        """Apply voltage sag effects to measurements"""
        sag_factor = 1.0 - (0.1 + severity * 0.4)  # 10% to 50% sag

        for key in ["voltage_a", "voltage_b", "voltage_c"]:
            if key in measurements:
                measurements[key] *= sag_factor

        if "voltage_phasors" in measurements:
            for phasor in measurements["voltage_phasors"]:
                phasor["magnitude"] *= sag_factor

        return measurements

    def _apply_frequency_deviation_effects(self, measurements: Dict[str, Any], severity: float) -> Dict[str, Any]:
        """Apply frequency deviation effects to measurements"""
        deviation = (random.random() - 0.5) * severity * 2.0  # ±severity Hz

        if "frequency" in measurements:
            measurements["frequency"] = max(55.0, min(65.0, measurements["frequency"] + deviation))

        return measurements

    def _apply_harmonic_distortion_effects(self, measurements: Dict[str, Any], severity: float) -> Dict[str, Any]:
        """Apply harmonic distortion effects to measurements"""
        thd_increase = severity * 10.0  # Up to 10% additional THD

        if "thd_voltage" in measurements:
            measurements["thd_voltage"] += thd_increase

        return measurements

    def _add_realistic_variations(self, measurements: Dict[str, Any]) -> Dict[str, Any]:
        """Add realistic noise and variations to measurements"""
        for key, value in measurements.items():
            if isinstance(value, (int, float)) and key not in ["timestamp", "device_id"]:
                # Add small random variations (±0.5%)
                variation = value * (random.random() - 0.5) * 0.01
                measurements[key] = value + variation

        return measurements

    async def _monitor_device_health(self):
        """Monitor and simulate device health"""
        while self.running:
            try:
                # Simulate gradual health degradation
                if random.random() < 0.001:  # 0.1% chance per check
                    self.device_health = max(0.0, self.device_health - random.uniform(1.0, 5.0))

                # Simulate communication failures
                if self.device_health < 50.0:
                    self.communication_status = random.random() > 0.1  # 10% failure rate
                else:
                    self.communication_status = random.random() > 0.001  # 0.1% failure rate

                # Log health issues
                if self.device_health < 80.0:
                    self.logger.warning(f"Device health degraded: {self.device_health:.1f}%")

                if not self.communication_status:
                    self.logger.warning("Communication failure simulated")

                await asyncio.sleep(self.config.health_check_interval)

            except Exception as e:
                self.logger.error(f"Health monitoring error: {e}")
                await asyncio.sleep(10.0)

    async def _handle_events(self):
        """Handle event processing and lifecycle"""
        while self.running:
            try:
                # Check if current event should end
                if self.current_event:
                    event_duration = (datetime.utcnow() -
                                    datetime.fromisoformat(self.current_event["start_time"])).total_seconds()

                    if event_duration >= self.current_event["duration"]:
                        await self._end_event()

                await asyncio.sleep(0.1)  # Check every 100ms

            except Exception as e:
                self.logger.error(f"Event handling error: {e}")
                await asyncio.sleep(1.0)

    async def _update_metrics(self):
        """Update performance metrics"""
        while self.running:
            try:
                current_time = datetime.utcnow()
                self.metrics["uptime_seconds"] = (current_time - self.metrics["last_update"]).total_seconds()

                await asyncio.sleep(60.0)  # Update every minute

            except Exception as e:
                self.logger.error(f"Metrics update error: {e}")
                await asyncio.sleep(60.0)

    async def trigger_event(self, event_type: EventType, severity: float = 1.0,
                          duration: float = 5.0, metadata: Optional[Dict] = None):
        """Trigger a power system event"""
        if self.current_event:
            self.logger.warning(f"Event already active: {self.current_event['type']}")
            return

        event_data = {
            "type": event_type.value,
            "severity": severity,
            "duration": duration,
            "start_time": datetime.utcnow().isoformat(),
            "metadata": metadata or {}
        }

        self.current_event = event_data
        self.metrics["events_triggered"] += 1

        self.logger.info(f"Triggered {event_type.value} event (severity: {severity}, duration: {duration}s)")

        # Notify event handlers
        for handler in self.event_handlers:
            try:
                await handler(event_data)
            except Exception as e:
                self.logger.error(f"Event handler error: {e}")

    async def _end_event(self):
        """End the current event"""
        if not self.current_event:
            return

        event_type = self.current_event["type"]
        self.logger.info(f"Ending {event_type} event")

        self.current_event = None

        # Reset system state to normal
        self.system_state = PowerSystemState()

    def add_measurement_handler(self, handler):
        """Add a measurement data handler"""
        self.measurement_handlers.append(handler)

    def add_event_handler(self, handler):
        """Add an event handler"""
        self.event_handlers.append(handler)

    def get_metrics(self) -> Dict[str, Any]:
        """Get current performance metrics"""
        return {
            **self.metrics,
            "device_health": self.device_health,
            "communication_status": self.communication_status,
            "current_event": self.current_event,
            "system_state": {
                "voltage_avg": sum(self.system_state.voltage_magnitude) / 3,
                "current_avg": sum(self.system_state.current_magnitude) / 3,
                "frequency": self.system_state.frequency,
                "power_factor": self.system_state.power_factor
            }
        }

    async def _dfr_waveform_capture(self):
        """DFR-specific waveform capture simulation"""
        while self.running:
            try:
                if self.current_event and self.config.device_type == DeviceType.DFR:
                    # Simulate high-speed waveform capture
                    await self._capture_fault_waveforms()

                await asyncio.sleep(0.1)

            except Exception as e:
                self.logger.error(f"DFR waveform capture error: {e}")
                await asyncio.sleep(1.0)

    async def _pmu_synchrophasor_stream(self):
        """PMU-specific synchrophasor streaming"""
        while self.running:
            try:
                if self.config.device_type == DeviceType.PMU:
                    # Generate high-rate synchrophasor data
                    phasor_data = self._generate_synchrophasor_frame()

                    # Stream at PMU reporting rate (typically 30-60 fps)
                    for handler in self.measurement_handlers:
                        await handler(phasor_data)

                await asyncio.sleep(1.0 / 30.0)  # 30 fps

            except Exception as e:
                self.logger.error(f"PMU synchrophasor streaming error: {e}")
                await asyncio.sleep(1.0)

    async def _relay_protection_logic(self):
        """Relay-specific protection logic simulation"""
        while self.running:
            try:
                if self.config.device_type == DeviceType.RELAY:
                    # Evaluate protection elements
                    await self._evaluate_protection_elements()

                await asyncio.sleep(0.05)  # 20ms protection evaluation cycle

            except Exception as e:
                self.logger.error(f"Relay protection logic error: {e}")
                await asyncio.sleep(1.0)
