category,id,message,name,severity,source,timeStamp
Start Stop,1,Starting GTWEngine: pid=3332,,Information,Gateway,09/17/2025_14:17:39.998
Start Stop,2,Validate gtw_config.json Success,,Information,Gateway,09/17/2025_14:17:41.008
HTTPS,3,400 Bad Request:Engine is not ready,,E<PERSON>r,Gateway,09/17/2025_14:17:42.071
HTTPS,4,400 Bad Request:Engine is not ready,,Error,Gateway,09/17/2025_14:17:47.093
HTTPS,5,400 Bad Request:Engine is not ready,,E<PERSON>r,Gateway,09/17/2025_14:17:52.106
HTTPS,6,400 Bad Request:Engine is not ready,,Error,Gateway,09/17/2025_14:17:57.121
HTTPS,7,400 Bad Request:Engine is not ready,,Error,Gateway,09/17/2025_14:18:02.137
HTTPS,8,400 Bad Request:Engine is not ready,,E<PERSON>r,Gateway,09/17/2025_14:18:07.153
HTTPS,9,400 Bad Request:Engine is not ready,,Error,Gateway,09/17/2025_14:18:12.169
HTTPS,10,400 Bad Request:Engine is not ready,,<PERSON><PERSON><PERSON>,Gateway,09/17/2025_14:18:17.184
HTTPS,11,400 Bad Request:Engine is not ready,,Error,Gateway,09/17/2025_14:18:22.199
HTTPS,12,400 Bad Request:Engine is not ready,,Error,Gateway,09/17/2025_14:18:27.214
HTTPS,13,400 Bad Request:Engine is not ready,,Error,Gateway,09/17/2025_14:18:32.231
HTTPS,14,400 Bad Request:Engine is not ready,,Error,Gateway,09/17/2025_14:18:37.246
HTTPS,15,400 Bad Request:Engine is not ready,,Error,Gateway,09/17/2025_14:18:42.256
HTTPS,16,400 Bad Request:Engine is not ready,,Error,Gateway,09/17/2025_14:18:47.291
HTTPS,17,400 Bad Request:Engine is not ready,,Error,Gateway,09/17/2025_14:18:52.310
HTTPS,18,400 Bad Request:Engine is not ready,,Error,Gateway,09/17/2025_14:18:57.324
HTTPS,19,400 Bad Request:Engine is not ready,,Error,Gateway,09/17/2025_14:19:02.339
Start Stop,20,SDG starting,,Information,Gateway,09/17/2025_14:19:06.655
Start Stop,21,Loading INI file: C:\ProgramData\Triangle MicroWorks\SCADADataGateway\WorkSpaces\tmwgtway_sdnp_to_mdnp\tmwgtway_sdnp_to_mdnp.ini,,Information,Gateway,09/17/2025_14:19:06.655
Start Stop,22,Loading INI file: C:\ProgramData\Triangle MicroWorks\SCADADataGateway\WorkSpaces\tmwgtway_sdnp_to_mdnp\tmwgtway_sdnp_to_mdnp.ini,,Information,Gateway,09/17/2025_14:19:06.740
Start Stop,23,Requested Display TimeZone: 'UTC',,Information,Gateway,09/17/2025_14:19:06.987
Start Stop,24,Using timezone UTC,,Information,Gateway,09/17/2025_14:19:06.987
License,25,SDO Point Count Limit: unlimited,,Information,Gateway,09/17/2025_14:19:06.988
Start Stop,26,Initializing,,Information,Gateway,09/17/2025_14:19:06.988
License,27,Licensed components ...  M101: yes M101 SA: yes M102: yes M103: yes M104: yes M104 SA: yes S101: yes S101 SA: yes S102: yes S103: yes S104: yes S104 SA: yes MDNP: yes MDNP SAV5: yes SDNP: yes SDNP SAV5: yes MMB: yes SMB: yes ODBC CLT: yes 61850 CLT: yes 61850 SVR: yes OPC DA SVR: yes OPC DA CLT: yes OPC XML DA SVR: yes OPC XML DA CLT: yes OPC AE SVR: yes OPC AE CLT: yes  TASE.2 SVR: yes TASE.2 CLT: yes OPC UA SVR: yes OPC UA CLT: yes ,,Information,Gateway,09/17/2025_14:19:06.988
OPC UA,28,Adding OPC UA Server.,,Information,Gateway,09/17/2025_14:19:06.989
OPC UA,29,OPC UA Server,,Information,Gateway,09/17/2025_14:19:06.999
HTTPS,30,400 Bad Request:Engine is not ready,,Error,Gateway,09/17/2025_14:19:07.355
OPC UA,31,OPC UA Server,,Information,Gateway,09/17/2025_14:19:07.561
OPC UA,32,OPC UA Server,,Warning,Gateway,09/17/2025_14:19:07.563
OPC,33,Adding OPC Classic Server.,,Information,Gateway,09/17/2025_14:19:07.563
Start Stop,34,Creating Ports and Sessions.,,Information,Gateway,09/17/2025_14:19:07.563
Start Stop,35,Creating ICCP Servers...,,Information,Gateway,09/17/2025_14:19:07.564
ICCP,36,No ICCP Servers configured,,Information,Gateway,09/17/2025_14:19:07.564
61850,37,No 61850 Servers configured,,Information,Gateway,09/17/2025_14:19:07.564
Start Stop,38,Creating channels  sessions  and sectors...,,Information,Gateway,09/17/2025_14:19:07.564
Start Stop,39,Creating Session 0,,Information,Gateway,09/17/2025_14:19:07.565
General,40,sdnp,,Information,Gateway,09/17/2025_14:19:07.565
Start Stop,41,Success creating session: sdnp.L3,,Information,Gateway,09/17/2025_14:19:07.566
Start Stop,42,Creating Session 1,,Information,Gateway,09/17/2025_14:19:07.566
General,43,mdnp,,Information,Gateway,09/17/2025_14:19:07.566
Start Stop,44,Success creating session: mdnp.L4,,Information,Gateway,09/17/2025_14:19:07.567
61850,45,Connecting 61850 Clients to 61850 Servers.,,Information,Gateway,09/17/2025_14:19:07.567
61850,46,No 61850 Clients configured,,Information,Gateway,09/17/2025_14:19:07.567
ICCP,47,Creating TASE2 Clients.,,Information,Gateway,09/17/2025_14:19:07.568
ICCP,48,No ICCP/Tase2 Clients configured.,,Information,Gateway,09/17/2025_14:19:07.568
OPC,49,No GOOSE Monitors configured,,Information,Gateway,09/17/2025_14:19:07.568
ODBC,50,Connecting ODBC Clients to ODBC Servers...,,Information,Gateway,09/17/2025_14:19:07.568
General,51,Set GatewayOnline MDO true,,Information,Gateway,09/17/2025_14:19:07.572
Start Stop,52,Using Point Map CSV File: tmwgtway_sdnp_to_mdnp.csv,,Information,Gateway,09/17/2025_14:19:07.572
Start Stop,53,Adding SDG OPC Server...,OPC Classic Server,Information,Gateway,09/17/2025_14:19:07.574
Start Stop,54,OPC Classic Server started,OPC Classic Server,Information,Gateway,09/17/2025_14:19:07.574
Point Map,55,Loading Point Map.,,Information,Gateway,09/17/2025_14:19:07.575
Point Map,56,Processing point map configuration from 'C:\ProgramData\Triangle MicroWorks\SCADADataGateway\WorkSpaces\tmwgtway_sdnp_to_mdnp\tmwgtway_sdnp_to_mdnp.csv',,Information,Gateway,09/17/2025_14:19:07.576
Point Map,57,csv line: 1:,,Information,Gateway,09/17/2025_14:19:07.576
OPC DEEP,58,IsOpcReady FALSE,,Information,Gateway,09/17/2025_14:19:07.577
OPC DEEP,59,IsOpcReady FALSE,,Information,Gateway,09/17/2025_14:19:07.577
OPC DEEP,60,IsOpcReady FALSE,,Information,Gateway,09/17/2025_14:19:07.578
OPC DEEP,61,IsOpcReady FALSE,,Information,Gateway,09/17/2025_14:19:07.578
OPC DEEP,62,IsOpcReady FALSE,,Information,Gateway,09/17/2025_14:19:07.578
OPC DEEP,63,IsOpcReady FALSE,,Information,Gateway,09/17/2025_14:19:07.579
OPC DEEP,64,IsOpcReady FALSE,,Information,Gateway,09/17/2025_14:19:07.579
OPC DEEP,65,IsOpcReady FALSE,,Information,Gateway,09/17/2025_14:19:07.579
OPC DEEP,66,IsOpcReady FALSE,,Information,Gateway,09/17/2025_14:19:07.580
OPC DEEP,67,IsOpcReady FALSE,,Information,Gateway,09/17/2025_14:19:07.580
Point Map,68,Finished processing point map configuration from 'C:\ProgramData\Triangle MicroWorks\SCADADataGateway\WorkSpaces\tmwgtway_sdnp_to_mdnp\tmwgtway_sdnp_to_mdnp.csv',,Information,Gateway,09/17/2025_14:19:07.580
General,69,Initialize SCL Channels.,,Information,Gateway,09/17/2025_14:19:07.580
Start Stop,70,sdnp,,Information,Gateway,09/17/2025_14:19:07.581
General,71,sdnp - 127.0.0.1:20000 - Created new TCP Channel,,Information,SCL,09/17/2025_14:19:07.584
DNP,72,sdnp - 127.0.0.1:20000 - TCP open,sdnp,Information,SCL,09/17/2025_14:19:07.585
DNP,73,sdnp - 127.0.0.1:20000 - TCP listen for a connection on port:,sdnp,Information,SCL,09/17/2025_14:19:07.585
DNP,74,sdnp - 127.0.0.1:20000 - TCP listen  no existing Listener found  creating one on port:,sdnp,Information,SCL,09/17/2025_14:19:07.585
DNP,75,sdnp - 127.0.0.1:20000 - TCP Listen  add this channel to the listener on port:,sdnp,Information,SCL,09/17/2025_14:19:07.586
DNP,76,sdnp - 127.0.0.1:20000 - TCP Add Channel to Listener  added channel to listener on port 20000,sdnp,Information,SCL,09/17/2025_14:19:07.586
DNP,77,sdnp - 127.0.0.1:20000 - TCP Add Channel to Listener  starting listening thread on port: 20000,sdnp,Information,SCL,09/17/2025_14:19:07.586
DNP,78,sdnp - 127.0.0.1:20000 - TCP Listen  successfully listening on port:,sdnp,Information,SCL,09/17/2025_14:19:07.586
General,79,TCP LISTENER enter listenThread,,Information,SCL,09/17/2025_14:19:07.587
Start Stop,80,mdnp,,Information,Gateway,09/17/2025_14:19:07.587
General,81,mdnp - 127.0.0.1:20000 - Created new TCP Channel,,Information,SCL,09/17/2025_14:19:07.591
DNP,82,<+++ mdnp       Build DNP3 Message: Integrity Poll Due to Master Restart ,mdnp,Information,SCL,09/17/2025_14:19:07.591
DNP,83,mdnp - 127.0.0.1:20000 - TCP open,mdnp,Information,SCL,09/17/2025_14:19:07.591
DNP,84,mdnp - 127.0.0.1:20000 - TCP Opening connection,mdnp,Information,SCL,09/17/2025_14:19:07.591
DNP,85,                Tx Object 60(Class Data)  variation 2  qualifier 0x06(All Points)  ,mdnp,Information,SCL,09/17/2025_14:19:07.591
DNP,86,mdnp - 127.0.0.1:20000 - TCP Connect success,mdnp,Information,SCL,09/17/2025_14:19:07.592
DNP,87,                Tx Object 60(Class Data)  variation 3  qualifier 0x06(All Points)  ,mdnp,Information,SCL,09/17/2025_14:19:07.592
DNP,88,                Tx Object 60(Class Data)  variation 4  qualifier 0x06(All Points)  ,mdnp,Information,SCL,09/17/2025_14:19:07.593
General,89,TCP LISTENER: accept incoming connection,,Information,SCL,09/17/2025_14:19:07.593
DNP,90,                Tx Object 60(Class Data)  variation 1  qualifier 0x06(All Points)  ,mdnp,Information,SCL,09/17/2025_14:19:07.593
DNP,91,sdnp - 127.0.0.1:20000 - TCP LISTENER: connection accepted ,sdnp,Information,SCL,09/17/2025_14:19:07.593
DNP,92,<+++ mdnp       Insert request in queue: Integrity Poll Due to Master Restart ,mdnp,Information,SCL,09/17/2025_14:19:07.593
General,93,TCP LISTENER: listenThread connection accepted from 127.0.0.1,,Information,SCL,09/17/2025_14:19:07.594
General,94,TCP LISTENER: listenThread Tell the channel we have a new connection from 127.0.0.1,,Information,SCL,09/17/2025_14:19:07.594
DNP,95,<=== mdnp       Application Header  Read Request ,mdnp,Information,SCL,09/17/2025_14:19:07.594
DNP,96,                FIR(1) FIN(1) CON(0) UNS(0) SEQ# 1 ,mdnp,Information,SCL,09/17/2025_14:19:07.595
DNP,97,                c1 01 3c 02 06 3c 03 06 3c 04 06 3c 01 06  ,mdnp,Information,SCL,09/17/2025_14:19:07.595
DNP,98,mdnp: opened Port: 127.0.0.1:20000 ,mdnp,Information,SCL,09/17/2025_14:19:07.595
DNP,99,<~~~ mdnp       Transport Header ,mdnp,Information,SCL,09/17/2025_14:19:07.595
DNP,100,                FIR(1) FIN(1) SEQ# 0 ,mdnp,Information,SCL,09/17/2025_14:19:07.595
DNP,101,                c0 c1 01 3c 02 06 3c 03 06 3c 04 06 3c 01 06  ,mdnp,Information,SCL,09/17/2025_14:19:07.596
DNP,102,<--- mdnp       Primary Frame - Unconfirmed User Data   ,mdnp,Information,SCL,09/17/2025_14:19:07.596
DNP,103,                LEN(20) DIR(1) PRM(1) FCV(0) FCB(0) DEST(4) SRC(3) ,mdnp,Information,SCL,09/17/2025_14:19:07.596
DNP,104,                05 64 14 c4 04 00 03 00 c7 17  ,mdnp,Information,SCL,09/17/2025_14:19:07.596
DNP,105,                c0 c1 01 3c 02 06 3c 03 06 3c 04 06 3c 01 06 7a  ,mdnp,Information,SCL,09/17/2025_14:19:07.596
DNP,106,                6f  ,mdnp,Information,SCL,09/17/2025_14:19:07.597
DNP,107,mdnp - 127.0.0.1:20000 - TCP transmit 27 bytes,mdnp,Information,SCL,09/17/2025_14:19:07.597
DNP,108,<... mdnp       05 64 14 c4 04 00 03 00 c7 17 c0 c1 01 3c 02 06  ,mdnp,Information,SCL,09/17/2025_14:19:07.597
DNP,109,                3c 03 06 3c 04 06 3c 01 06 7a 6f  ,mdnp,Information,SCL,09/17/2025_14:19:07.597
Start Stop,110,SCL Initialization Complete,,Information,Gateway,09/17/2025_14:19:07.598
Start Stop,111,Activating SCL Devices...,,Information,Gateway,09/17/2025_14:19:07.598
Start Stop,112,Make Sessions for Channel sdnp active,sdnp,Information,SCL,09/17/2025_14:19:07.598
OPC DEEP,113,IsOpcReady FALSE,,Information,Gateway,09/17/2025_14:19:07.598
Start Stop,114,Make Sessions for Channel mdnp active,mdnp,Information,SCL,09/17/2025_14:19:07.599
OPC DEEP,115,IsOpcReady FALSE,,Information,Gateway,09/17/2025_14:19:07.599
Start Stop,116,Finished activating SCL Devices...,,Information,Gateway,09/17/2025_14:19:07.599
OPC UA,117,OPC UA Server,,Information,Gateway,09/17/2025_14:19:07.599
OPC UA,118,OPC UA Server,,Information,Gateway,09/17/2025_14:19:07.601
OPC UA,119,OPC UA Server,,Information,Gateway,09/17/2025_14:19:07.610
Start Stop,120,SDG Initialization Complete ,,Information,Gateway,09/17/2025_14:19:07.610
Start Stop,121,SDG is running,,Information,Gateway,09/17/2025_14:19:07.611
Start Stop,122,Running: C:\ProgramData\Triangle MicroWorks\SCADADataGateway\WorkSpaces\tmwgtway_sdnp_to_mdnp\tmwgtway_sdnp_to_mdnp.ini,,Information,Gateway,09/17/2025_14:19:07.611
DNP,123,sdnp: opened Port: 127.0.0.1:20000 ,sdnp,Information,SCL,09/17/2025_14:19:07.611
DNP,124,<+++ sdnp.L3    Build DNP3 Message: Unsol NULL Response ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.612
DNP,125,<+++ sdnp.L3    Insert request in queue: Unsol NULL Response ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.612
DNP,126,<=== sdnp.L3    Application Header  Unsolicited ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.612
DNP,127,                FIR(1) FIN(1) CON(1) UNS(1) SEQ# 0 ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.612
DNP,128,                f0 82 90 00  ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.613
DNP,129,<~~~ sdnp.L3    Transport Header ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.613
DNP,130,                FIR(1) FIN(1) SEQ# 0 ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.613
DNP,131,                c0 f0 82 90 00  ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.613
DNP,132,<--- sdnp       Primary Frame - Unconfirmed User Data   ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.614
DNP,133,                LEN(10) DIR(0) PRM(1) FCV(0) FCB(0) DEST(3) SRC(4) ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.614
DNP,134,                05 64 0a 44 03 00 04 00 7c ae  ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.614
DNP,135,                c0 f0 82 90 00 43 a2  ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.614
DNP,136,sdnp - 127.0.0.1:20000 - TCP transmit 17 bytes,sdnp,Information,SCL,09/17/2025_14:19:07.614
DNP,137,<... sdnp       05 64 0a 44 03 00 04 00 7c ae c0 f0 82 90 00 43  ,sdnp,Information,SCL,09/17/2025_14:19:07.615
DNP,138,                a2  ,sdnp,Information,SCL,09/17/2025_14:19:07.615
OPC DEEP,139,Opc UA SDO updated for 'OPCUaServer.sdnp.NumTxBytes' :  Value(UInt32) = 17; quality=Good time=09-17-2025 14:19:07.615,,Information,Gateway,09/17/2025_14:19:07.615
OPC DEEP,140,Opc UA SDO updated for 'OPCUaServer.sdnp.NumTxFrames' :  Value(UInt32) = 1; quality=Good time=09-17-2025 14:19:07.616,,Information,Gateway,09/17/2025_14:19:07.616
OPC DEEP,141,Opc UA SDO updated for 'OPCUaServer.sdnp.NumTxFragments' :  Value(UInt32) = 1; quality=Good time=09-17-2025 14:19:07.616,,Information,Gateway,09/17/2025_14:19:07.616
DNP,142,...> sdnp       05  ,sdnp,Information,SCL,09/17/2025_14:19:07.616
OPC DEEP,143,Opc UA SDO updated for 'OPCUaServer.sdnp.L3.NumTxASDUs' :  Value(UInt32) = 1; quality=Good time=09-17-2025 14:19:07.616,,Information,Gateway,09/17/2025_14:19:07.617
OPC DEEP,144,Opc UA SDO updated for 'OPCUaServer.sdnp.NumRxBytes' :  Value(UInt32) = 1; quality=Good time=09-17-2025 14:19:07.617,,Information,Gateway,09/17/2025_14:19:07.617
DNP,145,...> sdnp       64 14 c4 04 00 03 00 c7 17  ,sdnp,Information,SCL,09/17/2025_14:19:07.617
OPC DEEP,146,Opc UA SDO updated for 'OPCUaServer.sdnp.NumRxBytes' :  Value(UInt32) = 10; quality=Good time=09-17-2025 14:19:07.618,,Information,Gateway,09/17/2025_14:19:07.618
DNP,147,...> sdnp       c0 c1 01 3c 02 06 3c 03 06 3c 04 06 3c 01 06 7a  ,sdnp,Information,SCL,09/17/2025_14:19:07.618
DNP,148,                6f  ,sdnp,Information,SCL,09/17/2025_14:19:07.618
OPC DEEP,149,Opc UA SDO updated for 'OPCUaServer.sdnp.L3.OnLineStatus' :  Value(Boolean) = True; quality=Good time=09-17-2025 14:19:07.618,,Information,Gateway,09/17/2025_14:19:07.618
OPC DEEP,150,Opc UA SDO updated for 'OPCUaServer.sdnp.NumRxBytes' :  Value(UInt32) = 27; quality=Good time=09-17-2025 14:19:07.618,,Information,Gateway,09/17/2025_14:19:07.619
OPC DEEP,151,Opc UA SDO updated for 'OPCUaServer.sdnp.L3.OnLineStatus' :  Value(Boolean) = True; quality=Good time=09-17-2025 14:19:07.619,,Information,Gateway,09/17/2025_14:19:07.619
OPC DEEP,152,Opc UA SDO updated for 'OPCUaServer.sdnp.NumRxFrames' :  Value(UInt32) = 1; quality=Good time=09-17-2025 14:19:07.619,,Information,Gateway,09/17/2025_14:19:07.619
General,153,Session sdnp.L3 was set online,,Information,Gateway,09/17/2025_14:19:07.619
OPC DEEP,154,Opc UA SDO updated for 'OPCUaServer.sdnp.ChannelSessionsOnLineStatus' :  Value(Boolean) = True; quality=Good time=09-17-2025 14:19:07.620,,Information,Gateway,09/17/2025_14:19:07.620
DNP,155,---> sdnp       Primary Frame - Unconfirmed User Data   ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.619
DNP,156,                LEN(20) DIR(1) PRM(1) FCV(0) FCB(0) DEST(4) SRC(3) ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.620
DNP,157,                05 64 14 c4 04 00 03 00 c7 17  ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.620
OPC DEEP,158,IsOpcReady FALSE,,Information,Gateway,09/17/2025_14:19:07.620
DNP,159,                c0 c1 01 3c 02 06 3c 03 06 3c 04 06 3c 01 06 7a  ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.620
DNP,160,                6f  ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.620
OPC DEEP,161,Opc UA SDO updated for 'OPCUaServer.ChannelsOnlineStatus' :  Value(Boolean) = False; quality=Good time=09-17-2025 14:19:07.620,,Information,Gateway,09/17/2025_14:19:07.620
DNP,162,~~~> sdnp.L3    Transport Header ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.620
DNP,163,                FIR(1) FIN(1) SEQ# 0 ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.620
DNP,164,                c0 c1 01 3c 02 06 3c 03 06 3c 04 06 3c 01 06  ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.620
OPC DEEP,165,Opc UA SDO updated for 'OPCUaServer.sdnp.NumRxFragments' :  Value(UInt32) = 1; quality=Good time=09-17-2025 14:19:07.620,,Information,Gateway,09/17/2025_14:19:07.620
DNP,166,===> sdnp.L3    Application Header  Read Request ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.620
DNP,167,                FIR(1) FIN(1) CON(0) UNS(0) SEQ# 1 ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.620
DNP,168,                c1 01 3c 02 06 3c 03 06 3c 04 06 3c 01 06  ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.620
OPC DEEP,169,Opc UA SDO updated for 'OPCUaServer.sdnp.L3.NumRxASDUs' :  Value(UInt32) = 1; quality=Good time=09-17-2025 14:19:07.620,,Information,Gateway,09/17/2025_14:19:07.620
DNP,170,...> mdnp       05  ,mdnp,Information,SCL,09/17/2025_14:19:07.620
OPC DEEP,171,Opc UA SDO updated for 'OPCUaServer.mdnp.NumRxBytes' :  Value(UInt32) = 1; quality=Good time=09-17-2025 14:19:07.620,,Information,Gateway,09/17/2025_14:19:07.620
DNP,172,...> mdnp       64 0a 44 03 00 04 00 7c ae  ,mdnp,Information,SCL,09/17/2025_14:19:07.620
OPC DEEP,173,Opc UA SDO updated for 'OPCUaServer.mdnp.NumRxBytes' :  Value(UInt32) = 10; quality=Good time=09-17-2025 14:19:07.620,,Information,Gateway,09/17/2025_14:19:07.620
DNP,174,...> mdnp       c0 f0 82 90 00 43 a2  ,mdnp,Information,SCL,09/17/2025_14:19:07.620
OPC DEEP,175,Opc UA SDO updated for 'OPCUaServer.mdnp.NumRxBytes' :  Value(UInt32) = 17; quality=Good time=09-17-2025 14:19:07.620,,Information,Gateway,09/17/2025_14:19:07.620
OPC DEEP,176,Opc UA SDO updated for 'OPCUaServer.mdnp.L4.OnLineStatus' :  Value(Boolean) = True; quality=Good time=09-17-2025 14:19:07.620,,Information,Gateway,09/17/2025_14:19:07.620
OPC DEEP,177,Opc UA SDO updated for 'OPCUaServer.mdnp.NumRxFrames' :  Value(UInt32) = 1; quality=Good time=09-17-2025 14:19:07.620,,Information,Gateway,09/17/2025_14:19:07.620
DNP,178,---> mdnp       Primary Frame - Unconfirmed User Data   ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.620
DNP,179,                LEN(10) DIR(0) PRM(1) FCV(0) FCB(0) DEST(3) SRC(4) ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.620
DNP,180,                05 64 0a 44 03 00 04 00 7c ae  ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.620
DNP,181,                c0 f0 82 90 00 43 a2  ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.620
DNP,182,~~~> mdnp.L4    Transport Header ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.620
OPC DEEP,183,Opc UA SDO updated for 'OPCUaServer.mdnp.L4.OnLineStatus' :  Value(Boolean) = True; quality=Good time=09-17-2025 14:19:07.620,,Information,Gateway,09/17/2025_14:19:07.620
DNP,184,                FIR(1) FIN(1) SEQ# 0 ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.620
DNP,185,                c0 f0 82 90 00  ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.620
OPC DEEP,186,Opc UA SDO updated for 'OPCUaServer.mdnp.L4.T1.P1' :  Value(Boolean) = False; quality=BadWaitingForInitialData time=09-17-2025 14:19:07.620,,Information,Gateway,09/17/2025_14:19:07.620
OPC DEEP,187,Opc UA SDO updated for 'OPCUaServer.mdnp.NumRxFragments' :  Value(UInt32) = 1; quality=Good time=09-17-2025 14:19:07.620,,Information,Gateway,09/17/2025_14:19:07.620
DNP,188,===> mdnp.L4    Application Header  Unsolicited ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.620
DNP,189,                FIR(1) FIN(1) CON(1) UNS(1) SEQ# 0 ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.620
DNP,190,                f0 82 90 00  ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.620
OPC DEEP,191,Opc UA SDO updated for 'OPCUaServer.mdnp.L4.T1.P2' :  Value(Boolean) = False; quality=BadWaitingForInitialData time=09-17-2025 14:19:07.620,,Information,Gateway,09/17/2025_14:19:07.620
DNP,192,<+++ mdnp.L4    Insert request in queue: Application Confirmation ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.620
DNP,193,<=== mdnp.L4    Application Header  Application Confirmation ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.620
DNP,194,                FIR(1) FIN(1) CON(0) UNS(1) SEQ# 0 ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.620
DNP,195,                d0 00  ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.620
DNP,196,<~~~ mdnp.L4    Transport Header ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.620
DNP,197,                FIR(1) FIN(1) SEQ# 1 ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.620
OPC DEEP,198,Opc UA SDO updated for 'OPCUaServer.mdnp.L4.T1.P3' :  Value(Boolean) = False; quality=BadWaitingForInitialData time=09-17-2025 14:19:07.620,,Information,Gateway,09/17/2025_14:19:07.620
DNP,199,                c1 d0 00  ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.620
DNP,200,<--- mdnp       Primary Frame - Unconfirmed User Data   ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.620
DNP,201,                LEN(8) DIR(1) PRM(1) FCV(0) FCB(0) DEST(4) SRC(3) ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.620
DNP,202,                05 64 08 c4 04 00 03 00 b4 b8  ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.620
DNP,203,                c1 d0 00 a3 50  ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.620
OPC DEEP,204,Opc UA SDO updated for 'OPCUaServer.mdnp.L4.T1.P4' :  Value(Boolean) = False; quality=BadWaitingForInitialData time=09-17-2025 14:19:07.620,,Information,Gateway,09/17/2025_14:19:07.620
DNP,205,mdnp - 127.0.0.1:20000 - TCP transmit 15 bytes,mdnp,Information,SCL,09/17/2025_14:19:07.620
OPC DEEP,206,Opc UA SDO updated for 'OPCUaServer.mdnp.L4.T1.P5' :  Value(Boolean) = False; quality=BadWaitingForInitialData time=09-17-2025 14:19:07.620,,Information,Gateway,09/17/2025_14:19:07.620
OPC DEEP,207,Opc UA SDO updated for 'OPCUaServer.mdnp.L4.T1.P6' :  Value(Boolean) = False; quality=BadWaitingForInitialData time=09-17-2025 14:19:07.620,,Information,Gateway,09/17/2025_14:19:07.620
General,208,Session mdnp.L4 was set online,,Information,Gateway,09/17/2025_14:19:07.620
DNP,209,<... mdnp       05 64 08 c4 04 00 03 00 b4 b8 c1 d0 00 a3 50  ,mdnp,Information,SCL,09/17/2025_14:19:07.620
OPC DEEP,210,Opc UA SDO updated for 'OPCUaServer.mdnp.ChannelSessionsOnLineStatus' :  Value(Boolean) = True; quality=Good time=09-17-2025 14:19:07.620,,Information,Gateway,09/17/2025_14:19:07.620
OPC DEEP,211,IsOpcReady FALSE,,Information,Gateway,09/17/2025_14:19:07.620
OPC DEEP,212,Opc UA SDO updated for 'OPCUaServer.ChannelsOnlineStatus' :  Value(Boolean) = True; quality=Good time=09-17-2025 14:19:07.620,,Information,Gateway,09/17/2025_14:19:07.620
OPC DEEP,213,Opc UA SDO updated for 'OPCUaServer.mdnp.NumTxBytes' :  Value(UInt32) = 42; quality=Good time=09-17-2025 14:19:07.620,,Information,Gateway,09/17/2025_14:19:07.621
OPC DEEP,214,Opc UA SDO updated for 'OPCUaServer.mdnp.NumTxFrames' :  Value(UInt32) = 2; quality=Good time=09-17-2025 14:19:07.621,,Information,Gateway,09/17/2025_14:19:07.621
OPC DEEP,215,Opc UA SDO updated for 'OPCUaServer.mdnp.NumTxFragments' :  Value(UInt32) = 2; quality=Good time=09-17-2025 14:19:07.621,,Information,Gateway,09/17/2025_14:19:07.621
OPC DEEP,216,Opc UA SDO updated for 'OPCUaServer.mdnp.L4.NumRxASDUs' :  Value(UInt32) = 1; quality=Good time=09-17-2025 14:19:07.621,,Information,Gateway,09/17/2025_14:19:07.621
DNP,217,===> mdnp.L4    IIN Bits: ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.621
DNP,218,                IIN1.7 Device Restart ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.621
DNP,219,<+++ mdnp.L4    Build DNP3 Message: ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.621
DNP,220,                Tx Object 60(Class Data)  variation 2  qualifier 0x06(All Points)  ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.621
DNP,221,                Tx Object 60(Class Data)  variation 3  qualifier 0x06(All Points)  ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.621
DNP,222,                Tx Object 60(Class Data)  variation 4  qualifier 0x06(All Points)  ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.621
DNP,223,                Tx Object 60(Class Data)  variation 1  qualifier 0x06(All Points)  ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.621
DNP,224,                IIN1.4 Time Synchronization Required ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.621
DNP,225,<+++ mdnp.L4    Build DNP3 Message: Disable Unsolicited Due to Restart IIN ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.621
DNP,226,                Tx Object 60(Class Data)  variation 2  qualifier 0x06(All Points)  ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.621
DNP,227,                Tx Object 60(Class Data)  variation 3  qualifier 0x06(All Points)  ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.621
DNP,228,                Tx Object 60(Class Data)  variation 4  qualifier 0x06(All Points)  ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.621
DNP,229,<+++ mdnp.L4    Insert request in queue: Disable Unsolicited Due to Restart IIN ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.621
DNP,230,<+++ mdnp.L4    Build DNP3 Message: Clear Restart Due to Restart IIN ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.621
DNP,231,                Tx Object 80(Internal Indications)  variation 1  qualifier 0x00(8 Bit Start Stop)  ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.621
DNP,232,<+++ mdnp.L4    Insert request in queue: Clear Restart Due to Restart IIN ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.621
DNP,233,<+++ mdnp.L4    Build DNP3 Message: Time Synchronization Due to Need Time IIN ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.621
DNP,234,                Tx Object 50(Time and Date)  variation 1  qualifier 0x07(8 Bit Limited Quantity)  ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.621
DNP,235,<+++ mdnp.L4    Insert request in queue: Time Synchronization Due to Need Time IIN ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.621
DNP,236,<+++ mdnp.L4    Insert request in queue: SDG DNP Action sent: CLASS1 CLASS2 CLASS3 CLASS0  ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.621
OPC DEEP,237,Opc UA SDO updated for 'OPCUaServer.mdnp.L4.NumTxASDUs' :  Value(UInt32) = 2; quality=Good time=09-17-2025 14:19:07.621,,Information,Gateway,09/17/2025_14:19:07.621
DNP,238,...> sdnp       05  ,sdnp,Information,SCL,09/17/2025_14:19:07.709
OPC DEEP,239,Opc UA SDO updated for 'OPCUaServer.sdnp.NumRxBytes' :  Value(UInt32) = 28; quality=Good time=09-17-2025 14:19:07.709,,Information,Gateway,09/17/2025_14:19:07.709
DNP,240,...> sdnp       64 08 c4 04 00 03 00 b4 b8  ,sdnp,Information,SCL,09/17/2025_14:19:07.709
OPC DEEP,241,Opc UA SDO updated for 'OPCUaServer.sdnp.NumRxBytes' :  Value(UInt32) = 37; quality=Good time=09-17-2025 14:19:07.709,,Information,Gateway,09/17/2025_14:19:07.709
DNP,242,...> sdnp       c1 d0 00 a3 50  ,sdnp,Information,SCL,09/17/2025_14:19:07.709
OPC DEEP,243,Opc UA SDO updated for 'OPCUaServer.sdnp.NumRxBytes' :  Value(UInt32) = 42; quality=Good time=09-17-2025 14:19:07.709,,Information,Gateway,09/17/2025_14:19:07.709
OPC DEEP,244,Opc UA SDO updated for 'OPCUaServer.sdnp.NumRxFrames' :  Value(UInt32) = 2; quality=Good time=09-17-2025 14:19:07.709,,Information,Gateway,09/17/2025_14:19:07.709
DNP,245,---> sdnp       Primary Frame - Unconfirmed User Data   ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.709
DNP,246,                LEN(8) DIR(1) PRM(1) FCV(0) FCB(0) DEST(4) SRC(3) ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.709
DNP,247,                05 64 08 c4 04 00 03 00 b4 b8  ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.709
DNP,248,                c1 d0 00 a3 50  ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.709
DNP,249,~~~> sdnp.L3    Transport Header ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.709
DNP,250,                FIR(1) FIN(1) SEQ# 1 ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.709
DNP,251,                c1 d0 00  ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.709
OPC DEEP,252,Opc UA SDO updated for 'OPCUaServer.sdnp.NumRxFragments' :  Value(UInt32) = 2; quality=Good time=09-17-2025 14:19:07.709,,Information,Gateway,09/17/2025_14:19:07.709
DNP,253,===> sdnp.L3    Application Header  Application Confirmation ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.709
DNP,254,                FIR(1) FIN(1) CON(0) UNS(1) SEQ# 0 ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.709
DNP,255,                d0 00  ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.709
OPC DEEP,256,Opc UA SDO updated for 'OPCUaServer.sdnp.L3.NumRxASDUs' :  Value(UInt32) = 2; quality=Good time=09-17-2025 14:19:07.709,,Information,Gateway,09/17/2025_14:19:07.709
DNP,257,                Rx Object 60(Class Data)  variation 2  qualifier 0x06(All Points)  ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.709
DNP,258,<+++ sdnp.L3    Build DNP3 Message: Read Response ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.709
DNP,259,                Rx Object 60(Class Data)  variation 3  qualifier 0x06(All Points)  ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.709
DNP,260,<+++ sdnp.L3    Build DNP3 Message: Read Response ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.709
DNP,261,                Rx Object 60(Class Data)  variation 4  qualifier 0x06(All Points)  ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.709
DNP,262,<+++ sdnp.L3    Build DNP3 Message: Read Response ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.709
DNP,263,                Rx Object 60(Class Data)  variation 1  qualifier 0x06(All Points)  ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.709
DNP,264,<+++ sdnp.L3    Build DNP3 Message: Read Response ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.709
DNP,265,                Tx Object 1(Binary Input)  variation 1  qualifier 0x00(8 Bit Start Stop)  ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.709
DNP,266,                Tx Object 1(Binary Input)  variation 2  qualifier 0x00(8 Bit Start Stop)  ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.709
DNP,267,                      Binary Input 000001 = 0x00 ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.709
DNP,268,                      Binary Input 000002 = 0x00 ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.709
DNP,269,                      Binary Input 000003 = 0x00 ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.709
DNP,270,                      Binary Input 000004 = 0x00 ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.709
DNP,271,                      Binary Input 000005 = 0x00 ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.709
DNP,272,                      Binary Input 000006 = 0x00 ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.709
DNP,273,<+++ sdnp.L3    Insert request in queue: Read Response ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.709
DNP,274,<=== sdnp.L3    Application Header  Response ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.709
DNP,275,                FIR(1) FIN(1) CON(0) UNS(0) SEQ# 1 ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.709
DNP,276,                c1 81 90 00 01 02 00 01 06 00 00 00 00 00 00  ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.709
DNP,277,<~~~ sdnp.L3    Transport Header ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.709
DNP,278,                FIR(1) FIN(1) SEQ# 1 ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.709
DNP,279,                c1 c1 81 90 00 01 02 00 01 06 00 00 00 00 00 00  ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.709
DNP,280,<--- sdnp       Primary Frame - Unconfirmed User Data   ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.709
DNP,281,                LEN(21) DIR(0) PRM(1) FCV(0) FCB(0) DEST(3) SRC(4) ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.709
DNP,282,                05 64 15 44 03 00 04 00 5f 92  ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.709
DNP,283,                c1 c1 81 90 00 01 02 00 01 06 00 00 00 00 00 00  ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.709
DNP,284,                7b 04  ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.709
DNP,285,sdnp - 127.0.0.1:20000 - TCP transmit 28 bytes,sdnp,Information,SCL,09/17/2025_14:19:07.709
DNP,286,<... sdnp       05 64 15 44 03 00 04 00 5f 92 c1 c1 81 90 00 01  ,sdnp,Information,SCL,09/17/2025_14:19:07.710
DNP,287,                02 00 01 06 00 00 00 00 00 00 7b 04  ,sdnp,Information,SCL,09/17/2025_14:19:07.710
OPC DEEP,288,Opc UA SDO updated for 'OPCUaServer.sdnp.NumTxBytes' :  Value(UInt32) = 45; quality=Good time=09-17-2025 14:19:07.710,,Information,Gateway,09/17/2025_14:19:07.710
OPC DEEP,289,Opc UA SDO updated for 'OPCUaServer.sdnp.NumTxFrames' :  Value(UInt32) = 2; quality=Good time=09-17-2025 14:19:07.710,,Information,Gateway,09/17/2025_14:19:07.710
OPC DEEP,290,Opc UA SDO updated for 'OPCUaServer.sdnp.NumTxFragments' :  Value(UInt32) = 2; quality=Good time=09-17-2025 14:19:07.710,,Information,Gateway,09/17/2025_14:19:07.710
OPC DEEP,291,Opc UA SDO updated for 'OPCUaServer.sdnp.L3.NumTxASDUs' :  Value(UInt32) = 2; quality=Good time=09-17-2025 14:19:07.710,,Information,Gateway,09/17/2025_14:19:07.710
DNP,292,...> mdnp       05  ,mdnp,Information,SCL,09/17/2025_14:19:07.710
OPC DEEP,293,Opc UA SDO updated for 'OPCUaServer.mdnp.NumRxBytes' :  Value(UInt32) = 18; quality=Good time=09-17-2025 14:19:07.710,,Information,Gateway,09/17/2025_14:19:07.710
DNP,294,...> mdnp       64 15 44 03 00 04 00 5f 92  ,mdnp,Information,SCL,09/17/2025_14:19:07.710
OPC DEEP,295,Opc UA SDO updated for 'OPCUaServer.mdnp.NumRxBytes' :  Value(UInt32) = 27; quality=Good time=09-17-2025 14:19:07.710,,Information,Gateway,09/17/2025_14:19:07.710
DNP,296,...> mdnp       c1 c1 81 90 00 01 02 00 01 06 00 00 00 00 00 00  ,mdnp,Information,SCL,09/17/2025_14:19:07.710
DNP,297,                7b 04  ,mdnp,Information,SCL,09/17/2025_14:19:07.710
OPC DEEP,298,Opc UA SDO updated for 'OPCUaServer.mdnp.NumRxBytes' :  Value(UInt32) = 45; quality=Good time=09-17-2025 14:19:07.710,,Information,Gateway,09/17/2025_14:19:07.710
OPC DEEP,299,Opc UA SDO updated for 'OPCUaServer.mdnp.NumRxFrames' :  Value(UInt32) = 2; quality=Good time=09-17-2025 14:19:07.710,,Information,Gateway,09/17/2025_14:19:07.711
DNP,300,---> mdnp       Primary Frame - Unconfirmed User Data   ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.711
DNP,301,                LEN(21) DIR(0) PRM(1) FCV(0) FCB(0) DEST(3) SRC(4) ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.711
DNP,302,                05 64 15 44 03 00 04 00 5f 92  ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.711
DNP,303,                c1 c1 81 90 00 01 02 00 01 06 00 00 00 00 00 00  ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.711
DNP,304,                7b 04  ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.711
DNP,305,~~~> mdnp.L4    Transport Header ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.711
DNP,306,                FIR(1) FIN(1) SEQ# 1 ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.711
DNP,307,                c1 c1 81 90 00 01 02 00 01 06 00 00 00 00 00 00  ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.711
OPC DEEP,308,Opc UA SDO updated for 'OPCUaServer.mdnp.NumRxFragments' :  Value(UInt32) = 2; quality=Good time=09-17-2025 14:19:07.711,,Information,Gateway,09/17/2025_14:19:07.711
OPC DEEP,309,Opc UA SDO updated for 'OPCUaServer.mdnp.L4.NumRxASDUs' :  Value(UInt32) = 2; quality=Good time=09-17-2025 14:19:07.711,,Information,Gateway,09/17/2025_14:19:07.711
DNP,310,===> mdnp.L4    Application Header  Response ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.711
DNP,311,                FIR(1) FIN(1) CON(0) UNS(0) SEQ# 1 ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.711
DNP,312,                c1 81 90 00 01 02 00 01 06 00 00 00 00 00 00  ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.711
DNP,313,+++> mdnp.L4    Process response to request: Integrity Poll Due to Master Restart ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.711
DNP,314,                Rx Object 1(Binary Input)  variation 2  qualifier 0x00(8 Bit Start Stop)  ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.711
DNP,315,                      Binary Input 000001 = 0x00 ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.711
DNP,316,                      Binary Input 000002 = 0x00 ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.711
DNP,317,                      Binary Input 000003 = 0x00 ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.711
DNP,318,                      Binary Input 000004 = 0x00 ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.711
DNP,319,                      Binary Input 000005 = 0x00 ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.711
DNP,320,                      Binary Input 000006 = 0x00 ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.711
DNP,321,===> mdnp.L4    IIN Bits: ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.711
DNP,322,                IIN1.7 Device Restart ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.711
DNP,323,                IIN1.4 Time Synchronization Required ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.711
DNP,324,<+++ mdnp.L4    Build DNP3 Message: Disable Unsolicited Due to Restart IIN ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.711
DNP,325,                Tx Object 60(Class Data)  variation 2  qualifier 0x06(All Points)  ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.711
DNP,326,                Tx Object 60(Class Data)  variation 3  qualifier 0x06(All Points)  ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.711
DNP,327,                Tx Object 60(Class Data)  variation 4  qualifier 0x06(All Points)  ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.711
DNP,328,<+++ mdnp.L4    Insert request in queue: Disable Unsolicited Due to Restart IIN ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.711
DNP,329,<=== mdnp.L4    Application Header  Disable Unsolicited Messages ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.711
DNP,330,                FIR(1) FIN(1) CON(0) UNS(0) SEQ# 2 ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.711
DNP,331,                c2 15 3c 02 06 3c 03 06 3c 04 06  ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.711
DNP,332,<~~~ mdnp.L4    Transport Header ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.711
DNP,333,                FIR(1) FIN(1) SEQ# 2 ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.711
DNP,334,                c2 c2 15 3c 02 06 3c 03 06 3c 04 06  ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.711
DNP,335,<--- mdnp       Primary Frame - Unconfirmed User Data   ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.711
DNP,336,                LEN(17) DIR(1) PRM(1) FCV(0) FCB(0) DEST(4) SRC(3) ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.711
DNP,337,                05 64 11 c4 04 00 03 00 4e ef  ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.711
DNP,338,                c2 c2 15 3c 02 06 3c 03 06 3c 04 06 b1 ec  ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.711
DNP,339,mdnp - 127.0.0.1:20000 - TCP transmit 24 bytes,mdnp,Information,SCL,09/17/2025_14:19:07.711
DNP,340,<... mdnp       05 64 11 c4 04 00 03 00 4e ef c2 c2 15 3c 02 06  ,mdnp,Information,SCL,09/17/2025_14:19:07.711
DNP,341,                3c 03 06 3c 04 06 b1 ec  ,mdnp,Information,SCL,09/17/2025_14:19:07.711
OPC DEEP,342,Opc UA SDO updated for 'OPCUaServer.mdnp.NumTxBytes' :  Value(UInt32) = 66; quality=Good time=09-17-2025 14:19:07.711,,Information,Gateway,09/17/2025_14:19:07.711
OPC DEEP,343,Opc UA SDO updated for 'OPCUaServer.mdnp.NumTxFrames' :  Value(UInt32) = 3; quality=Good time=09-17-2025 14:19:07.711,,Information,Gateway,09/17/2025_14:19:07.711
OPC DEEP,344,Opc UA SDO updated for 'OPCUaServer.mdnp.NumTxFragments' :  Value(UInt32) = 3; quality=Good time=09-17-2025 14:19:07.711,,Information,Gateway,09/17/2025_14:19:07.711
OPC DEEP,345,Opc UA SDO updated for 'OPCUaServer.mdnp.L4.NumTxASDUs' :  Value(UInt32) = 3; quality=Good time=09-17-2025 14:19:07.711,,Information,Gateway,09/17/2025_14:19:07.711
DNP,346,<+++ mdnp.L4    Build DNP3 Message: Clear Restart Due to Restart IIN ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.711
DNP,347,                Tx Object 80(Internal Indications)  variation 1  qualifier 0x00(8 Bit Start Stop)  ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.711
DNP,348,<+++ mdnp.L4    Insert request in queue: Clear Restart Due to Restart IIN ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.711
DNP,349,<+++ mdnp.L4    Build DNP3 Message: Integrity Poll Due to Restart IIN ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.711
DNP,350,                Tx Object 60(Class Data)  variation 2  qualifier 0x06(All Points)  ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.711
DNP,351,                Tx Object 60(Class Data)  variation 3  qualifier 0x06(All Points)  ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.711
DNP,352,                Tx Object 60(Class Data)  variation 4  qualifier 0x06(All Points)  ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.711
DNP,353,                Tx Object 60(Class Data)  variation 1  qualifier 0x06(All Points)  ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.711
DNP,354,<+++ mdnp.L4    Insert request in queue: Integrity Poll Due to Restart IIN ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.711
DNP,355,<+++ mdnp.L4    Build DNP3 Message: Time Synchronization Due to Need Time IIN ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.711
DNP,356,                Tx Object 50(Time and Date)  variation 1  qualifier 0x07(8 Bit Limited Quantity)  ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.711
DNP,357,<+++ mdnp.L4    Insert request in queue: Time Synchronization Due to Need Time IIN ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.711
OPC DEEP,358,Opc UA SDO updated for 'OPCUaServer.mdnp.L4.T1.P1' :  Value(Boolean) = False; quality=Bad time=09-17-2025 14:19:07.788,,Information,Gateway,09/17/2025_14:19:07.789
OPC DEEP,359,Opc UA SDO updated for 'OPCUaServer.mdnp.L4.T1.P2' :  Value(Boolean) = False; quality=Bad time=09-17-2025 14:19:07.789,,Information,Gateway,09/17/2025_14:19:07.789
OPC DEEP,360,Opc UA SDO updated for 'OPCUaServer.mdnp.L4.T1.P3' :  Value(Boolean) = False; quality=Bad time=09-17-2025 14:19:07.789,,Information,Gateway,09/17/2025_14:19:07.789
OPC DEEP,361,Opc UA SDO updated for 'OPCUaServer.mdnp.L4.T1.P4' :  Value(Boolean) = False; quality=Bad time=09-17-2025 14:19:07.789,,Information,Gateway,09/17/2025_14:19:07.789
OPC DEEP,362,Opc UA SDO updated for 'OPCUaServer.mdnp.L4.T1.P5' :  Value(Boolean) = False; quality=Bad time=09-17-2025 14:19:07.789,,Information,Gateway,09/17/2025_14:19:07.789
OPC DEEP,363,Opc UA SDO updated for 'OPCUaServer.mdnp.L4.T1.P6' :  Value(Boolean) = False; quality=Bad time=09-17-2025 14:19:07.789,,Information,Gateway,09/17/2025_14:19:07.789
DNP,364,...> sdnp       05  ,sdnp,Information,SCL,09/17/2025_14:19:07.818
OPC DEEP,365,Opc UA SDO updated for 'OPCUaServer.sdnp.NumRxBytes' :  Value(UInt32) = 43; quality=Good time=09-17-2025 14:19:07.819,,Information,Gateway,09/17/2025_14:19:07.819
DNP,366,...> sdnp       64 11 c4 04 00 03 00 4e ef  ,sdnp,Information,SCL,09/17/2025_14:19:07.819
OPC DEEP,367,Opc UA SDO updated for 'OPCUaServer.sdnp.NumRxBytes' :  Value(UInt32) = 52; quality=Good time=09-17-2025 14:19:07.819,,Information,Gateway,09/17/2025_14:19:07.819
DNP,368,...> sdnp       c2 c2 15 3c 02 06 3c 03 06 3c 04 06 b1 ec  ,sdnp,Information,SCL,09/17/2025_14:19:07.819
OPC DEEP,369,Opc UA SDO updated for 'OPCUaServer.sdnp.NumRxBytes' :  Value(UInt32) = 66; quality=Good time=09-17-2025 14:19:07.819,,Information,Gateway,09/17/2025_14:19:07.819
OPC DEEP,370,Opc UA SDO updated for 'OPCUaServer.sdnp.NumRxFrames' :  Value(UInt32) = 3; quality=Good time=09-17-2025 14:19:07.819,,Information,Gateway,09/17/2025_14:19:07.819
DNP,371,---> sdnp       Primary Frame - Unconfirmed User Data   ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.819
DNP,372,                LEN(17) DIR(1) PRM(1) FCV(0) FCB(0) DEST(4) SRC(3) ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.819
DNP,373,                05 64 11 c4 04 00 03 00 4e ef  ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.819
DNP,374,                c2 c2 15 3c 02 06 3c 03 06 3c 04 06 b1 ec  ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.819
DNP,375,~~~> sdnp.L3    Transport Header ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.819
DNP,376,                FIR(1) FIN(1) SEQ# 2 ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.819
DNP,377,                c2 c2 15 3c 02 06 3c 03 06 3c 04 06  ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.819
OPC DEEP,378,Opc UA SDO updated for 'OPCUaServer.sdnp.NumRxFragments' :  Value(UInt32) = 3; quality=Good time=09-17-2025 14:19:07.819,,Information,Gateway,09/17/2025_14:19:07.819
OPC DEEP,379,Opc UA SDO updated for 'OPCUaServer.sdnp.L3.NumRxASDUs' :  Value(UInt32) = 3; quality=Good time=09-17-2025 14:19:07.819,,Information,Gateway,09/17/2025_14:19:07.819
DNP,380,===> sdnp.L3    Application Header  Disable Unsolicited Messages ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.819
DNP,381,                FIR(1) FIN(1) CON(0) UNS(0) SEQ# 2 ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.819
DNP,382,                c2 15 3c 02 06 3c 03 06 3c 04 06  ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.819
DNP,383,                Rx Object 60(Class Data)  variation 2  qualifier 0x06(All Points)  ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.819
DNP,384,                Rx Object 60(Class Data)  variation 3  qualifier 0x06(All Points)  ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.819
DNP,385,                Rx Object 60(Class Data)  variation 4  qualifier 0x06(All Points)  ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.819
DNP,386,<+++ sdnp.L3    Insert request in queue: Disable Unsolicited Response ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.819
DNP,387,<=== sdnp.L3    Application Header  Response ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.819
DNP,388,                FIR(1) FIN(1) CON(0) UNS(0) SEQ# 2 ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.819
DNP,389,                c2 81 90 00  ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.819
DNP,390,<~~~ sdnp.L3    Transport Header ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.819
DNP,391,                FIR(1) FIN(1) SEQ# 2 ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.819
DNP,392,                c2 c2 81 90 00  ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.822
DNP,393,<--- sdnp       Primary Frame - Unconfirmed User Data   ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.822
DNP,394,                LEN(10) DIR(0) PRM(1) FCV(0) FCB(0) DEST(3) SRC(4) ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.822
DNP,395,                05 64 0a 44 03 00 04 00 7c ae  ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.822
DNP,396,                c2 c2 81 90 00 3e a2  ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.822
DNP,397,sdnp - 127.0.0.1:20000 - TCP transmit 17 bytes,sdnp,Information,SCL,09/17/2025_14:19:07.822
DNP,398,<... sdnp       05 64 0a 44 03 00 04 00 7c ae c2 c2 81 90 00 3e  ,sdnp,Information,SCL,09/17/2025_14:19:07.822
DNP,399,                a2  ,sdnp,Information,SCL,09/17/2025_14:19:07.822
OPC DEEP,400,Opc UA SDO updated for 'OPCUaServer.sdnp.NumTxBytes' :  Value(UInt32) = 62; quality=Good time=09-17-2025 14:19:07.822,,Information,Gateway,09/17/2025_14:19:07.822
OPC DEEP,401,Opc UA SDO updated for 'OPCUaServer.sdnp.NumTxFrames' :  Value(UInt32) = 3; quality=Good time=09-17-2025 14:19:07.822,,Information,Gateway,09/17/2025_14:19:07.822
OPC DEEP,402,Opc UA SDO updated for 'OPCUaServer.sdnp.NumTxFragments' :  Value(UInt32) = 3; quality=Good time=09-17-2025 14:19:07.822,,Information,Gateway,09/17/2025_14:19:07.822
OPC DEEP,403,Opc UA SDO updated for 'OPCUaServer.sdnp.L3.NumTxASDUs' :  Value(UInt32) = 3; quality=Good time=09-17-2025 14:19:07.822,,Information,Gateway,09/17/2025_14:19:07.822
DNP,404,...> mdnp       05  ,mdnp,Information,SCL,09/17/2025_14:19:07.822
OPC DEEP,405,Opc UA SDO updated for 'OPCUaServer.mdnp.NumRxBytes' :  Value(UInt32) = 46; quality=Good time=09-17-2025 14:19:07.822,,Information,Gateway,09/17/2025_14:19:07.822
DNP,406,...> mdnp       64 0a 44 03 00 04 00 7c ae  ,mdnp,Information,SCL,09/17/2025_14:19:07.822
OPC DEEP,407,Opc UA SDO updated for 'OPCUaServer.mdnp.NumRxBytes' :  Value(UInt32) = 55; quality=Good time=09-17-2025 14:19:07.822,,Information,Gateway,09/17/2025_14:19:07.822
DNP,408,...> mdnp       c2 c2 81 90 00 3e a2  ,mdnp,Information,SCL,09/17/2025_14:19:07.822
OPC DEEP,409,Opc UA SDO updated for 'OPCUaServer.mdnp.NumRxBytes' :  Value(UInt32) = 62; quality=Good time=09-17-2025 14:19:07.822,,Information,Gateway,09/17/2025_14:19:07.822
OPC DEEP,410,Opc UA SDO updated for 'OPCUaServer.mdnp.NumRxFrames' :  Value(UInt32) = 3; quality=Good time=09-17-2025 14:19:07.822,,Information,Gateway,09/17/2025_14:19:07.822
DNP,411,---> mdnp       Primary Frame - Unconfirmed User Data   ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.822
DNP,412,                LEN(10) DIR(0) PRM(1) FCV(0) FCB(0) DEST(3) SRC(4) ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.822
DNP,413,                05 64 0a 44 03 00 04 00 7c ae  ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.822
DNP,414,                c2 c2 81 90 00 3e a2  ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.822
DNP,415,~~~> mdnp.L4    Transport Header ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.822
DNP,416,                FIR(1) FIN(1) SEQ# 2 ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.822
DNP,417,                c2 c2 81 90 00  ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.822
OPC DEEP,418,Opc UA SDO updated for 'OPCUaServer.mdnp.NumRxFragments' :  Value(UInt32) = 3; quality=Good time=09-17-2025 14:19:07.822,,Information,Gateway,09/17/2025_14:19:07.822
OPC DEEP,419,Opc UA SDO updated for 'OPCUaServer.mdnp.L4.NumRxASDUs' :  Value(UInt32) = 3; quality=Good time=09-17-2025 14:19:07.822,,Information,Gateway,09/17/2025_14:19:07.822
DNP,420,===> mdnp.L4    Application Header  Response ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.823
DNP,421,                FIR(1) FIN(1) CON(0) UNS(0) SEQ# 2 ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.823
DNP,422,                c2 81 90 00  ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.823
DNP,423,+++> mdnp.L4    Process response to request: Disable Unsolicited Due to Restart IIN ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.823
DNP,424,===> mdnp.L4    IIN Bits: ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.823
DNP,425,                IIN1.7 Device Restart ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.823
DNP,426,                IIN1.4 Time Synchronization Required ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.823
DNP,427,<+++ mdnp.L4    Build DNP3 Message: Clear Restart Due to Restart IIN ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.823
DNP,428,                Tx Object 80(Internal Indications)  variation 1  qualifier 0x00(8 Bit Start Stop)  ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.823
DNP,429,<+++ mdnp.L4    Insert request in queue: Clear Restart Due to Restart IIN ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.823
DNP,430,<=== mdnp.L4    Application Header  Write Request ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.823
DNP,431,                FIR(1) FIN(1) CON(0) UNS(0) SEQ# 3 ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.823
DNP,432,                c3 02 50 01 00 07 07 00  ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.823
DNP,433,<~~~ mdnp.L4    Transport Header ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.823
DNP,434,                FIR(1) FIN(1) SEQ# 3 ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.823
DNP,435,                c3 c3 02 50 01 00 07 07 00  ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.823
DNP,436,<--- mdnp       Primary Frame - Unconfirmed User Data   ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.823
DNP,437,                LEN(14) DIR(1) PRM(1) FCV(0) FCB(0) DEST(4) SRC(3) ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.823
DNP,438,                05 64 0e c4 04 00 03 00 6d d3  ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.823
DNP,439,                c3 c3 02 50 01 00 07 07 00 20 5d  ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.823
DNP,440,mdnp - 127.0.0.1:20000 - TCP transmit 21 bytes,mdnp,Information,SCL,09/17/2025_14:19:07.823
DNP,441,<... mdnp       05 64 0e c4 04 00 03 00 6d d3 c3 c3 02 50 01 00  ,mdnp,Information,SCL,09/17/2025_14:19:07.823
DNP,442,                07 07 00 20 5d  ,mdnp,Information,SCL,09/17/2025_14:19:07.823
OPC DEEP,443,Opc UA SDO updated for 'OPCUaServer.mdnp.NumTxBytes' :  Value(UInt32) = 87; quality=Good time=09-17-2025 14:19:07.823,,Information,Gateway,09/17/2025_14:19:07.823
OPC DEEP,444,Opc UA SDO updated for 'OPCUaServer.mdnp.NumTxFrames' :  Value(UInt32) = 4; quality=Good time=09-17-2025 14:19:07.823,,Information,Gateway,09/17/2025_14:19:07.823
OPC DEEP,445,Opc UA SDO updated for 'OPCUaServer.mdnp.NumTxFragments' :  Value(UInt32) = 4; quality=Good time=09-17-2025 14:19:07.823,,Information,Gateway,09/17/2025_14:19:07.823
OPC DEEP,446,Opc UA SDO updated for 'OPCUaServer.mdnp.L4.NumTxASDUs' :  Value(UInt32) = 4; quality=Good time=09-17-2025 14:19:07.823,,Information,Gateway,09/17/2025_14:19:07.823
DNP,447,<+++ mdnp.L4    Build DNP3 Message: Time Synchronization Due to Need Time IIN ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.823
DNP,448,                Tx Object 50(Time and Date)  variation 1  qualifier 0x07(8 Bit Limited Quantity)  ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.823
DNP,449,<+++ mdnp.L4    Insert request in queue: Time Synchronization Due to Need Time IIN ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.823
DNP,450,...> sdnp       05  ,sdnp,Information,SCL,09/17/2025_14:19:07.929
OPC DEEP,451,Opc UA SDO updated for 'OPCUaServer.sdnp.NumRxBytes' :  Value(UInt32) = 67; quality=Good time=09-17-2025 14:19:07.929,,Information,Gateway,09/17/2025_14:19:07.929
DNP,452,...> sdnp       64 0e c4 04 00 03 00 6d d3  ,sdnp,Information,SCL,09/17/2025_14:19:07.929
OPC DEEP,453,Opc UA SDO updated for 'OPCUaServer.sdnp.NumRxBytes' :  Value(UInt32) = 76; quality=Good time=09-17-2025 14:19:07.929,,Information,Gateway,09/17/2025_14:19:07.929
DNP,454,...> sdnp       c3 c3 02 50 01 00 07 07 00 20 5d  ,sdnp,Information,SCL,09/17/2025_14:19:07.929
OPC DEEP,455,Opc UA SDO updated for 'OPCUaServer.sdnp.NumRxBytes' :  Value(UInt32) = 87; quality=Good time=09-17-2025 14:19:07.929,,Information,Gateway,09/17/2025_14:19:07.930
OPC DEEP,456,Opc UA SDO updated for 'OPCUaServer.sdnp.NumRxFrames' :  Value(UInt32) = 4; quality=Good time=09-17-2025 14:19:07.930,,Information,Gateway,09/17/2025_14:19:07.930
DNP,457,---> sdnp       Primary Frame - Unconfirmed User Data   ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.930
DNP,458,                LEN(14) DIR(1) PRM(1) FCV(0) FCB(0) DEST(4) SRC(3) ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.933
DNP,459,                05 64 0e c4 04 00 03 00 6d d3  ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.933
DNP,460,                c3 c3 02 50 01 00 07 07 00 20 5d  ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.933
DNP,461,~~~> sdnp.L3    Transport Header ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.933
DNP,462,                FIR(1) FIN(1) SEQ# 3 ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.933
DNP,463,                c3 c3 02 50 01 00 07 07 00  ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.933
OPC DEEP,464,Opc UA SDO updated for 'OPCUaServer.sdnp.NumRxFragments' :  Value(UInt32) = 4; quality=Good time=09-17-2025 14:19:07.933,,Information,Gateway,09/17/2025_14:19:07.933
OPC DEEP,465,Opc UA SDO updated for 'OPCUaServer.sdnp.L3.NumRxASDUs' :  Value(UInt32) = 4; quality=Good time=09-17-2025 14:19:07.933,,Information,Gateway,09/17/2025_14:19:07.933
DNP,466,===> sdnp.L3    Application Header  Write Request ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.933
DNP,467,                FIR(1) FIN(1) CON(0) UNS(0) SEQ# 3 ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.933
DNP,468,                c3 02 50 01 00 07 07 00  ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.933
DNP,469,                Rx Object 80(Internal Indications)  variation 1  qualifier 0x00(8 Bit Start Stop)  ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.933
DNP,470,<+++ sdnp.L3    Insert request in queue: Write Response ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.933
DNP,471,<=== sdnp.L3    Application Header  Response ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.933
DNP,472,                FIR(1) FIN(1) CON(0) UNS(0) SEQ# 3 ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.933
DNP,473,                c3 81 10 00  ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.933
DNP,474,<~~~ sdnp.L3    Transport Header ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.934
DNP,475,                FIR(1) FIN(1) SEQ# 3 ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.934
DNP,476,                c3 c3 81 10 00  ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.934
DNP,477,<--- sdnp       Primary Frame - Unconfirmed User Data   ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.934
DNP,478,                LEN(10) DIR(0) PRM(1) FCV(0) FCB(0) DEST(3) SRC(4) ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.934
DNP,479,                05 64 0a 44 03 00 04 00 7c ae  ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.934
DNP,480,                c3 c3 81 10 00 ff 58  ,sdnp.L3,Information,SCL,09/17/2025_14:19:07.934
DNP,481,sdnp - 127.0.0.1:20000 - TCP transmit 17 bytes,sdnp,Information,SCL,09/17/2025_14:19:07.934
DNP,482,<... sdnp       05 64 0a 44 03 00 04 00 7c ae c3 c3 81 10 00 ff  ,sdnp,Information,SCL,09/17/2025_14:19:07.935
DNP,483,                58  ,sdnp,Information,SCL,09/17/2025_14:19:07.935
OPC DEEP,484,Opc UA SDO updated for 'OPCUaServer.sdnp.NumTxBytes' :  Value(UInt32) = 79; quality=Good time=09-17-2025 14:19:07.935,,Information,Gateway,09/17/2025_14:19:07.935
OPC DEEP,485,Opc UA SDO updated for 'OPCUaServer.sdnp.NumTxFrames' :  Value(UInt32) = 4; quality=Good time=09-17-2025 14:19:07.935,,Information,Gateway,09/17/2025_14:19:07.935
OPC DEEP,486,Opc UA SDO updated for 'OPCUaServer.sdnp.NumTxFragments' :  Value(UInt32) = 4; quality=Good time=09-17-2025 14:19:07.935,,Information,Gateway,09/17/2025_14:19:07.935
OPC DEEP,487,Opc UA SDO updated for 'OPCUaServer.sdnp.L3.NumTxASDUs' :  Value(UInt32) = 4; quality=Good time=09-17-2025 14:19:07.935,,Information,Gateway,09/17/2025_14:19:07.935
DNP,488,...> mdnp       05  ,mdnp,Information,SCL,09/17/2025_14:19:07.935
OPC DEEP,489,Opc UA SDO updated for 'OPCUaServer.mdnp.NumRxBytes' :  Value(UInt32) = 63; quality=Good time=09-17-2025 14:19:07.935,,Information,Gateway,09/17/2025_14:19:07.935
DNP,490,...> mdnp       64 0a 44 03 00 04 00 7c ae  ,mdnp,Information,SCL,09/17/2025_14:19:07.935
OPC DEEP,491,Opc UA SDO updated for 'OPCUaServer.mdnp.NumRxBytes' :  Value(UInt32) = 72; quality=Good time=09-17-2025 14:19:07.935,,Information,Gateway,09/17/2025_14:19:07.935
DNP,492,...> mdnp       c3 c3 81 10 00 ff 58  ,mdnp,Information,SCL,09/17/2025_14:19:07.935
OPC DEEP,493,Opc UA SDO updated for 'OPCUaServer.mdnp.NumRxBytes' :  Value(UInt32) = 79; quality=Good time=09-17-2025 14:19:07.935,,Information,Gateway,09/17/2025_14:19:07.935
OPC DEEP,494,Opc UA SDO updated for 'OPCUaServer.mdnp.NumRxFrames' :  Value(UInt32) = 4; quality=Good time=09-17-2025 14:19:07.935,,Information,Gateway,09/17/2025_14:19:07.935
DNP,495,---> mdnp       Primary Frame - Unconfirmed User Data   ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.935
DNP,496,                LEN(10) DIR(0) PRM(1) FCV(0) FCB(0) DEST(3) SRC(4) ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.935
DNP,497,                05 64 0a 44 03 00 04 00 7c ae  ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.935
DNP,498,                c3 c3 81 10 00 ff 58  ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.935
DNP,499,~~~> mdnp.L4    Transport Header ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.935
DNP,500,                FIR(1) FIN(1) SEQ# 3 ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.935
DNP,501,                c3 c3 81 10 00  ,mdnp.L4,Information,SCL,09/17/2025_14:19:07.935
OPC DEEP,818,Opc UA SDO updated for 'OPCUaServer.MdoUpdateRate' :  Value(UInt32) = 3; quality=Good time=09-17-2025 14:19:12.626,,Information,Gateway,09/17/2025_14:19:12.626
OPC DEEP,819,Opc UA SDO updated for 'OPCUaServer.AverageMdoUpdateRate' :  Value(UInt32) = 2; quality=Good time=09-17-2025 14:19:12.626,,Information,Gateway,09/17/2025_14:19:12.626
OPC DEEP,820,Opc UA SDO updated for 'OPCUaServer.DataBaseUpdateQSize' :  Value(UInt32) = 0; quality=Good time=09-17-2025 14:19:12.626,,Information,Gateway,09/17/2025_14:19:12.626
DNP,821,<+++ mdnp.L4    Build DNP3 Message: ,mdnp.L4,Information,SCL,09/17/2025_14:19:12.626
DNP,822,                Tx Object 60(Class Data)  variation 2  qualifier 0x06(All Points)  ,mdnp.L4,Information,SCL,09/17/2025_14:19:12.626
DNP,823,                Tx Object 60(Class Data)  variation 3  qualifier 0x06(All Points)  ,mdnp.L4,Information,SCL,09/17/2025_14:19:12.626
DNP,824,                Tx Object 60(Class Data)  variation 4  qualifier 0x06(All Points)  ,mdnp.L4,Information,SCL,09/17/2025_14:19:12.626
DNP,825,                Tx Object 60(Class Data)  variation 1  qualifier 0x06(All Points)  ,mdnp.L4,Information,SCL,09/17/2025_14:19:12.626
DNP,826,<+++ mdnp.L4    Insert request in queue: SDG DNP Action sent: CLASS1 CLASS2 CLASS3 CLASS0  ,mdnp.L4,Information,SCL,09/17/2025_14:19:12.626
DNP,827,<=== mdnp.L4    Application Header  Read Request ,mdnp.L4,Information,SCL,09/17/2025_14:19:12.626
DNP,828,                FIR(1) FIN(1) CON(0) UNS(0) SEQ# 7 ,mdnp.L4,Information,SCL,09/17/2025_14:19:12.626
DNP,829,                c7 01 3c 02 06 3c 03 06 3c 04 06 3c 01 06  ,mdnp.L4,Information,SCL,09/17/2025_14:19:12.626
DNP,830,<~~~ mdnp.L4    Transport Header ,mdnp.L4,Information,SCL,09/17/2025_14:19:12.626
DNP,831,                FIR(1) FIN(1) SEQ# 7 ,mdnp.L4,Information,SCL,09/17/2025_14:19:12.626
DNP,832,                c7 c7 01 3c 02 06 3c 03 06 3c 04 06 3c 01 06  ,mdnp.L4,Information,SCL,09/17/2025_14:19:12.626
DNP,833,<--- mdnp       Primary Frame - Unconfirmed User Data   ,mdnp.L4,Information,SCL,09/17/2025_14:19:12.626
DNP,834,                LEN(20) DIR(1) PRM(1) FCV(0) FCB(0) DEST(4) SRC(3) ,mdnp.L4,Information,SCL,09/17/2025_14:19:12.626
DNP,835,                05 64 14 c4 04 00 03 00 c7 17  ,mdnp.L4,Information,SCL,09/17/2025_14:19:12.626
DNP,836,                c7 c7 01 3c 02 06 3c 03 06 3c 04 06 3c 01 06 6b  ,mdnp.L4,Information,SCL,09/17/2025_14:19:12.626
DNP,837,                ae  ,mdnp.L4,Information,SCL,09/17/2025_14:19:12.626
DNP,838,mdnp - 127.0.0.1:20000 - TCP transmit 27 bytes,mdnp,Information,SCL,09/17/2025_14:19:12.626
DNP,839,<... mdnp       05 64 14 c4 04 00 03 00 c7 17 c7 c7 01 3c 02 06  ,mdnp,Information,SCL,09/17/2025_14:19:12.626
DNP,840,                3c 03 06 3c 04 06 3c 01 06 6b ae  ,mdnp,Information,SCL,09/17/2025_14:19:12.626
OPC DEEP,841,Opc UA SDO updated for 'OPCUaServer.mdnp.NumTxBytes' :  Value(UInt32) = 193; quality=Good time=09-17-2025 14:19:12.626,,Information,Gateway,09/17/2025_14:19:12.626
OPC DEEP,842,Opc UA SDO updated for 'OPCUaServer.mdnp.NumTxFrames' :  Value(UInt32) = 8; quality=Good time=09-17-2025 14:19:12.627,,Information,Gateway,09/17/2025_14:19:12.627
OPC DEEP,843,Opc UA SDO updated for 'OPCUaServer.mdnp.NumTxFragments' :  Value(UInt32) = 8; quality=Good time=09-17-2025 14:19:12.627,,Information,Gateway,09/17/2025_14:19:12.627
OPC DEEP,844,Opc UA SDO updated for 'OPCUaServer.mdnp.L4.NumTxASDUs' :  Value(UInt32) = 8; quality=Good time=09-17-2025 14:19:12.627,,Information,Gateway,09/17/2025_14:19:12.627
DNP,845,...> sdnp       05  ,sdnp,Information,SCL,09/17/2025_14:19:12.738
OPC DEEP,846,Opc UA SDO updated for 'OPCUaServer.sdnp.NumRxBytes' :  Value(UInt32) = 167; quality=Good time=09-17-2025 14:19:12.738,,Information,Gateway,09/17/2025_14:19:12.738
DNP,847,...> sdnp       64 14 c4 04 00 03 00 c7 17  ,sdnp,Information,SCL,09/17/2025_14:19:12.738
OPC DEEP,848,Opc UA SDO updated for 'OPCUaServer.sdnp.NumRxBytes' :  Value(UInt32) = 176; quality=Good time=09-17-2025 14:19:12.738,,Information,Gateway,09/17/2025_14:19:12.738
DNP,849,...> sdnp       c7 c7 01 3c 02 06 3c 03 06 3c 04 06 3c 01 06 6b  ,sdnp,Information,SCL,09/17/2025_14:19:12.738
DNP,850,                ae  ,sdnp,Information,SCL,09/17/2025_14:19:12.738
OPC DEEP,851,Opc UA SDO updated for 'OPCUaServer.sdnp.NumRxBytes' :  Value(UInt32) = 193; quality=Good time=09-17-2025 14:19:12.738,,Information,Gateway,09/17/2025_14:19:12.738
OPC DEEP,852,Opc UA SDO updated for 'OPCUaServer.sdnp.NumRxFrames' :  Value(UInt32) = 8; quality=Good time=09-17-2025 14:19:12.738,,Information,Gateway,09/17/2025_14:19:12.738
DNP,853,---> sdnp       Primary Frame - Unconfirmed User Data   ,sdnp.L3,Information,SCL,09/17/2025_14:19:12.738
DNP,854,                LEN(20) DIR(1) PRM(1) FCV(0) FCB(0) DEST(4) SRC(3) ,sdnp.L3,Information,SCL,09/17/2025_14:19:12.738
DNP,855,                05 64 14 c4 04 00 03 00 c7 17  ,sdnp.L3,Information,SCL,09/17/2025_14:19:12.738
DNP,856,                c7 c7 01 3c 02 06 3c 03 06 3c 04 06 3c 01 06 6b  ,sdnp.L3,Information,SCL,09/17/2025_14:19:12.738
DNP,857,                ae  ,sdnp.L3,Information,SCL,09/17/2025_14:19:12.738
DNP,858,~~~> sdnp.L3    Transport Header ,sdnp.L3,Information,SCL,09/17/2025_14:19:12.738
DNP,859,                FIR(1) FIN(1) SEQ# 7 ,sdnp.L3,Information,SCL,09/17/2025_14:19:12.738
DNP,860,                c7 c7 01 3c 02 06 3c 03 06 3c 04 06 3c 01 06  ,sdnp.L3,Information,SCL,09/17/2025_14:19:12.738
OPC DEEP,861,Opc UA SDO updated for 'OPCUaServer.sdnp.NumRxFragments' :  Value(UInt32) = 8; quality=Good time=09-17-2025 14:19:12.738,,Information,Gateway,09/17/2025_14:19:12.738
DNP,862,===> sdnp.L3    Application Header  Read Request ,sdnp.L3,Information,SCL,09/17/2025_14:19:12.738
DNP,863,                FIR(1) FIN(1) CON(0) UNS(0) SEQ# 7 ,sdnp.L3,Information,SCL,09/17/2025_14:19:12.738
DNP,864,                c7 01 3c 02 06 3c 03 06 3c 04 06 3c 01 06  ,sdnp.L3,Information,SCL,09/17/2025_14:19:12.738
DNP,865,                Rx Object 60(Class Data)  variation 2  qualifier 0x06(All Points)  ,sdnp.L3,Information,SCL,09/17/2025_14:19:12.738
DNP,866,<+++ sdnp.L3    Build DNP3 Message: Read Response ,sdnp.L3,Information,SCL,09/17/2025_14:19:12.738
DNP,867,                Rx Object 60(Class Data)  variation 3  qualifier 0x06(All Points)  ,sdnp.L3,Information,SCL,09/17/2025_14:19:12.738
DNP,868,<+++ sdnp.L3    Build DNP3 Message: Read Response ,sdnp.L3,Information,SCL,09/17/2025_14:19:12.738
DNP,869,                Rx Object 60(Class Data)  variation 4  qualifier 0x06(All Points)  ,sdnp.L3,Information,SCL,09/17/2025_14:19:12.738
OPC DEEP,870,Opc UA SDO updated for 'OPCUaServer.sdnp.L3.NumRxASDUs' :  Value(UInt32) = 8; quality=Good time=09-17-2025 14:19:12.738,,Information,Gateway,09/17/2025_14:19:12.738
DNP,871,<+++ sdnp.L3    Build DNP3 Message: Read Response ,sdnp.L3,Information,SCL,09/17/2025_14:19:12.738
DNP,872,                Rx Object 60(Class Data)  variation 1  qualifier 0x06(All Points)  ,sdnp.L3,Information,SCL,09/17/2025_14:19:12.738
DNP,873,<+++ sdnp.L3    Build DNP3 Message: Read Response ,sdnp.L3,Information,SCL,09/17/2025_14:19:12.738
DNP,874,                Tx Object 1(Binary Input)  variation 1  qualifier 0x00(8 Bit Start Stop)  ,sdnp.L3,Information,SCL,09/17/2025_14:19:12.738
DNP,875,                Tx Object 1(Binary Input)  variation 2  qualifier 0x00(8 Bit Start Stop)  ,sdnp.L3,Information,SCL,09/17/2025_14:19:12.738
DNP,876,                      Binary Input 000001 = 0x00 ,sdnp.L3,Information,SCL,09/17/2025_14:19:12.738
DNP,877,                      Binary Input 000002 = 0x00 ,sdnp.L3,Information,SCL,09/17/2025_14:19:12.738
DNP,878,                      Binary Input 000003 = 0x00 ,sdnp.L3,Information,SCL,09/17/2025_14:19:12.738
DNP,879,                      Binary Input 000004 = 0x00 ,sdnp.L3,Information,SCL,09/17/2025_14:19:12.738
DNP,880,                      Binary Input 000005 = 0x00 ,sdnp.L3,Information,SCL,09/17/2025_14:19:12.738
DNP,881,                      Binary Input 000006 = 0x00 ,sdnp.L3,Information,SCL,09/17/2025_14:19:12.738
DNP,882,<+++ sdnp.L3    Insert request in queue: Read Response ,sdnp.L3,Information,SCL,09/17/2025_14:19:12.738
DNP,883,<=== sdnp.L3    Application Header  Response ,sdnp.L3,Information,SCL,09/17/2025_14:19:12.738
DNP,884,                FIR(1) FIN(1) CON(0) UNS(0) SEQ# 7 ,sdnp.L3,Information,SCL,09/17/2025_14:19:12.738
DNP,885,                c7 81 00 00 01 02 00 01 06 00 00 00 00 00 00  ,sdnp.L3,Information,SCL,09/17/2025_14:19:12.739
DNP,886,<~~~ sdnp.L3    Transport Header ,sdnp.L3,Information,SCL,09/17/2025_14:19:12.739
DNP,887,                FIR(1) FIN(1) SEQ# 7 ,sdnp.L3,Information,SCL,09/17/2025_14:19:12.739
DNP,888,                c7 c7 81 00 00 01 02 00 01 06 00 00 00 00 00 00  ,sdnp.L3,Information,SCL,09/17/2025_14:19:12.739
DNP,889,<--- sdnp       Primary Frame - Unconfirmed User Data   ,sdnp.L3,Information,SCL,09/17/2025_14:19:12.739
DNP,890,                LEN(21) DIR(0) PRM(1) FCV(0) FCB(0) DEST(3) SRC(4) ,sdnp.L3,Information,SCL,09/17/2025_14:19:12.739
DNP,891,                05 64 15 44 03 00 04 00 5f 92  ,sdnp.L3,Information,SCL,09/17/2025_14:19:12.739
DNP,892,                c7 c7 81 00 00 01 02 00 01 06 00 00 00 00 00 00  ,sdnp.L3,Information,SCL,09/17/2025_14:19:12.739
DNP,893,                98 db  ,sdnp.L3,Information,SCL,09/17/2025_14:19:12.739
DNP,894,sdnp - 127.0.0.1:20000 - TCP transmit 28 bytes,sdnp,Information,SCL,09/17/2025_14:19:12.739
DNP,895,<... sdnp       05 64 15 44 03 00 04 00 5f 92 c7 c7 81 00 00 01  ,sdnp,Information,SCL,09/17/2025_14:19:12.739
DNP,896,                02 00 01 06 00 00 00 00 00 00 98 db  ,sdnp,Information,SCL,09/17/2025_14:19:12.739
OPC DEEP,897,Opc UA SDO updated for 'OPCUaServer.sdnp.NumTxBytes' :  Value(UInt32) = 180; quality=Good time=09-17-2025 14:19:12.739,,Information,Gateway,09/17/2025_14:19:12.739
OPC DEEP,898,Opc UA SDO updated for 'OPCUaServer.sdnp.NumTxFrames' :  Value(UInt32) = 8; quality=Good time=09-17-2025 14:19:12.739,,Information,Gateway,09/17/2025_14:19:12.739
OPC DEEP,899,Opc UA SDO updated for 'OPCUaServer.sdnp.NumTxFragments' :  Value(UInt32) = 8; quality=Good time=09-17-2025 14:19:12.739,,Information,Gateway,09/17/2025_14:19:12.739
DNP,900,...> mdnp       05  ,mdnp,Information,SCL,09/17/2025_14:19:12.739
OPC DEEP,901,Opc UA SDO updated for 'OPCUaServer.mdnp.NumRxBytes' :  Value(UInt32) = 153; quality=Good time=09-17-2025 14:19:12.739,,Information,Gateway,09/17/2025_14:19:12.739
DNP,902,...> mdnp       64 15 44 03 00 04 00 5f 92  ,mdnp,Information,SCL,09/17/2025_14:19:12.739
OPC DEEP,903,Opc UA SDO updated for 'OPCUaServer.sdnp.L3.NumTxASDUs' :  Value(UInt32) = 8; quality=Good time=09-17-2025 14:19:12.739,,Information,Gateway,09/17/2025_14:19:12.739
OPC DEEP,904,Opc UA SDO updated for 'OPCUaServer.mdnp.NumRxBytes' :  Value(UInt32) = 162; quality=Good time=09-17-2025 14:19:12.739,,Information,Gateway,09/17/2025_14:19:12.739
DNP,905,...> mdnp       c7 c7 81 00 00 01 02 00 01 06 00 00 00 00 00 00  ,mdnp,Information,SCL,09/17/2025_14:19:12.739
DNP,906,                98 db  ,mdnp,Information,SCL,09/17/2025_14:19:12.739
OPC DEEP,907,Opc UA SDO updated for 'OPCUaServer.mdnp.NumRxBytes' :  Value(UInt32) = 180; quality=Good time=09-17-2025 14:19:12.739,,Information,Gateway,09/17/2025_14:19:12.739
OPC DEEP,908,Opc UA SDO updated for 'OPCUaServer.mdnp.NumRxFrames' :  Value(UInt32) = 8; quality=Good time=09-17-2025 14:19:12.739,,Information,Gateway,09/17/2025_14:19:12.739
DNP,909,---> mdnp       Primary Frame - Unconfirmed User Data   ,mdnp.L4,Information,SCL,09/17/2025_14:19:12.739
DNP,910,                LEN(21) DIR(0) PRM(1) FCV(0) FCB(0) DEST(3) SRC(4) ,mdnp.L4,Information,SCL,09/17/2025_14:19:12.739
DNP,911,                05 64 15 44 03 00 04 00 5f 92  ,mdnp.L4,Information,SCL,09/17/2025_14:19:12.739
DNP,912,                c7 c7 81 00 00 01 02 00 01 06 00 00 00 00 00 00  ,mdnp.L4,Information,SCL,09/17/2025_14:19:12.739
DNP,913,                98 db  ,mdnp.L4,Information,SCL,09/17/2025_14:19:12.739
DNP,914,~~~> mdnp.L4    Transport Header ,mdnp.L4,Information,SCL,09/17/2025_14:19:12.739
DNP,915,                FIR(1) FIN(1) SEQ# 7 ,mdnp.L4,Information,SCL,09/17/2025_14:19:12.739
DNP,916,                c7 c7 81 00 00 01 02 00 01 06 00 00 00 00 00 00  ,mdnp.L4,Information,SCL,09/17/2025_14:19:12.739
OPC DEEP,917,Opc UA SDO updated for 'OPCUaServer.mdnp.NumRxFragments' :  Value(UInt32) = 8; quality=Good time=09-17-2025 14:19:12.739,,Information,Gateway,09/17/2025_14:19:12.739
OPC DEEP,918,Opc UA SDO updated for 'OPCUaServer.MdoUpdateRate' :  Value(UInt32) = 22; quality=Good time=09-17-2025 14:19:13.615,,Information,Gateway,09/17/2025_14:19:13.615
DNP,919,===> mdnp.L4    Application Header  Response ,mdnp.L4,Information,SCL,09/17/2025_14:19:18.456
DNP,920,                FIR(1) FIN(1) CON(0) UNS(0) SEQ# 7 ,mdnp.L4,Information,SCL,09/17/2025_14:19:18.456
DNP,921,                c7 81 00 00 01 02 00 01 06 00 00 00 00 00 00  ,mdnp.L4,Information,SCL,09/17/2025_14:19:18.456
DNP,922,+++> mdnp.L4    Process response to request: SDG DNP Action sent: CLASS1 CLASS2 CLASS3 CLASS0  ,mdnp.L4,Information,SCL,09/17/2025_14:19:18.456
OPC DEEP,923,Opc UA SDO updated for 'OPCUaServer.AverageMdoUpdateRate' :  Value(UInt32) = 6; quality=Good time=09-17-2025 14:19:18.456,,Information,Gateway,09/17/2025_14:19:18.456
OPC DEEP,925,Opc UA SDO updated for 'OPCUaServer.mdnp.L4.NumRxASDUs' :  Value(UInt32) = 8; quality=Good time=09-17-2025 14:19:18.456,,Information,Gateway,09/17/2025_14:19:18.456
OPC DEEP,926,Opc UA SDO updated for 'OPCUaServer.DataBaseUpdateQSize' :  Value(UInt32) = 0; quality=Good time=09-17-2025 14:19:18.456,,Information,Gateway,09/17/2025_14:19:18.456
DNP,924,                Rx Object 1(Binary Input)  variation 2  qualifier 0x00(8 Bit Start Stop)  ,mdnp.L4,Information,SCL,09/17/2025_14:19:18.456
DNP,927,                      Binary Input 000001 = 0x00 ,mdnp.L4,Information,SCL,09/17/2025_14:19:18.456
DNP,929,                      Binary Input 000002 = 0x00 ,mdnp.L4,Information,SCL,09/17/2025_14:19:18.456
DNP,930,                      Binary Input 000003 = 0x00 ,mdnp.L4,Information,SCL,09/17/2025_14:19:18.456
OPC DEEP,928,Opc UA SDO updated for 'OPCUaServer.MdoUpdateRate' :  Value(UInt32) = 0; quality=Good time=09-17-2025 14:19:18.456,,Information,Gateway,09/17/2025_14:19:18.456
DNP,931,                      Binary Input 000004 = 0x00 ,mdnp.L4,Information,SCL,09/17/2025_14:19:18.457
OPC DEEP,932,Opc UA SDO updated for 'OPCUaServer.AverageMdoUpdateRate' :  Value(UInt32) = 5; quality=Good time=09-17-2025 14:19:18.457,,Information,Gateway,09/17/2025_14:19:18.457
DNP,933,                      Binary Input 000005 = 0x00 ,mdnp.L4,Information,SCL,09/17/2025_14:19:18.457
DNP,934,                      Binary Input 000006 = 0x00 ,mdnp.L4,Information,SCL,09/17/2025_14:19:18.457
OPC DEEP,935,Opc UA SDO updated for 'OPCUaServer.DataBaseUpdateQSize' :  Value(UInt32) = 4; quality=Good time=09-17-2025 14:19:18.457,,Information,Gateway,09/17/2025_14:19:18.457
OPC DEEP,936,Opc UA SDO updated for 'OPCUaServer.MdoUpdateRate' :  Value(UInt32) = 0; quality=Good time=09-17-2025 14:19:18.457,,Information,Gateway,09/17/2025_14:19:18.457
OPC DEEP,937,Opc UA SDO updated for 'OPCUaServer.AverageMdoUpdateRate' :  Value(UInt32) = 4; quality=Good time=09-17-2025 14:19:18.457,,Information,Gateway,09/17/2025_14:19:18.457
General,938,SCL GTWmain::ProcessInputs() is taking longer than 100(ms) time to process is 5703(ms) ,,Error,Gateway,09/17/2025_14:19:18.457
OPC DEEP,939,Opc UA SDO updated for 'OPCUaServer.DataBaseUpdateQSize' :  Value(UInt32) = 6; quality=Good time=09-17-2025 14:19:18.457,,Information,Gateway,09/17/2025_14:19:18.457
OPC DEEP,940,Opc UA SDO updated for 'OPCUaServer.MdoUpdateRate' :  Value(UInt32) = 0; quality=Good time=09-17-2025 14:19:18.457,,Information,Gateway,09/17/2025_14:19:18.457
OPC DEEP,941,Opc UA SDO updated for 'OPCUaServer.AverageMdoUpdateRate' :  Value(UInt32) = 4; quality=Good time=09-17-2025 14:19:18.457,,Information,Gateway,09/17/2025_14:19:18.457
OPC DEEP,942,Opc UA SDO updated for 'OPCUaServer.DataBaseUpdateQSize' :  Value(UInt32) = 6; quality=Good time=09-17-2025 14:19:18.457,,Information,Gateway,09/17/2025_14:19:18.457
OPC DEEP,943,Opc UA SDO updated for 'OPCUaServer.MdoUpdateRate' :  Value(UInt32) = 0; quality=Good time=09-17-2025 14:19:18.457,,Information,Gateway,09/17/2025_14:19:18.457
OPC DEEP,944,Opc UA SDO updated for 'OPCUaServer.AverageMdoUpdateRate' :  Value(UInt32) = 3; quality=Good time=09-17-2025 14:19:18.457,,Information,Gateway,09/17/2025_14:19:18.457
OPC DEEP,945,Opc UA SDO updated for 'OPCUaServer.DataBaseUpdateQSize' :  Value(UInt32) = 6; quality=Good time=09-17-2025 14:19:18.457,,Information,Gateway,09/17/2025_14:19:18.457
DNP,946,<+++ mdnp.L4    Build DNP3 Message: ,mdnp.L4,Information,SCL,09/17/2025_14:19:18.457
DNP,947,                Tx Object 60(Class Data)  variation 2  qualifier 0x06(All Points)  ,mdnp.L4,Information,SCL,09/17/2025_14:19:18.457
DNP,948,                Tx Object 60(Class Data)  variation 3  qualifier 0x06(All Points)  ,mdnp.L4,Information,SCL,09/17/2025_14:19:18.457
DNP,949,                Tx Object 60(Class Data)  variation 4  qualifier 0x06(All Points)  ,mdnp.L4,Information,SCL,09/17/2025_14:19:18.457
DNP,950,                Tx Object 60(Class Data)  variation 1  qualifier 0x06(All Points)  ,mdnp.L4,Information,SCL,09/17/2025_14:19:18.457
DNP,951,<+++ mdnp.L4    Insert request in queue: SDG DNP Action sent: CLASS1 CLASS2 CLASS3 CLASS0  ,mdnp.L4,Information,SCL,09/17/2025_14:19:18.457
DNP,952,<=== mdnp.L4    Application Header  Read Request ,mdnp.L4,Information,SCL,09/17/2025_14:19:18.457
DNP,953,                FIR(1) FIN(1) CON(0) UNS(0) SEQ# 8 ,mdnp.L4,Information,SCL,09/17/2025_14:19:18.457
DNP,954,                c8 01 3c 02 06 3c 03 06 3c 04 06 3c 01 06  ,mdnp.L4,Information,SCL,09/17/2025_14:19:18.457
DNP,955,<~~~ mdnp.L4    Transport Header ,mdnp.L4,Information,SCL,09/17/2025_14:19:18.457
DNP,956,                FIR(1) FIN(1) SEQ# 8 ,mdnp.L4,Information,SCL,09/17/2025_14:19:18.457
DNP,957,                c8 c8 01 3c 02 06 3c 03 06 3c 04 06 3c 01 06  ,mdnp.L4,Information,SCL,09/17/2025_14:19:18.457
DNP,958,<--- mdnp       Primary Frame - Unconfirmed User Data   ,mdnp.L4,Information,SCL,09/17/2025_14:19:18.457
DNP,959,                LEN(20) DIR(1) PRM(1) FCV(0) FCB(0) DEST(4) SRC(3) ,mdnp.L4,Information,SCL,09/17/2025_14:19:18.457
DNP,960,                05 64 14 c4 04 00 03 00 c7 17  ,mdnp.L4,Information,SCL,09/17/2025_14:19:18.457
DNP,961,                c8 c8 01 3c 02 06 3c 03 06 3c 04 06 3c 01 06 38  ,mdnp.L4,Information,SCL,09/17/2025_14:19:18.457
DNP,962,                4c  ,mdnp.L4,Information,SCL,09/17/2025_14:19:18.457
DNP,963,mdnp - 127.0.0.1:20000 - TCP transmit 27 bytes,mdnp,Information,SCL,09/17/2025_14:19:18.457
DNP,964,<... mdnp       05 64 14 c4 04 00 03 00 c7 17 c8 c8 01 3c 02 06  ,mdnp,Information,SCL,09/17/2025_14:19:18.457
DNP,965,                3c 03 06 3c 04 06 3c 01 06 38 4c  ,mdnp,Information,SCL,09/17/2025_14:19:18.457
OPC DEEP,966,Opc UA SDO updated for 'OPCUaServer.mdnp.NumTxBytes' :  Value(UInt32) = 220; quality=Good time=09-17-2025 14:19:18.457,,Information,Gateway,09/17/2025_14:19:18.457
OPC DEEP,967,Opc UA SDO updated for 'OPCUaServer.mdnp.NumTxFrames' :  Value(UInt32) = 9; quality=Good time=09-17-2025 14:19:18.457,,Information,Gateway,09/17/2025_14:19:18.457
OPC DEEP,968,Opc UA SDO updated for 'OPCUaServer.mdnp.NumTxFragments' :  Value(UInt32) = 9; quality=Good time=09-17-2025 14:19:18.457,,Information,Gateway,09/17/2025_14:19:18.458
OPC DEEP,969,Opc UA SDO updated for 'OPCUaServer.mdnp.L4.NumTxASDUs' :  Value(UInt32) = 9; quality=Good time=09-17-2025 14:19:18.458,,Information,Gateway,09/17/2025_14:19:18.458
OPC DEEP,970,Opc UA SDO updated for 'OPCUaServer.mdnp.L4.T1.P1' :  Value(Boolean) = False; quality=Bad time=09-17-2025 14:19:18.459,,Information,Gateway,09/17/2025_14:19:18.459
OPC DEEP,971,Opc UA SDO updated for 'OPCUaServer.mdnp.L4.T1.P2' :  Value(Boolean) = False; quality=Bad time=09-17-2025 14:19:18.459,,Information,Gateway,09/17/2025_14:19:18.459
OPC DEEP,972,Opc UA SDO updated for 'OPCUaServer.mdnp.L4.T1.P3' :  Value(Boolean) = False; quality=Bad time=09-17-2025 14:19:18.459,,Information,Gateway,09/17/2025_14:19:18.459
OPC DEEP,973,Opc UA SDO updated for 'OPCUaServer.mdnp.L4.T1.P4' :  Value(Boolean) = False; quality=Bad time=09-17-2025 14:19:18.459,,Information,Gateway,09/17/2025_14:19:18.459
OPC DEEP,974,Opc UA SDO updated for 'OPCUaServer.mdnp.L4.T1.P5' :  Value(Boolean) = False; quality=Bad time=09-17-2025 14:19:18.459,,Information,Gateway,09/17/2025_14:19:18.459
OPC DEEP,975,Opc UA SDO updated for 'OPCUaServer.mdnp.L4.T1.P6' :  Value(Boolean) = False; quality=Bad time=09-17-2025 14:19:18.459,,Information,Gateway,09/17/2025_14:19:18.459
HTTPS,976,Web server error: request: Request from ::ffff:************:64870 GET /rest/node_page_info 1.1 Connection: keep-alive User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:143.0) Gecko/20100101 Firefox/143.0 Host: ************:58080 Authorization: 6798bade-20cf-4a0e-4927-6f49d3dc4727 Accept: application/json Accept-Language: en-US en;q=0.5 Accept-Encoding: gzip  deflate  br  zstd Origin: https://************:58090 Referer: https://************:58090/ Sec-Fetch-Mode: cors Sec-Fetch-Dest: empty Sec-Fetch-Site: same-site  error_code: 10053 error_message: An established connection was aborted by the software in your host machine.,,Error,Gateway,09/17/2025_14:19:18.462
DNP,977,...> sdnp       05  ,sdnp,Information,SCL,09/17/2025_14:19:18.476
OPC DEEP,978,Opc UA SDO updated for 'OPCUaServer.sdnp.NumRxBytes' :  Value(UInt32) = 194; quality=Good time=09-17-2025 14:19:18.476,,Information,Gateway,09/17/2025_14:19:18.476
DNP,979,...> sdnp       64 14 c4 04 00 03 00 c7 17  ,sdnp,Information,SCL,09/17/2025_14:19:18.476
OPC DEEP,980,Opc UA SDO updated for 'OPCUaServer.sdnp.NumRxBytes' :  Value(UInt32) = 203; quality=Good time=09-17-2025 14:19:18.476,,Information,Gateway,09/17/2025_14:19:18.476
DNP,981,...> sdnp       c8 c8 01 3c 02 06 3c 03 06 3c 04 06 3c 01 06 38  ,sdnp,Information,SCL,09/17/2025_14:19:18.476
DNP,982,                4c  ,sdnp,Information,SCL,09/17/2025_14:19:18.476
OPC DEEP,983,Opc UA SDO updated for 'OPCUaServer.sdnp.NumRxBytes' :  Value(UInt32) = 220; quality=Good time=09-17-2025 14:19:18.476,,Information,Gateway,09/17/2025_14:19:18.476
OPC DEEP,984,Opc UA SDO updated for 'OPCUaServer.sdnp.NumRxFrames' :  Value(UInt32) = 9; quality=Good time=09-17-2025 14:19:18.476,,Information,Gateway,09/17/2025_14:19:18.476
DNP,985,---> sdnp       Primary Frame - Unconfirmed User Data   ,sdnp.L3,Information,SCL,09/17/2025_14:19:18.476
DNP,986,                LEN(20) DIR(1) PRM(1) FCV(0) FCB(0) DEST(4) SRC(3) ,sdnp.L3,Information,SCL,09/17/2025_14:19:18.476
DNP,987,                05 64 14 c4 04 00 03 00 c7 17  ,sdnp.L3,Information,SCL,09/17/2025_14:19:18.476
DNP,988,                c8 c8 01 3c 02 06 3c 03 06 3c 04 06 3c 01 06 38  ,sdnp.L3,Information,SCL,09/17/2025_14:19:18.476
DNP,989,                4c  ,sdnp.L3,Information,SCL,09/17/2025_14:19:18.476
DNP,990,~~~> sdnp.L3    Transport Header ,sdnp.L3,Information,SCL,09/17/2025_14:19:18.476
DNP,991,                FIR(1) FIN(1) SEQ# 8 ,sdnp.L3,Information,SCL,09/17/2025_14:19:18.476
DNP,992,                c8 c8 01 3c 02 06 3c 03 06 3c 04 06 3c 01 06  ,sdnp.L3,Information,SCL,09/17/2025_14:19:18.476
OPC DEEP,993,Opc UA SDO updated for 'OPCUaServer.sdnp.NumRxFragments' :  Value(UInt32) = 9; quality=Good time=09-17-2025 14:19:18.476,,Information,Gateway,09/17/2025_14:19:18.476
OPC DEEP,994,Opc UA SDO updated for 'OPCUaServer.sdnp.L3.NumRxASDUs' :  Value(UInt32) = 9; quality=Good time=09-17-2025 14:19:18.476,,Information,Gateway,09/17/2025_14:19:18.476
DNP,995,===> sdnp.L3    Application Header  Read Request ,sdnp.L3,Information,SCL,09/17/2025_14:19:18.476
DNP,996,                FIR(1) FIN(1) CON(0) UNS(0) SEQ# 8 ,sdnp.L3,Information,SCL,09/17/2025_14:19:18.476
DNP,997,                c8 01 3c 02 06 3c 03 06 3c 04 06 3c 01 06  ,sdnp.L3,Information,SCL,09/17/2025_14:19:18.476
DNP,998,                Rx Object 60(Class Data)  variation 2  qualifier 0x06(All Points)  ,sdnp.L3,Information,SCL,09/17/2025_14:19:18.476
DNP,999,<+++ sdnp.L3    Build DNP3 Message: Read Response ,sdnp.L3,Information,SCL,09/17/2025_14:19:18.476
DNP,1000,                Rx Object 60(Class Data)  variation 3  qualifier 0x06(All Points)  ,sdnp.L3,Information,SCL,09/17/2025_14:19:18.476
DNP,1001,<+++ sdnp.L3    Build DNP3 Message: Read Response ,sdnp.L3,Information,SCL,09/17/2025_14:19:18.476
DNP,1002,                Rx Object 60(Class Data)  variation 4  qualifier 0x06(All Points)  ,sdnp.L3,Information,SCL,09/17/2025_14:19:18.476
DNP,1003,<+++ sdnp.L3    Build DNP3 Message: Read Response ,sdnp.L3,Information,SCL,09/17/2025_14:19:18.476
DNP,1004,                Rx Object 60(Class Data)  variation 1  qualifier 0x06(All Points)  ,sdnp.L3,Information,SCL,09/17/2025_14:19:18.476
DNP,1005,<+++ sdnp.L3    Build DNP3 Message: Read Response ,sdnp.L3,Information,SCL,09/17/2025_14:19:18.476
DNP,1006,                Tx Object 1(Binary Input)  variation 1  qualifier 0x00(8 Bit Start Stop)  ,sdnp.L3,Information,SCL,09/17/2025_14:19:18.476
DNP,1007,                Tx Object 1(Binary Input)  variation 2  qualifier 0x00(8 Bit Start Stop)  ,sdnp.L3,Information,SCL,09/17/2025_14:19:18.476
DNP,1008,                      Binary Input 000001 = 0x00 ,sdnp.L3,Information,SCL,09/17/2025_14:19:18.476
DNP,1009,                      Binary Input 000002 = 0x00 ,sdnp.L3,Information,SCL,09/17/2025_14:19:18.476
DNP,1010,                      Binary Input 000003 = 0x00 ,sdnp.L3,Information,SCL,09/17/2025_14:19:18.477
DNP,1011,                      Binary Input 000004 = 0x00 ,sdnp.L3,Information,SCL,09/17/2025_14:19:18.477
DNP,1012,                      Binary Input 000005 = 0x00 ,sdnp.L3,Information,SCL,09/17/2025_14:19:18.477
DNP,1013,                      Binary Input 000006 = 0x00 ,sdnp.L3,Information,SCL,09/17/2025_14:19:18.477
DNP,1014,<+++ sdnp.L3    Insert request in queue: Read Response ,sdnp.L3,Information,SCL,09/17/2025_14:19:18.477
DNP,1015,<=== sdnp.L3    Application Header  Response ,sdnp.L3,Information,SCL,09/17/2025_14:19:18.477
DNP,1016,                FIR(1) FIN(1) CON(0) UNS(0) SEQ# 8 ,sdnp.L3,Information,SCL,09/17/2025_14:19:18.477
DNP,1017,                c8 81 00 00 01 02 00 01 06 00 00 00 00 00 00  ,sdnp.L3,Information,SCL,09/17/2025_14:19:18.477
