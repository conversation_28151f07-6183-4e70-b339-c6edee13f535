# Core dependencies for simulated power system devices framework
# CR-010: Comprehensive power system device simulation with enhanced protocol communication

# Numerical computing for waveform generation
numpy>=1.24.0

# HTTP client/server for protocol communication and file transfer
aiohttp>=3.8.0

# SSH/SFTP support for secure file transfer
paramiko>=3.0.0

# Async file I/O operations
aiofiles>=23.0.0

# SSL certificate verification
certifi>=2022.12.7

# CRC calculation for protocol implementations
crc16>=0.1.1

# Protocol parsing and encoding
pyasn1>=0.4.8
pyasn1-modules>=0.2.8

# Async I/O support (included in Python 3.11+)
# asyncio - built-in

# Data structures and utilities
dataclasses>=0.6  # For Python < 3.7 compatibility (built-in for 3.7+)

# JSON handling (built-in)
# json - built-in

# Logging (built-in) 
# logging - built-in

# Date/time handling (built-in)
# datetime - built-in

# Path handling (built-in)
# pathlib - built-in

# Random number generation (built-in)
# random - built-in

# Regular expressions (built-in)
# re - built-in

# System utilities (built-in)
# os - built-in
# sys - built-in

# Struct packing/unpacking (built-in)
# struct - built-in

# Socket programming (built-in)
# socket - built-in

# Enum support (built-in for Python 3.4+)
# enum - built-in

# Type hints (built-in for Python 3.5+)
# typing - built-in

# UUID generation (built-in)
# uuid - built-in

# Optional dependencies for enhanced functionality

# For advanced mathematical operations
scipy>=1.10.0  # Optional: for advanced signal processing

# For data analysis and manipulation
pandas>=2.0.0  # Optional: for measurement data analysis

# For plotting and visualization
matplotlib>=3.6.0  # Optional: for waveform visualization

# For configuration file parsing
pyyaml>=6.0  # Optional: for YAML configuration support

# For enhanced logging
colorlog>=6.7.0  # Optional: for colored console logging

# For performance monitoring
psutil>=5.9.0  # Optional: for system resource monitoring

# For network communication (moved to core dependencies above)
# aiohttp>=3.8.0  # Now required for protocol communication

# For message queuing
pika>=1.3.0  # Optional: for RabbitMQ integration

# For database connectivity
asyncpg>=0.28.0  # Optional: for PostgreSQL async connectivity
aiosqlite>=0.19.0  # Optional: for SQLite async connectivity

# For testing
pytest>=7.0.0  # Optional: for unit testing
pytest-asyncio>=0.21.0  # Optional: for async test support
pytest-cov>=4.0.0  # Optional: for test coverage

# For development
black>=23.0.0  # Optional: for code formatting
flake8>=6.0.0  # Optional: for code linting
mypy>=1.0.0  # Optional: for type checking

# For documentation
sphinx>=6.0.0  # Optional: for documentation generation
sphinx-rtd-theme>=1.2.0  # Optional: for documentation theme
