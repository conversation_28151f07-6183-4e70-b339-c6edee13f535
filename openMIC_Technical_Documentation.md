# openMIC - Meter Information Collector
## Technical Documentation & Python Container Implementation Guide

### Overview

openMIC (Meter Information Collector) is a comprehensive data acquisition and management system designed to interrogate multiple vendors' Digital Fault Recorders (DFRs) and other power system monitoring devices. Originally built by the Grid Protection Alliance (GPA) as a Windows/.NET application, this document covers both the existing system architecture and the proposed Python-based containerized replacement.

### Current System (Windows/.NET)

The existing openMIC provides automated data collection, scheduling, and web-based management capabilities for power system monitoring infrastructure, built on the Grid Solutions Framework (GSF) and Web Solutions Framework (WSF).

### Proposed Python Container System

The new implementation will be a completely independent Python-based containerized system that:
- **Replaces openMIC functionality** without any dependencies on existing codebase
- **Integrates with Triangle MicroWorks SCADA Data Gateway** for protocol handling
- **Serves as STTP data provider** for downstream consumers
- **Focuses on file collection and data fusion** in a cloud-native architecture

### System Architecture Comparison

#### Current openMIC Architecture (Windows/.NET)

openMIC follows a service-oriented architecture with the following key components:

**Core Components**:
1. **Configuration Database** - SQL Server-based storage for system configuration
2. **openMIC Service** - The automation engine that handles device communication
3. **Web Management Interface** - Browser-based configuration and monitoring
4. **GSF Admin Console** - Administrative tools
5. **File Server/Share** - Storage for collected data files
6. **Installation Package** - Automated deployment system

**Technology Stack**:
- **.NET Framework 4.6+** - Core runtime environment
- **Grid Solutions Framework (GSF)** - Foundation libraries
- **Web Solutions Framework (WSF)** - Web interface framework
- **SignalR** - Real-time web communication
- **SQL Server/MySQL/Oracle/SQLite** - Database support
- **ASP.NET Core** - Web application framework

#### Proposed Python Container Architecture

**New Architecture Overview**:
```
Field Devices → Triangle MicroWorks SCADA Gateway → Python File Collector → STTP Consumers
                          ↓                              ↓                      ↓
                   Protocol Handling              File Collection &        Historians
                   (DNP3, Modbus, FTP)           Data Fusion &            Analytics
                   Device Communication          STTP Publishing          Control Centers
```

**Core Components**:
1. **Python Container Application** - FastAPI-based microservice
2. **Triangle MicroWorks Integration** - SCADA gateway coordination
3. **STTP Server** - IEEE P2664 data publishing server
4. **File Collection Engine** - Multi-protocol file gathering
5. **Data Fusion Service** - Real-time and file data correlation
6. **PostgreSQL Database** - Configuration and operational data storage

**Technology Stack**:
- **Python 3.11+** - Modern runtime with asyncio support
- **FastAPI** - High-performance web framework
- **PostgreSQL** - Enterprise database with JSON support
- **Redis** - Caching and task queuing
- **Docker/Kubernetes** - Container orchestration
- **Prometheus/Grafana** - Monitoring and observability

### Communication Protocols

#### Current openMIC Communication Methods

openMIC supports multiple communication methods for device interrogation:

**Network-Based Communication**:
- **FTP over IP** - File transfer for event, configuration, and trending data
- **Modbus over IP** - Real-time streaming data acquisition
- **SFTP** - Secure file transfer protocol
- **HTTP/HTTPS** - Web-based device communication

**Serial Communication**:
- **Modbus over Serial** - Direct serial connection for streaming data
- **Modem RAS with FTP** - Dial-up connections for remote devices

#### Python Container Communication Architecture

**Protocol Delegation Model**:
- **Triangle MicroWorks Handles**: ALL real-time device protocols
  - DNP3 (IEEE 1815) - SCADA communication
  - Modbus TCP/RTU - Industrial device communication
  - IEC 61850 - Substation automation
  - FTP/SFTP - File transfer coordination
  - HTTP/HTTPS - Web-based device interfaces

**Python Container Responsibilities**:
- **File Collection Coordination** - Triggered by Triangle MicroWorks
- **STTP Publishing** - IEEE P2664 data provider for downstream systems
- **Data Fusion** - Combine real-time data with collected files
- **No Direct Device Communication** - All protocols delegated to Triangle MicroWorks

**Key Architectural Benefit**:
The Python container focuses on data processing and publishing while Triangle MicroWorks handles the complexity of device protocol implementations, providing better separation of concerns and easier maintenance.

### STTP (Streaming Telemetry Transport Protocol) Implementation

#### STTP Server Architecture

The Python container implements a custom STTP server based on IEEE P2664 specifications:

**Core STTP Features**:
- **Publisher/Server Role**: Provides data TO downstream consumers (not a consumer)
- **Real-time Streaming**: High-frequency measurement data publishing
- **File Event Publishing**: Notifications for collected, processed, and archived files
- **Metadata Exchange**: IEEE C37.118 and IEC 61850 data model support
- **Client Management**: Connection authentication and subscription filtering

**Performance Specifications**:
- **Throughput**: 100,000+ measurements per second
- **Concurrent Clients**: 1000+ simultaneous STTP connections
- **Latency**: <100ms for real-time data publishing
- **Compression**: TSSC (Time-Series Special Compression) algorithm
- **Transport**: TCP with optional TLS 1.3 encryption

**Data Models Published via STTP**:
```python
# Real-time measurement data
{
    "measurement_id": "DEVICE_001.MW",
    "timestamp": "2023-12-15T10:30:00.123Z",
    "value": 125.67,
    "quality": "GOOD",
    "source": "triangle_microworks"
}

# File event data
{
    "event_type": "file_collected",
    "device_id": "DEVICE_001",
    "file_path": "/data/device_001/event_20231215.cfg",
    "file_size": 1024576,
    "checksum": "sha256:abc123...",
    "timestamp": "2023-12-15T10:30:00Z",
    "metadata": {
        "format": "COMTRADE",
        "trigger_type": "fault",
        "channels": 12
    }
}
```

**STTP Client Authentication**:
- **Certificate-based**: X.509 certificates for client authentication
- **Subscription Filtering**: Clients can filter data by device, measurement type, or custom criteria
- **Connection Monitoring**: Real-time client status and performance metrics
- **Graceful Degradation**: Automatic client reconnection and data buffering

### Database Schema

The system uses a comprehensive database schema with the following key tables:

#### Core Configuration Tables
- **Device** - Device registry with connection parameters
- **ConnectionProfile** - Communication profile definitions
- **ConnectionProfileTask** - Scheduled task configurations
- **Company** - Organization management
- **Vendor/VendorDevice** - Device type definitions

#### Operational Tables
- **DownloadedFile** - File transfer tracking
- **StatusLog** - System status and events
- **AuditLog** - Change tracking and auditing
- **ErrorLog** - Error and exception logging
- **Setting** - System configuration parameters

#### Data Management Tables
- **Measurement** - Real-time measurement definitions
- **MeasurementValue** - Time-series data storage
- **Historian** - Historical data management
- **OutputMirror** - Data mirroring configuration

### Device Support

openMIC provides specialized support for various device types:

#### Supported Vendors
- **Dranetz** - Power quality analyzers
- **PQube** - Power quality monitoring devices
- **iGrid** - Smart grid monitoring equipment
- **Generic Modbus** - Any Modbus-compatible device

#### Device Communication Features
- **Scheduled Interrogation** - Configurable polling schedules
- **Real-time Streaming** - Continuous data acquisition
- **File-based Collection** - Automated file retrieval
- **Configuration Management** - Remote device configuration
- **Status Monitoring** - Device health and connectivity tracking

### Web Management Interface

The web-based management system provides:

#### Configuration Management
- **Device Management** - Add, configure, and monitor devices
- **Schedule Management** - Define interrogation schedules using cron syntax
- **Connection Profiles** - Reusable communication templates
- **User Management** - Role-based access control
- **Company Management** - Multi-tenant organization support

#### Monitoring and Status
- **Real-time Dashboard** - System status overview
- **Device Status** - Individual device health monitoring
- **File Transfer Status** - Download progress tracking
- **Performance Statistics** - System performance metrics
- **Alarm Management** - Alert configuration and monitoring

#### Data Visualization
- **Graph Measurements** - Real-time data plotting
- **Historical Trending** - Time-series data analysis
- **Status Lights** - Visual system health indicators
- **Progress Updates** - Operation status tracking

### File Management System

openMIC includes a sophisticated file mirroring system:

#### Mirror Types
- **FTP Mirror** - FTP-based file synchronization
- **SFTP Mirror** - Secure FTP file transfer
- **UNC Mirror** - Network share file copying
- **File System Mirror** - Local file system operations

#### Features
- **Automatic Organization** - Files organized by device and date
- **Duplicate Detection** - Prevents redundant file transfers
- **Error Recovery** - Robust error handling and retry logic
- **Progress Tracking** - Real-time transfer status monitoring

### Security Features

#### Authentication and Authorization
- **Role-based Access Control** - Granular permission management
- **User Account Management** - Centralized user administration
- **Security Groups** - Group-based permission assignment
- **Session Management** - Secure web session handling

#### Data Protection
- **Encrypted Communications** - HTTPS/TLS support
- **Audit Logging** - Complete change tracking
- **Database Security** - Parameterized queries and SQL injection protection
- **File System Security** - Controlled file access permissions

### Installation and Deployment

#### System Requirements
- **Operating System**: 64-bit Windows 7 or newer
- **.NET Framework**: 4.6 or higher
- **Database**: SQL Server (recommended), MySQL, Oracle, or SQLite
- **Memory**: Minimum 4GB RAM (8GB+ recommended)
- **Storage**: Adequate space for collected data files

#### Installation Process
1. Download the latest release or nightly build
2. Run Setup.exe with administrative privileges
3. Follow the installation wizard
4. Configure database connection
5. Set up initial user accounts
6. Configure device connections

### Configuration Management

#### Global Settings
The system uses a comprehensive settings framework with:
- **Default Values** - System-provided defaults
- **User Overrides** - Customizable parameters
- **Environment-specific** - Development/production configurations
- **Runtime Updates** - Dynamic configuration changes

#### Connection Profiles
Reusable communication templates that define:
- **Protocol Settings** - Communication parameters
- **Authentication** - Credentials and security settings
- **Scheduling** - Default interrogation schedules
- **File Handling** - Data organization rules

### Performance and Scalability

#### Optimization Features
- **Asynchronous Operations** - Non-blocking I/O operations
- **Connection Pooling** - Efficient resource utilization
- **Caching** - Intelligent data caching strategies
- **Load Balancing** - Distributed processing capabilities

#### Monitoring and Diagnostics
- **Performance Counters** - System performance metrics
- **Health Checks** - Automated system health monitoring
- **Logging Framework** - Comprehensive diagnostic logging
- **Error Reporting** - Automated error notification

### Integration Capabilities

#### Data Export
- **Real-time Streaming** - Live data feeds
- **Batch Export** - Scheduled data exports
- **API Access** - RESTful web services
- **File-based Integration** - Standard file formats

#### External Systems
- **Historian Integration** - Time-series database connectivity
- **SCADA Systems** - Supervisory control integration
- **Analytics Platforms** - Data analysis tool connectivity
- **Reporting Systems** - Automated report generation

### Maintenance and Support

#### Automated Maintenance
- **Database Cleanup** - Automated data archival
- **Log Rotation** - Automatic log file management
- **Health Monitoring** - Proactive issue detection
- **Update Management** - Automated software updates

#### Support Resources
- **Documentation** - Comprehensive user guides
- **Community Forum** - User discussion and support
- **Wiki** - Collaborative documentation
- **Issue Tracking** - Bug reporting and feature requests

### License and Legal

openMIC is released under the MIT License, providing:
- **Open Source** - Full source code availability
- **Commercial Use** - Unrestricted commercial deployment
- **Modification Rights** - Freedom to customize and extend
- **Distribution Rights** - Ability to redistribute

### Development and Contribution

#### Development Environment
- **Visual Studio** - Primary development IDE
- **Git** - Version control system
- **NuGet** - Package management
- **MSBuild** - Build automation

#### Contribution Guidelines
- **Coding Standards** - GPA coding guidelines compliance
- **Pull Requests** - Structured contribution process
- **Testing Requirements** - Comprehensive test coverage
- **Documentation** - Inline and external documentation

## Python Container Implementation Summary

### Key Architectural Decisions

**Independence from OpenMIC**:
- Zero dependencies on existing OpenMIC codebase or Grid Protection Alliance libraries
- Complete rewrite using modern Python technologies and cloud-native patterns
- Independent database schema and data models

**Triangle MicroWorks Integration**:
- All real-time device protocols (DNP3, Modbus, FTP, etc.) handled by Triangle MicroWorks
- Python container coordinates file collection without direct device communication
- Bidirectional status synchronization and event-driven task triggering

**STTP Data Provider Role**:
- Serves as IEEE P2664 STTP publisher/server for downstream consumers
- Publishes fused data streams combining real-time measurements and file events
- Supports 1000+ concurrent STTP client connections with certificate-based authentication

**Cloud-Native Design**:
- Container-first architecture with Docker and Kubernetes deployment
- Horizontal scaling capabilities with load balancing
- Comprehensive monitoring with Prometheus/Grafana/Jaeger
- Infrastructure as Code with Terraform and Helm charts

### Implementation Benefits

**Operational Advantages**:
- **Reduced Infrastructure**: No Windows licensing or .NET runtime requirements
- **Cloud Deployment**: Native Kubernetes support with auto-scaling
- **Cost Effective**: Open source stack with enterprise monitoring capabilities
- **Cross Platform**: Linux/Windows/macOS compatibility

**Technical Advantages**:
- **Modern Architecture**: Microservices with API-first design
- **Protocol Delegation**: Triangle MicroWorks handles device complexity
- **Standardized Output**: STTP provides vendor-neutral data interface
- **Maintainability**: Python ecosystem with comprehensive testing

**Integration Advantages**:
- **SCADA Compatibility**: Works with any STTP-compatible downstream system
- **Vendor Neutral**: Not tied to specific SCADA or historian vendors
- **Extensible**: Plugin architecture for new file formats and protocols
- **Future Proof**: Modern technology stack with active community support

### Migration Path

**Parallel Deployment Strategy**:
1. Deploy Python container alongside existing openMIC
2. Configure Triangle MicroWorks for protocol handling
3. Migrate devices in phases with validation
4. Transition downstream consumers to STTP interface
5. Decommission legacy openMIC infrastructure

**Data Continuity**:
- Export existing device configurations and historical data
- Maintain audit trail during transition
- Validate data integrity throughout migration
- Provide rollback capability if needed

This comprehensive approach transforms the traditional Windows-based openMIC into a modern, containerized solution that leverages the strengths of both Python's ecosystem and Triangle MicroWorks' proven SCADA capabilities, while providing standardized STTP data publishing for seamless integration with downstream systems.
