{".class": "MypyFile", "_fullname": "protocol_testing_demo", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CommunicationErrorSimulator": {".class": "SymbolTableNode", "cross_ref": "simulated_devices.protocol_communication.CommunicationErrorSimulator", "kind": "Gdef"}, "DNP3Config": {".class": "SymbolTableNode", "cross_ref": "simulated_devices.protocol_communication.DNP3Config", "kind": "Gdef"}, "DeviceFactory": {".class": "SymbolTableNode", "cross_ref": "simulated_devices.device_factory.DeviceFactory", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "FileTransferCoordinator": {".class": "SymbolTableNode", "cross_ref": "simulated_devices.file_transfer_simulation.FileTransferCoordinator", "kind": "Gdef"}, "LoadTestingFramework": {".class": "SymbolTableNode", "cross_ref": "simulated_devices.protocol_communication.LoadTestingFramework", "kind": "Gdef"}, "ModbusConfig": {".class": "SymbolTableNode", "cross_ref": "simulated_devices.protocol_communication.ModbusConfig", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "ProtocolLatencySimulator": {".class": "SymbolTableNode", "cross_ref": "simulated_devices.protocol_communication.ProtocolLatencySimulator", "kind": "Gdef"}, "ProtocolMessageLogger": {".class": "SymbolTableNode", "cross_ref": "simulated_devices.protocol_communication.ProtocolMessageLogger", "kind": "Gdef"}, "ProtocolTestingDemo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "protocol_testing_demo.ProtocolTestingDemo", "name": "ProtocolTestingDemo", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "protocol_testing_demo.ProtocolTestingDemo", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "protocol_testing_demo", "mro": ["protocol_testing_demo.ProtocolTestingDemo", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "protocol_testing_demo.ProtocolTestingDemo.__init__", "name": "__init__", "type": null}}, "_log_event": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "protocol_testing_demo.ProtocolTestingDemo._log_event", "name": "_log_event", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["protocol_testing_demo.ProtocolTestingDemo", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_log_event of ProtocolTestingDemo", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_log_measurement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "measurement"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "protocol_testing_demo.ProtocolTestingDemo._log_measurement", "name": "_log_measurement", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "measurement"], "arg_types": ["protocol_testing_demo.ProtocolTestingDemo", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_log_measurement of ProtocolTestingDemo", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_test_dnp3_communication": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "protocol_testing_demo.ProtocolTestingDemo._test_dnp3_communication", "name": "_test_dnp3_communication", "type": null}}, "_test_iec61850_communication": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "protocol_testing_demo.ProtocolTestingDemo._test_iec61850_communication", "name": "_test_iec61850_communication", "type": null}}, "_test_modbus_communication": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "protocol_testing_demo.ProtocolTestingDemo._test_modbus_communication", "name": "_test_modbus_communication", "type": null}}, "coordinator": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "protocol_testing_demo.ProtocolTestingDemo.coordinator", "name": "coordinator", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "device_factory": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "protocol_testing_demo.ProtocolTestingDemo.device_factory", "name": "device_factory", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "error_simulator": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "protocol_testing_demo.ProtocolTestingDemo.error_simulator", "name": "error_simulator", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "file_transfer_coordinator": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "protocol_testing_demo.ProtocolTestingDemo.file_transfer_coordinator", "name": "file_transfer_coordinator", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "generate_test_report": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "protocol_testing_demo.ProtocolTestingDemo.generate_test_report", "name": "generate_test_report", "type": null}}, "latency_simulator": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "protocol_testing_demo.ProtocolTestingDemo.latency_simulator", "name": "latency_simulator", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "load_tester": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "protocol_testing_demo.ProtocolTestingDemo.load_tester", "name": "load_tester", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "logger": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "protocol_testing_demo.ProtocolTestingDemo.logger", "name": "logger", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "message_logger": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "protocol_testing_demo.ProtocolTestingDemo.message_logger", "name": "message_logger", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "run_comprehensive_tests": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "protocol_testing_demo.ProtocolTestingDemo.run_comprehensive_tests", "name": "run_comprehensive_tests", "type": null}}, "security_framework": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "protocol_testing_demo.ProtocolTestingDemo.security_framework", "name": "security_framework", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "setup_test_devices": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "protocol_testing_demo.ProtocolTestingDemo.setup_test_devices", "name": "setup_test_devices", "type": null}}, "test_communication_errors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "protocol_testing_demo.ProtocolTestingDemo.test_communication_errors", "name": "test_communication_errors", "type": null}}, "test_file_transfer_protocols": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "protocol_testing_demo.ProtocolTestingDemo.test_file_transfer_protocols", "name": "test_file_transfer_protocols", "type": null}}, "test_load_performance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "protocol_testing_demo.ProtocolTestingDemo.test_load_performance", "name": "test_load_performance", "type": null}}, "test_protocol_communication": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "protocol_testing_demo.ProtocolTestingDemo.test_protocol_communication", "name": "test_protocol_communication", "type": null}}, "test_protocol_timing_and_latency": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "protocol_testing_demo.ProtocolTestingDemo.test_protocol_timing_and_latency", "name": "test_protocol_timing_and_latency", "type": null}}, "test_results": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "protocol_testing_demo.ProtocolTestingDemo.test_results", "name": "test_results", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "test_security_authentication": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "protocol_testing_demo.ProtocolTestingDemo.test_security_authentication", "name": "test_security_authentication", "type": null}}, "test_triangle_microworks_integration": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "protocol_testing_demo.ProtocolTestingDemo.test_triangle_microworks_integration", "name": "test_triangle_microworks_integration", "type": null}}, "tmw_integration": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "protocol_testing_demo.ProtocolTestingDemo.tmw_integration", "name": "tmw_integration", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "protocol_testing_demo.ProtocolTestingDemo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "protocol_testing_demo.ProtocolTestingDemo", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SecurityTestingFramework": {".class": "SymbolTableNode", "cross_ref": "simulated_devices.protocol_communication.SecurityTestingFramework", "kind": "Gdef"}, "SimulationCoordinator": {".class": "SymbolTableNode", "cross_ref": "simulated_devices.simulation_coordinator.SimulationCoordinator", "kind": "Gdef"}, "TMWConfig": {".class": "SymbolTableNode", "cross_ref": "simulated_devices.triangle_microworks_integration.TMWConfig", "kind": "Gdef"}, "TMWIntegrationCoordinator": {".class": "SymbolTableNode", "cross_ref": "simulated_devices.triangle_microworks_integration.TMWIntegrationCoordinator", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "protocol_testing_demo.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "protocol_testing_demo.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "protocol_testing_demo.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "protocol_testing_demo.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "protocol_testing_demo.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "protocol_testing_demo.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "main": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "protocol_testing_demo.main", "name": "main", "type": null}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "timezone": {".class": "SymbolTableNode", "cross_ref": "datetime.timezone", "kind": "Gdef"}}, "path": "C:\\home-repos\\Quanta\\examples\\protocol_testing_demo.py"}