{"data_mtime": 1757981594, "dep_lines": [6, 7, 8, 9, 10, 11, 12, 13, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 5, 5, 5, 10, 5, 30, 30, 30], "dependencies": ["os", "struct", "numpy", "logging", "datetime", "typing", "pathlib", "uuid", "builtins", "_frozen_importlib", "abc", "typing_extensions"], "hash": "492d8b5a8d745736a3b7a15c5ae24c31a12ec234", "id": "simulated_devices.file_generators", "ignore_all": true, "interface_hash": "2b8ce30daa3d0bad02ccc3b5d8c964a28ef1cf0a", "mtime": 1757981496, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\Quanta\\simulated_devices\\file_generators.py", "plugin_data": null, "size": 18463, "suppressed": [], "version_id": "1.15.0"}