#!/usr/bin/env python3
"""
Simple Modbus TCP client to test the server
"""

import socket
import struct
import time

def create_modbus_request(transaction_id, unit_id, function_code, start_address, quantity):
    """Create a Modbus TCP request"""
    # MBAP Header: Transaction ID, Protocol ID, Length, Unit ID
    protocol_id = 0
    length = 6  # Unit ID + Function Code + Start Address + Quantity
    
    header = struct.pack('>HHHB', transaction_id, protocol_id, length, unit_id)
    pdu = struct.pack('>BHH', function_code, start_address, quantity)
    
    return header + pdu

def parse_modbus_response(response):
    """Parse Modbus TCP response"""
    if len(response) < 7:
        return None
        
    # Parse MBAP header
    transaction_id, protocol_id, length, unit_id = struct.unpack('>HHHB', response[:7])
    
    # Parse PDU
    function_code = response[7]
    data = response[8:]
    
    return {
        'transaction_id': transaction_id,
        'protocol_id': protocol_id,
        'length': length,
        'unit_id': unit_id,
        'function_code': function_code,
        'data': data
    }

def test_modbus_connection():
    """Test Modbus TCP connection to the server"""
    host = '************'
    port = 20010
    
    print(f"Testing Modbus TCP connection to {host}:{port}")
    
    try:
        # Create socket connection
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5.0)  # 5 second timeout
        
        print(f"Connecting to {host}:{port}...")
        sock.connect((host, port))
        print("Connected successfully!")
        
        # Test Read Holding Registers (Function Code 03)
        transaction_id = 1
        unit_id = 1  # METER_01 slave address
        function_code = 3  # Read Holding Registers
        start_address = 0  # Start at register 0 (voltage_a)
        quantity = 3  # Read 3 registers (voltage_a, voltage_b, voltage_c)
        
        request = create_modbus_request(transaction_id, unit_id, function_code, start_address, quantity)
        
        print(f"Sending request: FC={function_code}, Start={start_address}, Qty={quantity}")
        print(f"Request bytes: {request.hex()}")
        
        sock.send(request)
        
        # Receive response
        response = sock.recv(1024)
        print(f"Received {len(response)} bytes: {response.hex()}")
        
        # Parse response
        parsed = parse_modbus_response(response)
        if parsed:
            print(f"Response: TID={parsed['transaction_id']}, FC={parsed['function_code']}")
            
            if parsed['function_code'] == 3:  # Read Holding Registers response
                byte_count = parsed['data'][0]
                register_data = parsed['data'][1:1+byte_count]
                
                print(f"Byte count: {byte_count}")
                
                # Parse register values
                registers = []
                for i in range(0, len(register_data), 2):
                    if i + 1 < len(register_data):
                        value = struct.unpack('>H', register_data[i:i+2])[0]
                        registers.append(value)
                
                print(f"Register values: {registers}")
                
                # Convert to engineering units (scaled by 10)
                if len(registers) >= 3:
                    voltage_a = registers[0] / 10.0
                    voltage_b = registers[1] / 10.0
                    voltage_c = registers[2] / 10.0
                    
                    print(f"Voltage A: {voltage_a} kV")
                    print(f"Voltage B: {voltage_b} kV")
                    print(f"Voltage C: {voltage_c} kV")
                    
        else:
            print("Failed to parse response")
            
        sock.close()
        print("Test completed successfully!")
        
    except socket.timeout:
        print("ERROR: Connection timeout - server may not be responding")
    except ConnectionRefusedError:
        print("ERROR: Connection refused - server may not be running")
    except Exception as e:
        print(f"ERROR: {e}")
    finally:
        try:
            sock.close()
        except:
            pass

if __name__ == "__main__":
    test_modbus_connection()
