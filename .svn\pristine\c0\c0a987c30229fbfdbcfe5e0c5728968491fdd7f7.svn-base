#!/usr/bin/env python3
"""
Debug Modbus server to isolate the issue
"""

import asyncio
import logging
import struct

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("DebugModbus")

async def handle_connection(reader, writer):
    """Simple connection handler"""
    client_addr = writer.get_extra_info('peername')
    logger.info(f"Client connected: {client_addr}")
    
    try:
        while True:
            # Read data
            data = await reader.read(1024)
            if not data:
                logger.info("Client disconnected (no data)")
                break
                
            logger.info(f"Received {len(data)} bytes: {data.hex()}")
            
            # Parse basic Modbus TCP header
            if len(data) >= 7:
                transaction_id, protocol_id, length, unit_id = struct.unpack('>HHHB', data[:7])
                logger.info(f"Modbus header: TID={transaction_id}, PID={protocol_id}, Len={length}, Unit={unit_id}")
                
                if len(data) >= 8:
                    function_code = data[7]
                    logger.info(f"Function code: {function_code}")
                    
                    # Send a simple response for function code 3 (Read Holding Registers)
                    if function_code == 3:
                        # Simple response: 3 registers with values 1380, 1380, 1380 (138.0 kV scaled by 10)
                        response_data = struct.pack('B', 6) + struct.pack('>HHH', 1380, 1380, 1380)  # 6 bytes + 3 registers
                        response = struct.pack('>HHHB', transaction_id, 0, len(response_data) + 2, unit_id) + struct.pack('B', function_code) + response_data
                        
                        logger.info(f"Sending response: {response.hex()}")
                        writer.write(response)
                        await writer.drain()
                        logger.info("Response sent successfully")
                    else:
                        logger.info(f"Unsupported function code: {function_code}")
                        
    except Exception as e:
        logger.error(f"Connection error: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
    finally:
        try:
            writer.close()
            await writer.wait_closed()
        except Exception as e:
            logger.error(f"Error closing connection: {e}")
        logger.info(f"Client disconnected: {client_addr}")

async def main():
    """Main function"""
    logger.info("Starting debug Modbus server on port 20010")
    
    server = await asyncio.start_server(
        handle_connection,
        '0.0.0.0',
        20010
    )
    
    logger.info("Server started, waiting for connections...")
    
    async with server:
        await server.serve_forever()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Server stopped")
