---
swagger: "2.0"
info:
  description: "SDG Runtime API"
  version: "1.0.0"
  title: "SDG Runtime"
#host: "localhost:58080"
basePath: "/rest"
schemes:
  - http
  - https
securityDefinitions:
  Bearer:
    type: apiKey
    name: Authorization
    in: header
paths:
  /editors/{command}:
    get:
      security:
      - Bearer: []
      tags:
      - "editors"
      summary: "Gets an editable object editor."
      description: "For a new object send the MENU_CMD_ADD... command, for an existing object send the MENU_CMD_EDIT command"
      operationId: "getEditorData"
      produces:
      - "application/json"
      parameters:
      - name: "command"
        in: "path"
        description: "command name (see: EditorCommandsDTO for enum)"
        required: true
        type: "string"
        enum:
        - MENU_CMD_EDIT
        - MENU_CMD_ADD_MBP_CHANNEL
        - MENU_CMD_ADD_TCP_CHANNEL
        - MENU_CMD_ADD_DNP3_UDP_TCP_CHANNEL
        - MENU_CMD_ADD_DNP3_XML_DEVICE
        - MENU_CMD_ADD_SERIAL_CHANNEL
        - MENU_CMD_ADD_MODEM_POOL_CHANNEL
        - MENU_CMD_ADD_MODEM
        - MENU_CMD_ADD_MODEM_POOL
        - MENU_CMD_ADD_OPC_CLIENT
        - MENU_CMD_ADD_OPC_AE_CLIENT
        - MENU_CMD_ADD_61850_CLIENTS_FROM_FILE
        - MENU_CMD_ADD_61850_CLIENT
        - MENU_CMD_ADD_TASE2_CLIENT
        - MENU_CMD_ADD_TASE2_SERVER
        - MENU_CMD_ADD_TASE2_CLIENT_SERVER
        - MENU_CMD_ADD_61850_CONTROL_TO_OPC_MAPPING
        - MENU_CMD_ADD_61850_CONTROL_TO_OPC_MAPPING_ITEM
        - MENU_CMD_ADD_61850_SERVER
        - MENU_CMD_ADD_TASE2_LOGICAL_DEVICE
        - MENU_CMD_ADD_ODBC_CLIENT
        - MENU_CMD_ADD_EQ_MDO
        - MENU_CMD_ADD_INTERNAL_MDO
        - MENU_CMD_ADD_61850_REPORT
        - MENU_CMD_ADD_61850_GOOSE
        - MENU_CMD_ADD_61850_POLLED_DATA_SET
        - MENU_CMD_ADD_61850_POLLED_POINT_SET
        - MENU_CMD_ADD_61850_COMMAND_POINT
        - MENU_CMD_ADD_61850_COMMAND_POINT_SET
        - MENU_CMD_ADD_61850_WRITABLE_POINT
        - MENU_CMD_ADD_61850_WRITABLE_POINT_SET
        - MENU_CMD_ADD_GOOSE_MONITOR
        - MENU_CMD_ADD_REDUNDANT_SLAVE_CHANNEL
        - MENU_CMD_ADD_REDUNDANT_MASTER_CHANNEL
        - MENU_CMD_DELETE_REDUNDANT_CHANNEL
        - MENU_CMD_ADD_61400_ALARMS_NODE
        - MENU_CMD_ADD_61400_ALARM_MDO
        - MENU_CMD_ADD_61850_ITEM
        - MENU_CMD_ADD_TASE2_DSTS
        - MENU_CMD_ADD_TASE2_POLLED_DATA_SET
        - MENU_CMD_ADD_TASE2_POLLED_POINT_SET
        - MENU_CMD_ADD_TASE2_COMMAND_POINT
        - MENU_CMD_ADD_TASE2_COMMAND_POINT_SET
        - MENU_CMD_ADD_TASE2_ITEM
        - MENU_CMD_ADD_OPC_ITEM
        - MENU_CMD_ADD_MULTIPLE_OPC_ITEM
        - MENU_CMD_ADD_MULTIPLE_OPC_UA_ITEM
        - MENU_CMD_ADD_ODBC_ITEM
        - MENU_CMD_ADD_OPC_AE_ITEM
        - MENU_CMD_ADD_SESSION
        - MENU_CMD_ADD_MDO
        - MENU_CMD_ADD_MULTIPLE_MDO
        - MENU_CMD_ADD_SECTOR
        - MENU_CMD_ADD_DATA_TYPE
        - MENU_CMD_ADD_DNP_PROTO
        - MENU_CMD_ADD_DNP_DESCP
        - MENU_CMD_ADD_DATASET_ELEMENT
        - MENU_CMD_ADD_OPC_AE_ATTR
        - MENU_CMD_ADD_WRITE_ACTION
        - MENU_CMD_ADD_MULTI_POINT
        - MENU_CMD_SUBSCRIBE_GOOSE_STREAM
        - MENU_CMD_UNSUBSCRIBE_GOOSE_STREAM
        - MENU_CMD_CREATE_THXML_POINT_FILE
        - MENU_CMD_CREATE_DTM_CSV_POINT_FILE
        - MENU_CMD_CONNECT_OPC_SERVER
        - MENU_CMD_DISCONNECT_OPC_SERVER
        - MENU_CMD_CONNECT_OPC_AE_SERVER
        - MENU_CMD_DISCONNECT_OPC_AE_SERVER
        - MENU_CMD_CONNECT_OPC_UA_SERVER
        - MENU_CMD_DISCONNECT_OPC_UA_SERVER
        - MENU_CMD_OPC_UA_GET_SERVER_STATUS
        - MENU_CMD_ENABLE_DSTS
        - MENU_CMD_DISABLE_DSTS
        - MENU_CMD_SHOW_CONFIG_TASE2_SERVER
        - MENU_CMD_SHOW_CONFIG_TASE2_CLIENT
        - MENU_CMD_CREATE_SERVER
        - MENU_CMD_RESET_AVERAGE_MDO_UPDATE_RATE
        - MENU_CMD_ADD_USER_DEFINED_FOLDER
        - MENU_CMD_SWITCH_TO_RCHANNEL
      - name: "objectName"
        in: "query"
        description: "object path"
        required: true
        type: "string"
      - name: "objectClassName"
        in: "query"
        description: "object class i.e. the type of the object"
        required: false
        type: "string"
      - name: "parentObjectName"
        in: "query"
        description: "parent object path"
        required: true
        type: "string"
      - name: "editAtRuntime"
        in: "query"
        description: "edit at runtime (true to edit objects that require the SDG Engine to be restarted)"
        required: true
        type: "boolean"
        default: false
      - name: "objectCollectionKind"
        in: "query"
        description: "whether this node's collection has MDOs or SDOs in it."
        required: false
        type: "string"
        default: "ALL"
        enum:
        - MDO
        - SDO
        - ALL
      responses:
        200:
          description: "OK"
          schema:
            $ref: "#/definitions/EditorSpecificationObjectDTO"
        204:
          description: "No Content"
        400:
          description: "Bad Request"
    put:
      security:
      - Bearer: []
      tags:
      - "editors"
      summary: "Adds a new object to the database or updates the existing object."
      description: "For a new object send the MENU_CMD_ADD... command, for an existing object send the MENU_CMD_EDIT command. The definition returned by editors/get must be filled out and used in the objectDataJson member "
      operationId: "createOrUpdateEditorObject"
      consumes:
      - "application/json"
      parameters:
      - name: "command"
        in: "path"
        description: "add/update command (see: EditorCommandsDTO for enum)"
        required: true
        type: "string"
        enum:
        - MENU_CMD_EDIT
        - MENU_CMD_ADD_MBP_CHANNEL
        - MENU_CMD_ADD_TCP_CHANNEL
        - MENU_CMD_ADD_DNP3_UDP_TCP_CHANNEL
        - MENU_CMD_ADD_DNP3_XML_DEVICE
        - MENU_CMD_ADD_SERIAL_CHANNEL
        - MENU_CMD_ADD_MODEM_POOL_CHANNEL
        - MENU_CMD_ADD_MODEM
        - MENU_CMD_ADD_MODEM_POOL
        - MENU_CMD_ADD_OPC_CLIENT
        - MENU_CMD_ADD_OPC_AE_CLIENT
        - MENU_CMD_ADD_61850_CLIENTS_FROM_FILE
        - MENU_CMD_ADD_61850_CLIENT
        - MENU_CMD_ADD_TASE2_CLIENT
        - MENU_CMD_ADD_TASE2_SERVER
        - MENU_CMD_ADD_TASE2_CLIENT_SERVER
        - MENU_CMD_ADD_61850_SERVER
        - MENU_CMD_ADD_TASE2_LOGICAL_DEVICE
        - MENU_CMD_ADD_ODBC_CLIENT
        - MENU_CMD_ADD_EQ_MDO
        - MENU_CMD_ADD_INTERNAL_MDO
        - MENU_CMD_ADD_61850_REPORT
        - MENU_CMD_ADD_61850_GOOSE
        - MENU_CMD_ADD_61850_POLLED_DATA_SET
        - MENU_CMD_ADD_61850_POLLED_POINT_SET
        - MENU_CMD_ADD_61850_COMMAND_POINT
        - MENU_CMD_ADD_61850_COMMAND_POINT_SET
        - MENU_CMD_ADD_61850_WRITABLE_POINT
        - MENU_CMD_ADD_61850_WRITABLE_POINT_SET
        - MENU_CMD_ADD_61850_DATASET
        - MENU_CMD_CHANGE_61850_DATASET
        - MENU_CMD_ADD_GOOSE_MONITOR
        - MENU_CMD_ADD_REDUNDANT_SLAVE_CHANNEL
        - MENU_CMD_ADD_REDUNDANT_MASTER_CHANNEL
        - MENU_CMD_DELETE_REDUNDANT_CHANNEL
        - MENU_CMD_ADD_61400_ALARMS_NODE
        - MENU_CMD_ADD_61400_ALARM_MDO
        - MENU_CMD_ADD_61850_ITEM
        - MENU_CMD_ADD_TASE2_DSTS
        - MENU_CMD_ADD_TASE2_POLLED_DATA_SET
        - MENU_CMD_ADD_TASE2_POLLED_POINT_SET
        - MENU_CMD_ADD_TASE2_COMMAND_POINT
        - MENU_CMD_ADD_TASE2_COMMAND_POINT_SET
        - MENU_CMD_ADD_TASE2_ITEM
        - MENU_CMD_ADD_TASE2_DATASET
        - MENU_CMD_MANAGE_TASE2_DATASET
        - MENU_CMD_MANAGE_TASE2_DATASET_FULL_EDIT
        - MENU_CMD_ADD_TASE2_DOMAIN
        - MENU_CMD_ADD_TASE2_DATA_ATTRIBUTE
        - MENU_CMD_ADD_OPC_ITEM
        - MENU_CMD_ADD_MULTIPLE_OPC_ITEM
        - MENU_CMD_ADD_MULTIPLE_OPC_UA_ITEM
        - MENU_CMD_ADD_ODBC_ITEM
        - MENU_CMD_ADD_OPC_AE_ITEM
        - MENU_CMD_ADD_SESSION
        - MENU_CMD_ADD_MDO
        - MENU_CMD_ADD_MULTIPLE_MDO
        - MENU_CMD_ADD_SECTOR
        - MENU_CMD_ADD_DATA_TYPE
        - MENU_CMD_ADD_DNP_PROTO
        - MENU_CMD_ADD_DNP_DESCP
        - MENU_CMD_ADD_DATASET_ELEMENT
        - MENU_CMD_ADD_OPC_AE_ATTR
        - MENU_CMD_ADD_WRITE_ACTION
        - MENU_CMD_ADD_MULTI_POINT
        - MENU_CMD_ADD_USER_DEFINED_FOLDER
      - in: "body"
        name: "body"
        description: "command data"
        required: true
        schema:
          $ref: "#/definitions/EditorCommandDTO"
      responses:
        200:
          description: "OK"
        400:
          description: "Failed to create/edit object"
  /editor/action:
    get:
      security:
      - Bearer: []
      tags:
      - "editors"
      summary: "editor action"
      description: ""
      operationId: "editorAction"
      produces:
      - "application/json"
      parameters:
      - name: "objectName"
        in: "query"
        description: "object Name triggering the action"
        required: true
        type: "string"
      - name: "objectCollectionKind"
        in: "query"
        description: "whether this node's collection has MDOs or SDOs in it."
        required: false
        type: "string"
        default: "ALL"
        enum:
        - MDO
        - SDO
        - ALL
      - name: "action"
        in: "query"
        description: "action type triggering the action"
        required: true
        type: "string"
      - name: "parameter_1"
        in: "query"
        description: "parameter 1"
        required: false
        type: "string"
      - name: "parameter_2"
        in: "query"
        description: "parameter 2"
        required: false
        type: "string"
      - name: "parameter_3"
        in: "query"
        description: "parameter 3"
        required: false
        type: "string"
      - name: "parameter_4"
        in: "query"
        description: "parameter 4"
        required: false
        type: "string"
      - name: "parameter_5"
        in: "query"
        description: "parameter 5"
        required: false
        type: "string"
      - name: "parameter_6"
        in: "query"
        description: "parameter 6"
        required: false
        type: "string"
      responses:
        200:
          description: "Action result"
  /editor_context_menu:
    get:
      security:
      - Bearer: []
      tags:
      - "editor context menu"
      summary: "Get an editor context menu for an editable object."
      description: ""
      operationId: "getEditorContextMenu"
      produces:
      - "application/json"
      parameters:
      - name: "objectFullName"
        in: "query"
        description: "object name/path (i.e. mmb).  An editor context menu will be obtained for this node. Empty string for the root node."
        required: false
        type: "string"
      - name: "objectClassName"
        in: "query"
        description: "object type (i.e. class name)."
        required: false
        type: "string"
      - name: "objectCollectionKind"
        in: "query"
        description: "whether this object's collection has MDOs or SDOs in it."
        required: false
        type: "string"
        default: "ALL"
        enum:
        - MDO
        - SDO
        - ALL
      responses:
        200:
          description: "OK"
          schema:
            type: array
            items:
                $ref: '#/definitions/EditorContextMenuDTO'
        204:
          description: "No Content"
        400:
          description: "Bad Request"
  /editor/define_local_parameter:
    put:
      security:
      - Bearer: []
      tags:
      - "editors"
      summary: "Update local INI parameters."
      description: "Return an objectDataJson member with the updated local INI parameters"
      operationId: "defineLocalParameter"
      consumes:
      - "application/json"
      parameters:
        - name: "body"
          in: "body"
          description: "command data"
          required: true
          schema:
            $ref: "#/definitions/EditorCommandDTO"
      responses:
      200:
        description: "OK"
      400:
        description: "Failed to create/edit object"
  /nodes:
    get:
      security:
      - Bearer: []
      tags:
      - "nodes"
      summary: "Get nodes."
      description: "Provides items from the SDG tree as TreeNodeDTO objects"
      operationId: "getNodes"
      produces:
      - "application/json"
      parameters:
      - name: "rootNodePath"
        in: "query"
        description: "root node full path (we start at this node)"
        required: false
        type: "string"
      - name: "nodeFullNameFilter"
        in: "query"
        description: "node full name filter"
        required: false
        type: "string"
      - name: "includeLeaves"
        in: "query"
        description: "include leaf nodes in result"
        required: false
        type: "boolean"
        default: false
      - name: "onlyFirstChildrenLevel"
        in: "query"
        description: "include only the first children level in result"
        required: false
        type: "boolean"
        default: false
      - name: "nodeCollectionKindFilter"
        in: "query"
        description: "node collection kind filter (i.e. MDO,SDO,ALL)"
        required: false
        type: "string"
        default: "ALL"
        enum:
        - MDO
        - SDO
        - ALL
      responses:
        200:
          description: "OK"
          schema:
            $ref: '#/definitions/TreeNodeDTO'
        204:
          description: "No Content"
        400:
          description: "Bad Request"
    delete:
      security:
      - Bearer: []
      tags:
      - "nodes"
      summary: "Removes nodes from the database."
      description: "Removes nodes from the database."
      operationId: "deleteNodes"
      consumes:
      - "application/json"
      produces:
      - "application/json"
      parameters:
      - in: "body"
        name: "body"
        description: "nodes data"
        required: true
        schema:
          $ref: "#/definitions/TreeNodeCollectionObjectDTO"
      - name: "parentObjectName"
        in: "query"
        description: "parent object path"
        required: false
        type: "string"
      - name: "objectCollectionKind"
        in: "query"
        description: "whether this node's collection has MDOs or SDOs in it."
        required: false
        type: "string"
        default: "ALL"
        enum:
        - MDO
        - SDO
        - ALL
      responses:
        200:
          description: "Success"
        400:
          description: "Bad Request"
  /node_page_info:
    get:
      security:
      - Bearer: []
      tags:
      - "nodes"
      summary: "Get node page info."
      description: "Provides number of tags and mappings for items in the SDG tree"
      operationId: "getNodePageInfo"
      produces:
      - "application/json"
      parameters:
      - name: "rootNodePath"
        in: "query"
        description: "root node full path (we start at this node)"
        required: false
        type: "string"
      - name: "sortColumn"
        in: "query"
        description: "filed name of column to sort."
        required: false
        type: "string"
        enum:
          - tagName
          - tagValueType
        default: tagName
      - name: "sortColumnDirection"
        in: "query"
        description: "Direction of column to sort."
        required: false
        type: "string"
        enum:
          - Ascending
          - Descending
        default: Ascending
      - name: "valueTypeFilter"
        in: "query"
        description: "mdo value type filter"
        required: false
        type: "string"
        enum:
          - Unknown
          - Bool
          - Spare
          - Char
          - Unsigned Char
          - Short
          - Unsigned Short
          - Long
          - Unsigned Int
          - Float
          - Double
          - String
          - Time
          - Int 64
          - Unsigned Int 64
      - name: "nameFilter"
        in: "query"
        description: "returns items that contain this string (use empty string for no filter)."
        required: false
        type: "string"
      - name: "tagDescriptionFilter"
        in: "query"
        description: "tag description filter - returns items that contain this string (use empty string for no filter)."
        required: false
        type: "string"
      - name: "tagAliasFilter"
        in: "query"
        description: "tag user defined name filter - returns items that contain this string (use empty string for no filter)."
        required: false
        type: "string"
      - name: "recursive"
        in: "query"
        description: "get recursively"
        required: false
        type: "boolean"
        default: false
      - name: "tagPurposeFilter"
        in: "query"
        description: "Tag purpose mask for filtering tags (GTWTYPES_TAG_PURPOSE_MASK). Values can be combined using bitwise OR."
        required: false
        schema:
          $ref: '#/definitions/GTWTYPES_TAG_PURPOSE_MASK'
      responses:
        200:
          description: "OK"
          schema:
            $ref: '#/definitions/TreeNodePageInfoDTO'
        204:
          description: "No Content"
        400:
          description: "Bad Request"
  /classes:
    get:
      security:
      - Bearer: []
      tags:
      - "classes"
      summary: "Get classes."
      description: "Provides items from the SDG class registry"
      operationId: "getClasses"
      produces:
      - "application/json"
      parameters:
      - name: "classNameFilter"
        in: "query"
        description: "class name filter"
        required: false
        type: "string"
      responses:
        200:
          description: "OK"
          schema:
            $ref: '#/definitions/ClassNodeDTO'
        204:
          description: "No Content"
        400:
          description: "Bad Request"
  /ini_param_help:
    get:
      security:
      - Bearer: []
      tags:
      - "help"
      summary: "Get INI File parameter help."
      description: "Provides items from the SDG INI file"
      operationId: "getINIhelp"
      produces:
      - "application/json"
      parameters:
      - name: "iniParamFilter"
        in: "query"
        description: "ini param name filter (empty string for all)"
        required: false
        type: "string"
      responses:
        200:
          description: "OK"
          schema:
            type: array
            items:
              $ref: '#/definitions/INIParamNodeDTO'
        204:
          description: "No Content"
        400:
          description: "Bad Request"
  /health:
    get:
      security:
      - Bearer: []
      tags:
      - "manage"
      summary: "Get health of Engine."
      description: "Get health of Engine. Used by monitor and redundancy  to obtain health of engine"
      operationId: "healthGET"
      produces:
      - "application/json"
      parameters: []
      responses:
        200:
          description: "Health GET"
          schema:
            $ref: '#/definitions/SDGHealthObjectDTO'
        400:
          description: "Bad Request"
  /stopEngine:
    post:
      security:
      - Bearer: []
      tags:
      - "manage"
      summary: "Stop the Engine"
      description: "Stop the SDG.  By default O/S will attempt to re-start Engine"
      operationId: "stopEngine"
      produces:
      - "application/json"
      parameters: []
      responses:
        200:
          description: "StopEngine"
        400:
          description: "Bad Request"
  /load_config:
    post:
      security:
      - Bearer: []
      tags:
      - "manage"
      summary: "load the config for SDG."
      description: "load the config for SDG."
      operationId: "loadConfig"
      produces:
      - "application/json"
      parameters: []
      responses:
        200:
          description: "Load Config"
        400:
          description: "Bad Request"
  /save_file:
    post:
      security:
      - Bearer: []
      tags:
      - "manage"
      summary: "Save Gateway running file."
      description: "Save Gateway running file."
      operationId: "saveFile"
      consumes:
      - application/x-www-form-urlencoded
      parameters:
      - name: "overrideLoadError"
        in: "formData"
        description: "set to true to save INI/CSV when there are load errors"
        required: false
        default: false
        type: "boolean"
      responses:
        200:
          description: "OK"
        204:
          description: "Bad Request"
        401:
          description: "Unauthorized"
  /save_file_copy_as:
    post:
      security:
      - Bearer: []
      tags:
      - "manage"
      summary: "Save Gateway running workspace as copy."
      description: "Save Gateway running workspace as copy."
      operationId: "saveFileCopyAs"
      consumes:
      - application/x-www-form-urlencoded
      produces:
      - "application/json"
      parameters:
      - name: "newWorkSpaceName"
        in: "formData"
        description: "name of the work space to save copy as"
        required: false
        type: "string"
      responses:
        200:
          description: "OK"
        204:
          description: "Bad Request"
        401:
          description: "Unauthorized"
  /get_license:
    get:
      security:
      - Bearer: []
      tags:
      - "manage"
      summary: "Get license information."
      description: "Get license information."
      operationId: "getLicense"
      produces:
      - "application/json"
      parameters: []
      consumes:
      - "application/json"
      responses:
        200:
          description: "OK"
        204:
          description: "Bad Request"
        401:
          description: "Unauthorized"
  /save_license:
    post:
      security:
      - Bearer: []
      tags:
      - "config"
      summary: "Save license information."
      description: "Save license information."
      operationId: "saveLicense"
      consumes:
      - "application/json"
      parameters:
      - name: "action_type"
        in: "query"
        description: "action type"
        required: true
        type: "string"
      - name: "product_key"
        in: "query"
        description: "product key"
        required: false
        type: "string"
      - name: "is_new_license"
        in: "query"
        description: "license is new"
        required: true
        type: "boolean"
      responses:
        200:
          description: "OK"
        204:
          description: "No Content"
        400:
          description: "Bad Request"
        501:
          description: "Not Implemented"
  /gettag:
    get:
      security:
      - Bearer: []
      tags:
      - "tags"
      summary: "Gets a single tags value as a json object."
      description: "Gets a single tags value as a json object."
      operationId: "getTag"
      produces:
      - application/json
      parameters:
      - name: "tagName"
        in: "query"
        description: "tag path"
        required: true
        type: "string"
      responses:
        200:
          description: "OK"
          schema:
            $ref: '#/definitions/TagObjectDTO'
        204:
          description: "No Content"
        400:
          description: "Bad Request"
  /tags:
    get:
      security:
      - Bearer: []
      tags:
      - "tags"
      summary: "Get tags based on filter (i.e. MDOS, SDOS, etc) as TagObjectDTO objects."
      description: ""
      operationId: "getTags"
      produces:
      - "application/json"
      parameters:
      - name: "nodeFullName"
        in: "query"
        description: "node name/path (i.e. mmb.L1).  Tags will be obtained starting at this node. Empty string starts at the root node."
        required: false
        type: "string"
      - name: "sortColumn"
        in: "query"
        description: "filed name of column to sort."
        required: false
        type: "string"
        enum:
          - tagName
          - tagValueType
        default: tagName
      - name: "sortColumnDirection"
        in: "query"
        description: "Direction of column to sort."
        required: false
        type: "string"
        enum:
          - Ascending
          - Descending
        default: Ascending
      - name: "valueTypeFilter"
        in: "query"
        description: "tag value type filter"
        required: false
        type: "string"
        enum:
          - Unknown
          - Bool
          - Spare
          - Char
          - Unsigned_Char
          - Short
          - Unsigned_Short
          - Long
          - Unsigned_Int
          - Float
          - Double
          - String
          - Time
          - Int_64
          - Unsined_Int_64
      - name: "tagNameFilter"
        in: "query"
        description: "tag name filter"
        required: false
        type: "string"
      - name: "tagDescriptionFilter"
        in: "query"
        description: "tag description filter"
        required: false
        type: "string"
      - name: "tagAliasFilter"
        in: "query"
        description: "tag user defined name filter"
        required: false
        type: "string"
      - name: "tagCollectionKindFilter"
        in: "query"
        description: "tag collection kind filter (i.e. MDO,SDO,ALL)"
        required: false
        type: "string"
        enum:
            - MDO
            - SDO
            - ALL
      - name: "tagPurposeFilter"
        in: "query"
        description: "tag purpose mask (i.e. a binary or of health(0x01), performance(0x02), data(0x04), unhealthy(0x08), get_mappings_csv(0x10), get_points_csv(0x20) etc. see GTWTYPES_TAG_PURPOSE_MASK), a value of zero(0) or undefined(empty string)  will select all tags"
        required: false
        type: "number"
        default: 0
      - name: "recursive"
        in: "query"
        description: "get member tags recursivly"
        required: false
        type: "boolean"
        default: false
      - name: "startIndex"
        in: "query"
        description: "1 based start index number of tags to get (0 for not specified)"
        required: false
        type: "number"
        default: 0
      - name: "endIndex"
        in: "query"
        description: "1 based last index number of tags to get (0 for not specified)"
        required: false
        type: "number"
        default: 0
      - name: "fieldSelectionMask"
        in: "query"
        description: "field selection mask (i.e. a binary or of name(0x1), value(0x2), quality(0x4), time(0x8), description(0x10), options(0x20), class name(0x40), object icon(0x80), health(0x100), property mask(0x200), binding list(0x400) etc. see GTWTYPES_TAG_FIELD_MASK), a value of zero(0) or undefined(empty string)  will select all fields"
        required: false
        type: "number"
        default: 0
      responses:
        200:
          description: "OK"
          schema:
            type: array
            items:
              $ref: '#/definitions/TagObjectDTO'
        204:
          description: "No Content"
        400:
          description: "Bad Request"
    post:
      security:
      - Bearer: []
      tags:
      - "tags"
      summary: "upload a point list csv file"
      description: ""
      operationId: "postPoints"
      produces:
      - "application/json"
      consumes:
      - "multipart/form-data"
      parameters:
      - name: "file"
        in: "formData"
        description: "file"
        required: true
        type: "file"
      responses:
        200:
          description: "Upload points"
        400:
          description: "Bad Request"
    delete:
      security:
      - Bearer: []
      tags:
      - "tags"
      summary: "Removes tags from the database."
      description: "Removes tags from the database."
      operationId: "deleteTags"
      consumes:
      - "application/json"
      produces:
      - "application/json"
      parameters:
      - in: "body"
        name: "body"
        description: "tags data"
        required: true
        schema:
          $ref: "#/definitions/TagCollectionObjectDTO"
      - name: "parentObjectName"
        in: "query"
        description: "parent object path"
        required: false
        type: "string"
      - name: "objectCollectionKind"
        in: "query"
        description: "whether this node's collection has MDOs or SDOs in it."
        required: false
        type: "string"
        default: "ALL"
        enum:
        - MDO
        - SDO
        - ALL
      responses:
        200:
          description: "Success"
        400:
          description: "Bad Request"
  /set_tags_filtered:
    post:
      security:
      - Bearer: []
      tags:
      - "tags"
      summary: "Set tags based on filter."
      description: ""
      operationId: "setTagsFiltered"
      produces:
      - "application/json"
      consumes:
      - application/x-www-form-urlencoded
      parameters:
      - name: "nodeFullName"
        in: "formData"
        description: "node name/path (i.e. mmb.L1).  Tags will be set starting at this node. Empty string starts at the root node."
        required: false
        type: "string"
      - name: "valueTypeFilter"
        in: "formData"
        description: "tag value type filter"
        required: true
        type: "string"
        enum:
          - Unknown
          - Bool
          - Spare
          - Char
          - Unsigned Char
          - Short
          - Unsigned Short
          - Long
          - Unsigned Int
          - Float
          - Double
          - String
          - Time
          - Int_64
          - Unsigned Int 64
      - name: "tagNameFilter"
        in: "formData"
        description: "tag name filter"
        required: false
        type: "string"
      - name: "tagDescriptionFilter"
        in: "query"
        description: "tag description filter"
        required: false
        type: "string"
      - name: "tagAliasFilter"
        in: "query"
        description: "tag user defined name filter"
        required: false
        type: "string"
      - name: "tagPurposeFilter"
        in: "formData"
        description: "tag purpose mask (i.e. a binary or of health(0x01), performance(0x02), data(0x04), unhealthy(0x08), get_mappings_csv(0x10), get_points_csv(0x20) etc. see GTWTYPES_TAG_PURPOSE_MASK), a value of zero(0) or undefined(empty string)  will select all tags"
        required: false
        type: "number"
      - name: "recursive"
        in: "formData"
        description: "set member tags recursivly"
        required: false
        type: "boolean"
        default: false
      - name: "tagValue"
        in: "formData"
        description: "tag value"
        required: true
        type: "string"
      - name: "tagQuality"
        in: "formData"
        description: "tag quality (only for internal MDOs)"
        required: false
        type: "number"
      responses:
        200:
          description: "Success"
        204:
          description: "No Content"
        400:
          description: "Bad Request"
  /settag:
    post:
      security:
      - Bearer: []
      tags:
      - "tags"
      summary: "Sets a single tags value."
      description: ""
      operationId: "setTagForm"
      produces:
      - "application/json"
      consumes:
      - application/x-www-form-urlencoded
      parameters:
      - name: "tagName"
        in: "formData"
        description: "tag path"
        required: true
        type: "string"
      - name: "tagValue"
        in: "formData"
        description: "tag value"
        required: true
        type: "string"
      - name: "tagQuality"
        in: "formData"
        description: "tag quality (only for internal MDOs)"
        required: false
        type: "number"
      responses:
        200:
          description: "Success"
        204:
          description: "No Content"
        400:
          description: "Bad Request"
  /settags:
    post:
      security:
      - Bearer: []
      tags:
      - "tags"
      summary: "Sets a collection of tags values."
      description: ""
      operationId: "setTags"
      consumes:
      - application/json
      parameters:
      - in: "body"
        name: "body"
        description: "tags data"
        required: true
        schema:
          $ref: "#/definitions/TagValuesDTO"
      responses:
        200:
          description: "Success"
        204:
          description: "No Content"
        400:
          description: "Bad Request"
  /get_log_entries_range:
    get:
      security:
      - Bearer: []
      tags:
      - "log"
      summary: "Get log items."
      description: "Get protocol data object items as array"
      operationId: "getLogEntriesRange"
      produces:
      - "application/json"
      parameters:
      - name: "startEntryID"
        in: "query"
        description: "Retreive log entries starting with startEntryID"
        required: false
        type: "number"
      - name: "endEntryID"
        in: "query"
        description: "Retreive log entries ending with endEntryID"
        required: false
        type: "number"
      responses:
        200:
          description: "OK"
          schema:
            type: array
            items:
              $ref: '#/definitions/LogEntryDTO'
        204:
          description: "No Content"
        400:
          description: "Bad Request"
  /get_num_log_entries:
    get:
      security:
      - Bearer: []
      tags:
      - "log"
      summary: "Retrieve number of lines in protocol buffer"
      description: "Get number of lines"
      operationId: "getNumLogEntries"
      produces:
      - "application/json"
      parameters: []
      responses:
        200:
          description: "Get NumLogEntries setting"
          schema:
            type: number
            description: "Number of lines"
        400:
          description: "Bad Request"
  /get_mirror_all_to_log_file:
    get:
      security:
      - Bearer: []
      tags:
      - "log"
      summary: "Retrieve MirrorAllToLogFile setting for logger"
      description: "Get MirrorAllToLogFile setting"
      operationId: "getMirrorAllToLogFile"
      produces:
      - "application/json"
      parameters: []
      responses:
        200:
          description: "Get MirrorAllToLogFile setting"
        400:
          description: "Bad Request"
  /set_mirror_all_to_log_file:
    post:
      security:
      - Bearer: []
      tags:
      - "log"
      summary: "Set MirrorAllToLogFile setting for logger"
      description: "Set MirrorAllToLogFile setting"
      operationId: "putMirrorAllToLogFile"
      produces:
      - "application/json"
      consumes:
      - application/x-www-form-urlencoded
      parameters:
      - name: "mirrorAllToLogFile"
        in: "query"
        description: "True to Mirror All To Log File"
        required: true
        type: "boolean"
      responses:
        200:
          description: "Success"
        400:
          description: "Bad Request"
  /set_pause_logging:
    post:
      security:
      - Bearer: []
      tags:
      - "log"
      summary: "Set Pause setting for logger"
      description: "Set true to pause logging"
      operationId: "setPauseLogging"
      produces:
      - "application/json"
      consumes:
      - application/x-www-form-urlencoded
      parameters:
      - name: "pauseLogging"
        in: "query"
        description: "True to pause logging"
        required: true
        type: "boolean"
      responses:
        200:
          description: "Success"
        400:
          description: "Bad Request"
  /get_last_log_entry_id:
    get:
      security:
      - Bearer: []
      tags:
      - "log"
      summary: "Retrieve last log entry id"
      description: "Get last log entry id"
      operationId: "getLastLogEntryID"
      produces:
      - "application/json"
      parameters: []
      responses:
        200:
          description: "Get LastLogEntryID setting"
          schema:
            type: number
            description: "Last log entry id"
        400:
          description: "Bad Request"
  /get_max_log_entries:
    get:
      security:
      - Bearer: []
      tags:
      - "log"
      summary: "Retrieve max log entries to store on server"
      description: "Get max log entries"
      operationId: "getMaxLogEntries"
      produces:
      - "application/json"
      parameters: []
      responses:
        200:
          description: "Get MaxLogEntries setting"
          schema:
            type: number
            description: "Max log entries setting"
        400:
          description: "Bad Request"
  /set_max_log_entries:
    post:
      security:
      - Bearer: []
      tags:
      - "log"
      summary: "Set max log entries"
      description: "Set max log entries"
      operationId: "putMaxLogEntries"
      produces:
      - "application/json"
      consumes:
      - application/x-www-form-urlencoded
      parameters:
      - name: "maxLogEntries"
        in: "query"
        description: "Max log entries to store on server"
        required: true
        type: "integer"
        format: "int64"
      responses:
        200:
          description: "Success"
        400:
          description: "Bad Request"  
  /get_log_filter_config:
    get:
      security:
      - Bearer: []
      tags:
      - "log"
      summary: "Retrieve severity and category masks for each of source: SDG=0, SCL=1, 6T=2"
      description: "Retrieve log filter masks"
      operationId: "getLogFilterConfig"
      produces:
      - "application/json"
      parameters: []
      responses:
        200:
          description: "Retrieve severity and category masks for each of source: SDG=0, SCL=1, 6T=2"
          schema:
            type: array
            items:
              $ref: '#/definitions/LogConfigMaskDTO'
        400:
          description: "Bad Request"
  /set_log_filter_config:
    post:
      security:
      - Bearer: []
      tags:
      - "log"
      summary: "Set severity mask"
      description: "Set severity mask"
      operationId: "putLogFilterConfig"
      produces:
      - "application/json"
      consumes:
      - application/json
      parameters:
      - name: "logconfigmasks"
        in: "body"
        description: "Severity and category mask for each source"
        required: true
        schema:
          type: array
          items:
            $ref: '#/definitions/LogConfigMaskDTO'
      responses:
        200:
          description: "Set Severity and category mask for each source"
        400:
          description: "Bad Request"
  /set_log_filter_device:
    post:
      security:
      - Bearer: []
      tags:
      - "log"
      summary: "Set device filter"
      description: "Set device filter"
      operationId: "putLogFilterDevice"
      produces:
      - "application/json"
      consumes:
      - application/json
      parameters:
      - name: "logdevices"
        in: "body"
        description: "Device name and action"
        required: true
        schema:
          type: array
          items:
            $ref: '#/definitions/LogDeviceDTO'
      responses:
        200:
          description: "Set log filter devices"
        400:
          description: "Bad Request"
  /get_log_filter_device:
    get:
      security:
      - Bearer: []
      tags:
      - "log"
      summary: "Retrieve filtered devices"
      description: "Retrieve filtered devices"
      operationId: "getLogFilterDevice"
      produces:
      - "application/json"
      parameters: []
      responses:
        200:
          description: "Retrieve filtered devices"
          schema:
            type: array
            items:
              $ref: '#/definitions/LogDeviceGetDTO'
        400:
          description: "Bad Request"
  /set_max_suppression_time:
    post:
      security:
      - Bearer: []
      tags:
      - "log"
      summary: "Set max log suppression time"
      description: "Set log suppresion time in seconds, 0 disables"
      operationId: "setMaxSuppressionTime"
      produces:
      - "application/json"
      consumes:
      - application/x-www-form-urlencoded
      parameters:
      - name: "maxSuppressionTime"
        in: "query"
        description: "How long to suppress duplicate log messages in seconds"
        required: true
        type: "integer"
        format: "int64"
      responses:
        200:
          description: "Success"
        400:
          description: "Bad Request"
  /get_max_suppression_time:
    get:
      security:
      - Bearer: []
      tags:
      - "log"
      summary: "Retrieve max log suppression time"
      description: "Get  log suppression time in seconds"
      operationId: "getMaxSuppressionTime"
      produces:
      - "application/json"
      parameters: []
      responses:
        200:
          schema:
            type: number
          description: "Max log suppresion time"
        400:
          description: "Bad Request"
  /mappings:
    get:
      security:
      - Bearer: []
      tags:
      - "mappings"
      summary: "Get sdo to mdo mappings based on filter."
      description: ""
      operationId: "getMappings"
      produces:
      - "application/json"
      parameters:
      - name: "nodeFullName"
        in: "query"
        description: "node name/path (i.e. mmb).  Mappings will be obtained starting at this node. Empty string starts at the root node."
        required: false
        type: "string"
      - name: "startIndex"
        in: "query"
        description: "1 based start index number of mappings to get (0 for not specified)"
        required: false
        type: "number"
        default: 0
      - name: "endIndex"
        in: "query"
        description: "1 based last index number of mappings to get (0 for not specified)"
        required: false
        type: "number"
        default: 0
      - name: "nameFilter"
        in: "query"
        description: "returns items that contain this string (use empty string for no filter)."
        required: false
        type: "string"
      - name: "recursive"
        in: "query"
        description: "get mappings recursivly"
        required: false
        type: "boolean"
        default: false
      responses:
        200:
          description: "OK"
          schema:
            $ref: '#/definitions/MappingObjectDTO'
        204:
          description: "No Content"
        400:
          description: "Bad Request"
    post:
      security:
      - Bearer: []
      tags:
      - "mappings"
      summary: "upload a mapping csv file"
      description: ""
      operationId: "postMappings"
      produces:
      - "application/json"
      consumes:
      - "multipart/form-data"
      parameters:
      - name: "file"
        in: "formData"
        description: "file"
        required: true
        type: "file"
      responses:
        200:
          description: "Upload mappings"
        400:
          description: "Bad Request"
    delete:
      security:
      - Bearer: []
      tags:
      - "mapping"
      summary: "Removes an existing mapping."
      description: "Removes an existing mapping."
      operationId: "removeMapping"
      consumes:
      - "application/json"
      produces:
      - "application/json"
      parameters:
      - name: "mdoName"
        in: "query"
        description: "master Name"
        required: true
        type: "string"
      - name: "sdoName"
        in: "query"
        description: "slave Name"
        required: true
        type: "string"
      responses:
        200:
          description: "OK"
        400:
          description: "Bad Request"
  /get_config:
    get:
      security:
      - Bearer: []
      tags:
      - "config"
      summary: "Get SDG configuration"
      description: ""
      operationId: "getConfig"
      produces:
      - "application/json"
      parameters: []
      responses:
        200:
          description: "Get sdg config"
          schema:
            $ref: '#/definitions/SDGConfigDTO'
        401:
          description: "Unauthorized"
        400:
          description: "Bad Request"
  /set_active_control:
    post:
      security:
      - Bearer: []
      tags:
      - "manage"
      summary: "set ActiveControl MDOs for all devices"
      description: "set ActiveControl MDOs for all devices"
      operationId: "setActiveControl"
      produces:
      - "application/json"
      consumes:
      - application/x-www-form-urlencoded
      parameters:
      - name: "online"
        in: "formData"
        description: "set ChannelActiveControl true/false ... online/offline"
        required: true
        type: "boolean"
        default: false
      responses:
        200:
          description: "Set ActiveControl"
        400:
          description: "Bad Request"
  /set_active_control_device:
    post:
      security:
      - Bearer: []
      tags:
      - "manage"
      summary: "set ActiveControl MDO for a device"
      description: "set ActiveControl MDO for a device"
      operationId: "setActiveControlDevice"
      produces:
      - "application/json"
      consumes:
      - application/x-www-form-urlencoded
      parameters:
      - name: "channelName"
        in: "formData"
        description: "Channel Name"
        required: true
        type: "string"
        default: false
      - name: "online"
        in: "formData"
        description: "Set ChannelActiveControl true/false ... online/offline"
        required: true
        type: "boolean"
        default: false
      responses:
        200:
          description: "Set ActiveControl"
        400:
          description: "Bad Request"
  /set_sync_needed:
    post:
      security:
      - Bearer: []
      tags:
      - "manage"
      summary: "set/reset the sync needed flag"
      description: "set/reset the sync needed flag"
      operationId: "setSyncNeeded"
      produces:
      - "application/json"
      consumes:
      - application/x-www-form-urlencoded
      parameters:
      - name: "flag"
        in: "formData"
        description: "set ChannelActiveControl true/false ... online/offline"
        required: true
        type: "boolean"
        default: false
      responses:
        200:
          description: "Set SyncNeeded"
        400:
          description: "Bad Request"
definitions:
  INIParamNodeDTO:
    type: "object"
    properties:
      paramName:
        type: "string"
      paramHelp:
        type: "string"
      paramHelpKey:
        type: "string"
      paramLabel:
        type: "string"
      paramLabelKey:
        type: "string"
      paramDefault:
        type: "string"
      paramSection:
        type: "string"
      paramRange:
        type: "string"
  ClassNodeDTO:
    type: "object"
    properties:
      className:
        type: "string"
      baseName:
        type: "string"
  TreeNodePageInfoDTO:
    type: "object"
    properties:
      nodeFullName:
        type: "string"
      numTags:
        type: "number"
      edgePairs:
        type: array
        items:
          $ref: '#/definitions/EdgePairDTO'
  EdgePairDTO:
    type: "object"
    properties:
      startIndex:
        type: "number"
      startName:
        type: "string"
      endIndex:
        type: "number"
      endName:
        type: "string"
  TreeNodeCollectionObjectDTO:
    description: "Collection of TreeNodeDTOs"
    type: "object"
    properties:
      children:
       type: array
       items: 
         $ref: '#/definitions/TreeNodeDTO'
  TreeNodeDTO:
    type: "object"
    properties:
      nodeClassName:
        type: "string"
      nodeIcon:
        type: "integer"
      parentName:
        type: "string"
      nodeName:
        type: "string"
      nodeFullName:
        type: "string"
      nodeLabel:
        type: "string"
      nodeType:
        type: "string"
      nodeDescription:
        type: "string"
      isExpanded:
        type: "boolean"
      isActive:
        type: "boolean"
      isMappingTarget:
        type: "boolean"
      isFilterTarget:
        type: "boolean"
      isHealthy:
        type: "boolean"
      hasChildren:
        type: "boolean"
      isWarningActive:
        type: "boolean"
      nodeCollectionKind:
        type: "string"
        enum:
            - MDO
            - SDO
            - ALL
      memberClass:
        type: "string"
      children:
        type: array
        items:
          $ref: '#/definitions/TreeNodeDTO'
  MappingObjectDTO:
    type: "object"
    properties:
      mdoName:
        type: "string"
      mdoIcon:
        type: "integer"
      sdoName:
        type: "string"
      sdoIcon:
        type: "integer"
      dir:
        type: "integer"
  BoundObjectDTO:
    type: "object"
    properties:
      fullName:
        type: "string"
      canDelete:
        type: "boolean"
      direction:
        type: "string"
        enum:
            - LEFT
            - RIGHT
            - BOTH
  TagObjectDTO:
    description: "Sent by /getTags web socket endpoint, also used by other apis"
    type: "object"
    properties:
      tagName:
        type: "string"
      tagUserName:
        type: "string"
      tagValue:
        type: "string"
      tagQuality:
        type: "string"
      tagTime:
        type: "string"
      tagDescription:
        type: "string"
      tagOptions:
        type: "string"
      tagClassName:
        type: "string"
      tagObjectIcon:
        type: "integer"
      isHealthy:
        type: "boolean"
      tagPropertyMask:
        description: "See GTWDEFS_TAG_PROPERTY_MASK definitions"
        type: "number"
      tagValueType:
        description: "A more user friendly description for the type of the tag"
        type: "string"
      protocolValueType:
        description: "A more user friendly description for the type of the tag specific to the protocol"
        type: "string"
      tagPurposeMask:
        description: "See GTWTYPES_TAG_PURPOSE_MASK definitions"
        type: "number"
      tagBindingList:
        description: "A collection of tags bound to this tag"
        type: array
        items:
          $ref: '#/definitions/BoundObjectDTO'
  TagCollectionObjectDTO:
    description: "Sent by /getTags web socket endpoint and /deleteTags"
    type: "object"
    properties:
      children:
       type: array
       items: 
         $ref: '#/definitions/TagObjectDTO'
  TagValueDTO:
    description: "Tag value"
    type: "object"
    properties:
      tagName:
        type: "string"
      tagValue:
        type: "string"
      tagQuality:
        type: "string"
  TagValuesDTO:
    description: "Sent by /setTags request"
    type: "object"
    properties:
      tags:
       type: array
       items: 
          $ref: '#/definitions/TagValueDTO'
  LogConfigMaskDTO:
    description: "Log configuration mask"
    type: "object"
    properties:
      source:
        type: "string"
      severitymask:
        type: "integer"
      categorymask:
        type: "integer"
  LogDeviceDTO:
    description: "Log device"
    type: "object"
    properties:
      name:
        type: "string"
      add:
        type: "boolean"
  LogDeviceGetDTO:
    description: "Log device"
    type: "object"
    properties:
      name:
        type: "string"
  LogEntryDTO:
    description: "Sent by /getLogEnties web socket endpoint"
    type: "object"
    properties:
      source:
        type: "string"
      name:
        type: "string"
      id:
        type: "integer"
      category:
        type: "string"
      severity:
        type: "string"
      timeStamp:
        type: "string"
      message:
        type: "string"
  SDGConfigDTO:
    description: "represents the data stored in the gtw_config.json file"
    type: "object"
    properties:
#      gtwExeName:
#        type: "string"
      gtwHttpPort:
        type: "integer"
      gtwHost:
        type: "string"
      monHttpPort:
        type: "integer"
      monHost:
        type: "string"
      redHttpPort:
        type: "integer"
      redHost:
        type: "string"
      redIpNic1:
        type: "string"
      redNic1:
        type: "string"
      redIpNic2:
        type: "string"
      redNic2:
        type: "string"
      redPeerHost:
        type: "string"
      redPrimary:
        type: "boolean"
      redEnable:
        type: "boolean"
      redFavorPrimary:
        type: "boolean"
      redEnableSyncConfig:
        type: "boolean"
      redEnableAutoFailover:
        type: "boolean"
      redSlaveTolerancePercentage:
        type: "number"
      redMasterTolerancePercentage:
        type: "number"
      redCheckLocalEngineOnlineTimeout:
        type: "number"
      redCheckRemoteEngineOnlineTimeout:
        type: "number"
      redMonitorReStartRetryLimit:
        type: "number"
      redEngineReStartRetryLimit:
        type: "number"
      gtwWebDir:
        type: "string"
      gtwTzPath:
        type: "string"
      gtwAllowedIPs:
        type: "string"
      #httpsPrivateKeyFile:
      #  type: "string"
      #httpsPrivateKeyPassPhrase:
      #  type: "string"
      #httpsCertificateFile:
      #  type: "string"
      gtwDoAuth:
        type: "boolean"
      gtwPasswordComplexity:
        type: "number"
      gtwDoAudit:
        type: "boolean"
      gtwUseWebSSL:
        type: "boolean"
      gtwHttpsCertIsTmwSigned:
        type: "boolean"
      gtwUseLocalHostForEngineAndMonitorComms:
        type: "boolean"
      gtwEnableHttpDeflate:
        type: "boolean"
      gtwAuthExpVIEWER_ROLE:
        type: "integer"
      gtwAuthExpOPERATOR_ROLE:
        type: "integer"
      gtwAuthExpCONFIGURATOR_ROLE:
        type: "integer"
      gtwAuthExpSU_ROLE:
        type: "integer"
      gtwWsUpdateRate:
        type: "integer"
      gtwWsUpdateBlockSize:
        type: "integer"
      gtwHttpPageBlockSize:
        type: "integer"
      currentWorkSpaceName:
        type: "string"
      gtwMaxLogFiles:
        type: "integer"
      gtwMaxBackupFiles:
        type: "integer"
      fullLogOnRestart:
        type: "boolean"
      mirrorAllToLog:
        type: "boolean"
  SDGHealthObjectDTO:
    description: "response to /health api"
    type: "object"
    properties:
      engineState:
        type: "string"
      engineCpu:
        type: "number"
      engineMem:
        type: "number"
      iniPath:
        type: "string"
      isIniCsvDirty:
        type: "boolean"
      isRedSyncNeeded:
        type: "boolean"
      numConfiguredSlaveDevices:
        type: "number"
      numHealthySlaveDevices:
        type: "number"
      numConfiguredMasterDevices:
        type: "number"
      numHealthyMasterDevices:
        type: "number"
  EditorCommandDTO:
    type: "object"
    properties:
      objectName:
        type: "string"
      parentObjectName:
        type: "string"
      objectClassName:
        type: "string"
      objectCollectionKind:
        type: "string"
      objectDataJson:
        type: "string"
      oldObjectDataJson:
        type: "string"
      arg1:
        type: "string"
      arg2:
        type: "string"
  EditorFieldObjectDTO:
    description: "a specific field of an editor"
    type: "object"
    properties:
      schemaName:
        type: "string"
      name:
        type: "string"
      value:
        type: "string"
      help:
        type: "string"
      defaulthelp:
        type: "string"
      controlType:
        type: "string"
        enum:
          - Label
          - Text
          - TextMultiLine
          - TextAction
          - TextPassword
          - TextId
          - Number
          - Checkbox
          - RadioButton
          - OptionsEditor
          - MaskEditor
          - Combobox
          - ComboboxMultiselect
          - FileManagement
          - FileManagementCombobox
          - Grid
          - GridNoDefault
          - Treeview
          - TreeviewMutliChoice
          - TreeviewDynamic
          - TreeviewDynamicMutliChoice
          - TreeviewDynamicSelectMutliChoice
          - Action
          - Info
          - Hidden
          - AdvanceParameters
          - SelectIP
      controlSource:
        type: "string"
      isRequired:
        type: "boolean"
      isEditable:
        type: "string"
        enum:
          - Yes
          - No
      isEditableAtRuntime:
        type: "string"
        enum:
          - Yes
          - No
      range:
        type: "string"
        description: "json object: for controlType Text {minLen, maxLen} or Number {minValue, maxValue}"
      groupPanelName:
        type: "string"
      isGroupPanelHeader:
        type: "boolean"
      fieldsetName:
        type: "string"
      isFieldsetHeader:
        type: "boolean"
      isLocallyDefined:
        type: "boolean"
      canBeLocallyDefined:
        type: "boolean"
      isMissingLocalFile:
        type: "boolean"
      isMissingLocalValue:
        type: "boolean"
  EditorSpecificationObjectDTO:
    description: "Describes an editable object"
    type: "object"
    properties:
      objectDataJson:
        type: "string"
      editorType:
        type: "string"
      editorKind:
        type: "string"
        enum:
          - CancelOk
          - AddItem
          - CloseSave
          - Close
      children:
        type: array
        items: 
          $ref: '#/definitions/EditorFieldObjectDTO'
  BroadcastEventLogMaskEnumDTO:
    type: string
    enum:
    - refresh_ui
    - refresh_dirty_flag
    - refresh_log_parameter
    - force_log_off_user
    - message_info
    - message_warning
    - message_error
    - message_debug
    - message_success
  BroadcastEventDTO:
    description: "Describes a broadcast event"
    type: "object"
    properties:
      messageLogMask:
        type: "number"
      messageType:
        type: "string"
      messageKey:
        type: "string"
      messageText:
        type: "string"
      messageTime:
        type: "string"
      parameters:
        type: "object"
  TagValueTypeEnumDTO:
    type: string
    enum:
      - Unknown
      - Bool
      - Spare
      - Char
      - Unsigned Char
      - Short
      - Unsigned Short
      - Long
      - Unsigned Int
      - Float
      - Double
      - String
      - Time
      - Int 64
      - Unsigned Int 64
  EditorContextMenuDTO:
    type: "object"
    properties:
      menuText:
        type: "string"
      defaultMenuText:
        type: "string"
      command:
        type: "string"
      isLicensed:
        type: "boolean"
  EditorCommandsDTO:
    type: string
    enum:
    - MENU_CMD_NONE
    - MENU_CMD_SEPARATOR
    - MENU_CMD_EDIT
    - MENU_CMD_START_DEVICE
    - MENU_CMD_STOP_DEVICE
    - MENU_CMD_AUTO_CREATE_TAGS
    - MENU_CMD_EDIT_WORKSPACE_PARAMETERS
    - MENU_CMD_ADD_MBP_CHANNEL
    - MENU_CMD_ADD_TCP_CHANNEL_OUTSTATION_SLAVE
    - MENU_CMD_ADD_TCP_CHANNEL_MASTER
    - MENU_CMD_ADD_DNP3_UDP_TCP_CHANNEL_OUTSTATION_SLAVE
    - MENU_CMD_ADD_DNP3_UDP_TCP_CHANNEL_MASTER
    - MENU_CMD_ADD_DNP3_XML_DEVICE
    - MENU_CMD_ADD_SERIAL_CHANNEL_OUTSTATION_SLAVE
    - MENU_CMD_ADD_SERIAL_CHANNEL_MASTER
    - MENU_CMD_ADD_MODEM_POOL_CHANNEL
    - MENU_CMD_ADD_MODEM
    - MENU_CMD_ADD_MODEM_POOL
    - MENU_CMD_ADD_OPC_CLIENT
    - MENU_CMD_ADD_OPC_UA_CLIENT
    - MENU_CMD_ADD_OPC_UA_CERTIFICATE
    - MENU_CMD_EDIT_OPC_UA_SERVER
    - MENU_CMD_ADD_OPC_AE_CLIENT
    - MENU_CMD_ADD_61850_CLIENTS_FROM_FILE
    - MENU_CMD_ADD_61850_CLIENT
    - MENU_CMD_ADD_TASE2_CLIENT
    - MENU_CMD_ADD_TASE2_SERVER
    - MENU_CMD_ADD_TASE2_CLIENT_SERVER
    - MENU_CMD_ADD_61850_SERVER
    - MENU_CMD_ADD_TASE2_LOGICAL_DEVICE
    - MENU_CMD_RESTART_TASE2_SERVER
    - MENU_CMD_SHOW_CONFIG_TASE2_SERVER
    - MENU_CMD_SHOW_CONFIG_TASE2_CLIENT
    - MENU_CMD_DISCONNECT_TASE2_SERVER
    - MENU_CMD_RESTART_61850_SERVER
    - MENU_CMD_SAVE_TASE2_SERVER
    - MENU_CMD_ADD_ODBC_CLIENT
    - MENU_CMD_ADD_EQ_MDO
    - MENU_CMD_ADD_INTERNAL_MDO
    - MENU_CMD_ADD_61850_REPORT
    - MENU_CMD_ENABLE_RCB
    - MENU_CMD_DISABLE_RCB
    - MENU_CMD_ENABLE_DSTS
    - MENU_CMD_DISABLE_DSTS
    - MENU_CMD_VERIFY_DATASET
    - MENU_CMD_ADD_61850_GOOSE
    - MENU_CMD_ADD_61850_POLLED_DATA_SET
    - MENU_CMD_ADD_61850_POLLED_POINT_SET
    - MENU_CMD_ADD_61850_COMMAND_POINT
    - MENU_CMD_ADD_61850_COMMAND_POINT_SET
    - MENU_CMD_ADD_61850_WRITABLE_POINT
    - MENU_CMD_ADD_61850_WRITABLE_POINT_SET
    - MENU_CMD_ADD_61850_DATASET
    - MENU_CMD_CHANGE_61850_DATASET
    - MENU_CMD_ADD_GOOSE_MONITOR
    - MENU_CMD_ADD_REDUNDANT_SLAVE_CHANNEL
    - MENU_CMD_ADD_REDUNDANT_MASTER_CHANNEL
    - MENU_CMD_DELETE_REDUNDANT_CHANNEL
    - MENU_CMD_ADD_61400_ALARMS_NODE
    - MENU_CMD_ADD_61400_ALARM_MDO
    - MENU_CMD_ADD_61850_ITEM
    - MENU_CMD_CONNECT_TO_61850_SERVER
    - MENU_CMD_DISCONNECT_FROM_61850_SERVER
    - MENU_CMD_SAVE_MODEL_TO_FILE
    - MENU_CMD_EDIT_TASE2_EDIT_MODEL
    - MENU_CMD_EDIT_TASE2_DATA_POINTS
    - MENU_CMD_ADD_TASE2_DSTS
    - MENU_CMD_ADD_TASE2_POLLED_DATA_SET
    - MENU_CMD_ADD_TASE2_POLLED_POINT_SET
    - MENU_CMD_ADD_TASE2_COMMAND_POINT
    - MENU_CMD_ADD_TASE2_COMMAND_POINT_SET
    - MENU_CMD_ADD_TASE2_ITEM
    - MENU_CMD_ADD_TASE2_DATASET
    - MENU_CMD_MANAGE_TASE2_DATASET
    - MENU_CMD_MANAGE_TASE2_DATASET_FULL_EDIT
    - MENU_CMD_OPERATE_TASE2_CONTROL
    - MENU_CMD_CONNECT_TO_TASE2_SERVER
    - MENU_CMD_CONNECT_TO_DISCOVER_TASE2_SERVER
    - MENU_CMD_DISCONNECT_FROM_TASE2_SERVER
    - MENU_CMD_ADD_OPC_ITEM
    - MENU_CMD_ADD_MULTIPLE_OPC_ITEM
    - MENU_CMD_ADD_MULTIPLE_OPC_UA_ITEM
    - MENU_CMD_ADD_ODBC_ITEM
    - MENU_CMD_ADD_OPC_AE_ITEM
    - MENU_CMD_ADD_OPC_UA_ITEM
    - MENU_CMD_ADD_SESSION
    - MENU_CMD_ADD_MDO
    - MENU_CMD_ADD_MULTIPLE_MDO
    - MENU_CMD_ADD_MAPPING_SDO
    - MENU_CMD_ADD_MAPPING_SDOS
    - MENU_CMD_ADD_SECTOR
    - MENU_CMD_ADD_DATA_TYPE
    - MENU_CMD_ADD_DNP_PROTO
    - MENU_CMD_ADD_DNP_DESCP
    - MENU_CMD_ADD_DATASET_ELEMENT
    - MENU_CMD_ADD_OPC_AE_ATTR
    - MENU_CMD_DELETE
    - MENU_CMD_CHANGE_VALUE
    - MENU_CMD_SET_VALUE_AND_QUALITY
    - MENU_CMD_READ_DNP_PROTO
    - MENU_CMD_WRITE_DNP_PROTO
    - MENU_CMD_READ_DNP_DESCP
    - MENU_CMD_WRITE_DNP_DESCP
    - MENU_CMD_RESET_61850_RETRY_CONNECT_COUNT
    - MENU_CMD_READ_61850_MDO
    - MENU_CMD_ADD_WRITE_ACTION
    - MENU_CMD_ADD_MULTI_POINT
    - MENU_CMD_PERFORM_WRITE_ACTION
    - MENU_CMD_WRITE_MULTI_POINT
    - MENU_CMD_SELECT_DATA_ATTRIBUTE
    - MENU_CMD_ADD_USER_DEFINED_FOLDER
    - MENU_CMD_READ_DATASET
    - MENU_CMD_READ_OPC_UA_MDO
    - MENU_CMD_READ_OPC_DA_MDO
  ProtocolTypesEnumDTO:
    type: string
    enum:
    - GTWTYPES_PROTOCOL_S101
    - GTWTYPES_PROTOCOL_S102
    - GTWTYPES_PROTOCOL_S103
    - GTWTYPES_PROTOCOL_S104
    - GTWTYPES_PROTOCOL_SDNP
    - GTWTYPES_PROTOCOL_M101
    - GTWTYPES_PROTOCOL_M102
    - GTWTYPES_PROTOCOL_M103
    - GTWTYPES_PROTOCOL_M104
    - GTWTYPES_PROTOCOL_MDNP
    - GTWTYPES_PROTOCOL_MMB
    - GTWTYPES_PROTOCOL_SMB
  TargetTCPModeEnumDTO:
    type: string
    enum:
    - TMWTARGTCP_MODE_SERVER
    - TMWTARGTCP_MODE_CLIENT
    - TMWTARGTCP_MODE_UDP
    - TMWTARGTCP_MODE_DUAL_ENDPOINT
  TargetTypeEnumDTO:
    type: string
    enum:
    - TMWTARGIO_TYPE_232
    - TMWTARGIO_TYPE_MODEM_POOL_CHANNEL
    - TMWTARGIO_TYPE_MODEM_POOL
    - TMWTARGIO_TYPE_MODEM
    - TMWTARGIO_TYPE_TCP
    - TMWTARGIO_TYPE_UDP_TCP
    - TMWTARGIO_TYPE_MBP
    - TMWTARGIO_TYPE_MON
  GTWTYPES_TAG_PURPOSE_MASK:
    type: integer
    format: int32
    title: "GTWTYPES_TAG_PURPOSE_MASK"
    description: "Tag purpose mask for filtering tags"
    default: 0
    enum:
      - 0x01  # GTWTYPES_TAG_PURPOSE_MASK_HEALTH
      - 0x02  # GTWTYPES_TAG_PURPOSE_MASK_PERFORMANCE
      - 0x04  # GTWTYPES_TAG_PURPOSE_MASK_DATA
      - 0x08  # GTWTYPES_TAG_PURPOSE_MASK_REDUNDANCY
      - 0x10  # GTWTYPES_TAG_PURPOSE_MASK_UNHEALTHY
      - 0x20  # GTWTYPES_TAG_PURPOSE_MASK_GET_MAPPINGS_CSV
      - 0x40  # GTWTYPES_TAG_PURPOSE_MASK_GET_POINTS_CSV
      - 0x80  # GTWTYPES_TAG_PURPOSE_MASK_RESET_TO_ZERO
    x-enum-varnames:
      - GTWTYPES_TAG_PURPOSE_MASK_HEALTH
      - GTWTYPES_TAG_PURPOSE_MASK_PERFORMANCE
      - GTWTYPES_TAG_PURPOSE_MASK_DATA
      - GTWTYPES_TAG_PURPOSE_MASK_REDUNDANCY
      - GTWTYPES_TAG_PURPOSE_MASK_UNHEALTHY
      - GTWTYPES_TAG_PURPOSE_MASK_GET_MAPPINGS_CSV
      - GTWTYPES_TAG_PURPOSE_MASK_GET_POINTS_CSV
      - GTWTYPES_TAG_PURPOSE_MASK_RESET_TO_ZERO    