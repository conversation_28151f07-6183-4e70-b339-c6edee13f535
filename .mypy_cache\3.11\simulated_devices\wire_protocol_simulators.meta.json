{"data_mtime": 1758122932, "dep_lines": [16, 16, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 976, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 25], "dep_prios": [10, 20, 10, 10, 10, 10, 10, 10, 20, 5, 5, 5, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["xml.etree.ElementTree", "xml.etree", "asyncio", "logging", "struct", "socket", "time", "<PERSON><PERSON><PERSON><PERSON>", "xml", "datetime", "typing", "enum", "dataclasses", "json", "traceback", "builtins", "_asyncio", "_contextvars", "_frozen_importlib", "_socket", "_typeshed", "abc", "asyncio.base_events", "asyncio.events", "asyncio.streams", "types"], "hash": "ae92ec6163107f7b8276a9c9b21bbe7c8681324c", "id": "simulated_devices.wire_protocol_simulators", "ignore_all": true, "interface_hash": "6e53f5c3011724626a275e900a3d32f9b0eefb75", "mtime": 1758120348, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\Quanta\\simulated_devices\\wire_protocol_simulators.py", "plugin_data": null, "size": 65606, "suppressed": ["crc16"], "version_id": "1.15.0"}