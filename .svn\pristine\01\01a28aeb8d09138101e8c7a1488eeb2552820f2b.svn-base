"""
Simulated Field Device Framework for OpenMIC Python Container
CR-010: Comprehensive power system device simulation for integration testing

This package provides realistic simulation of:
- Digital Fault Recorders (DFRs)
- Power Meters
- Protective Relays
- Phasor Measurement Units (PMUs)

Features:
- IEEE C37.118 synchrophasor standards compliance
- IEEE C37.111 COMTRADE file generation
- IEEE 1159.3 PQDIF file generation
- Realistic power system waveform generation
- Event-driven fault injection
- Multi-device coordination
"""

# Core simulator classes
from .power_system_simulator import (
    PowerSystemSimulator,
    DeviceConfig,
    DeviceType,
    EventType,
    PowerSystemState
)

# Device factory for creating configured devices
from .device_factory import DeviceFactory, DeviceProfile

# Event generation and coordination
from .event_generator import EventGenerator, EventScenario, EventSeverity

# File generation for IEEE standards
from .file_generators import COMTRADEGenerator, PQDIFGenerator, FileCoordinator



# Enhanced protocol communication
from .protocol_communication import (
    ProtocolMessageLogger,
    CommunicationErrorSimulator,
    ProtocolLatencySimulator,
    SecurityTestingFramework,
    LoadTestingFramework,
    ProtocolConfig,
    DNP3Config,
    ModbusConfig,
    IEC61850Config
)

# File transfer simulation
from .file_transfer_simulation import (
    FileTransferCoordinator,
    HTTPFileServer,
    FTPServer,
    SFTPServer,
    FileTransferClient
)

# Triangle MicroWorks integration
from .triangle_microworks_integration import (
    TriangleMicroWorksClient,
    TMWIntegrationCoordinator,
    TMWConfig,
    DeviceConnection
)

# Wire-level protocol implementations
from .wire_protocol_simulators import (
    WireLevelDNP3Simulator,
    WireLevelModbusSimulator,
    WireLevelIEC61850Simulator,
    DNP3DataPoint,
    IEC61850DataAttribute
)

# Wire-level protocol coordinator
from .wire_protocol_coordinator import (
    WireProtocolCoordinator,
    ProtocolType as WireProtocolType
)

# Unified protocol server (RECOMMENDED)
from .unified_protocol_server import (
    UnifiedProtocolServer,
    ProtocolServerFactory,
    ProtocolServerConfig,
    ProtocolType as UnifiedProtocolType
)

# Main coordination class
from .simulation_coordinator import SimulationCoordinator

__version__ = "1.0.0"
__author__ = "OpenMIC Python Container Team"
__description__ = "Comprehensive power system device simulation framework for CR-010"

__all__ = [
    # Core classes
    "PowerSystemSimulator",
    "DeviceConfig",
    "DeviceType",
    "EventType",
    "PowerSystemState",

    # Factory and profiles
    "DeviceFactory",
    "DeviceProfile",

    # Event management
    "EventGenerator",
    "EventScenario",
    "EventSeverity",

    # File generation
    "COMTRADEGenerator",
    "PQDIFGenerator",
    "FileCoordinator",


    # Enhanced protocol communication
    "ProtocolMessageLogger",
    "CommunicationErrorSimulator",
    "ProtocolLatencySimulator",
    "SecurityTestingFramework",
    "LoadTestingFramework",
    "ProtocolConfig",
    "DNP3Config",
    "ModbusConfig",
    "IEC61850Config",

    # File transfer simulation
    "FileTransferCoordinator",
    "HTTPFileServer",
    "FTPServer",
    "SFTPServer",
    "FileTransferClient",

    # Triangle MicroWorks integration
    "TriangleMicroWorksClient",
    "TMWIntegrationCoordinator",
    "TMWConfig",
    "DeviceConnection",

    # Wire-level protocol implementations
    "WireLevelDNP3Simulator",
    "WireLevelModbusSimulator",
    "WireLevelIEC61850Simulator",
    "DNP3DataPoint",
    "IEC61850DataAttribute",

    # Wire-level protocol coordinator
    "WireProtocolCoordinator",
    "WireProtocolType",

    # Unified protocol server (RECOMMENDED)
    "UnifiedProtocolServer",
    "ProtocolServerFactory",
    "ProtocolServerConfig",
    "UnifiedProtocolType",

    # Main coordinator
    "SimulationCoordinator"
]
