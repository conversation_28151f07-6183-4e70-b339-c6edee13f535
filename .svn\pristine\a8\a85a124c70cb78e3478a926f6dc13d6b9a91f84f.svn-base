---
swagger: "2.0"
info:
  description: "SDG Redundancy API"
  version: "1.0.0"
  title: "SDG Redundancy"
#host: "localhost:58070"
basePath: "/rest"
schemes:
  - http
  - https
securityDefinitions:
  Bearer:
    type: apiKey
    name: Authorization
    in: header
paths:
  /get_red_state:
    get:
      security:
      - Bearer: []
      tags:
      - "config"
      summary: "Get Redundancy state"
      description: "Get Redundancy state"
      operationId: "getRedState"
      produces:
      - "application/json"
      parameters: []
      responses:
        200:
          description: "Get redundancy state"
          schema:
            $ref: '#/definitions/RedStateObjectDTO'
  /get_red_status:
    get:
      security:
      - Bearer: []
      tags:
      - "config"
      summary: "Get Redundancy health"
      description: "Get Redundancy health"
      operationId: "getRedStatus"
      produces:
      - "application/json"
      parameters: []
      responses:
        200:
          description: "Get redundancy health status"
          schema:
            $ref: '#/definitions/RedHealthObjectDTO'
  /get_config:
    get:
      security:
      - Bearer: []
      tags:
      - "config"
      summary: "Get SDG configuration"
      description: ""
      operationId: "getConfig"
      produces:
      - "application/json"
      parameters: []
      responses:
        200:
          description: "Get sdg config"
          schema:
            $ref: '#/definitions/SDGConfigDTO'
        401:
          description: "Unauthorized"
        400:
          description: "Bad Request"
  /process_broadcast_event:
    post:
      security:
      - Bearer: []
      tags:
      - "config"
      summary: "Process a broadcast event"
      description: "messageLogMaskEnum:   NONE = 0, EVENT_LOG = 1,ALERT_POPUP = 2,STATUS_BAR = 4  messageTypeEnum: refresh_ui, refresh_dirty_flag, refresh_log_parameter, force_log_off_user, message_info, message_warning, message_error, message_debug, message_success"
      operationId: "processBroadcastEvent"
      consumes:
      - application/json
      parameters:
      - name: "body"
        in: "body"
        required: true
        schema:
           $ref:  '#/definitions/BroadcastEventDTO'
      responses:
        200:
          description: "Success"
        400:
          description: "Bad Request"
  /stopRed:
    post:
      security:
      - Bearer: []
      tags:
      - "redundancy"
      summary: "Stop Redundancy"
      description: "Stops the Redundancy Manager"
      operationId: "stopRed"
      consumes:
      - application/x-www-form-urlencoded
      produces:
      - "application/json"
      responses:
        200:
          description: "Success"
        400:
          description: "Bad Request"
  /file:
    get:
      security:
      - Bearer: []
      tags:
      - "file"
      summary: "download a file of a specific type"
      description: ""
      operationId: "fileGet"
      produces: 
      - "application/octet-stream"
      consumes:
      - "application/json"
      parameters:
      - name: "fileName"
        in: "query"
        description: "file name/path"
        required: true
        type: "string"
      - name: "fileType"
        in: "query"
        description: "file type"
        required: true
        type: "string"
      responses:
        200:
          description: "Download file"
          schema:
            type: file
        400:
          description: "Bad Request"
    post:
      security:
      - Bearer: []
      tags:
      - "file"
      summary: "upload a file for a work space"
      description: ""
      operationId: "filePost"
      produces:
      - "application/json"
      consumes:
      - "multipart/form-data"
      parameters:
      - name: "file"
        in: "formData"
        description: "file"
        required: true
        type: "file"
      - name: "fileType"
        in: "query"
        description: "file type"
        required: false
        type: "string"
      - name: "workspaceName"
        in: "query"
        description: "workspace name if blank its the current work space"
        required: false
        type: "string"
      responses:
        200:
          description: "Upload file"
        400:
          description: "Bad Request"
    delete:
      security:
      - Bearer: []
      tags:
      - "file"
      summary: "delete a file of a specific type"
      description: ""
      operationId: "fileDelete"
      produces: 
      - "application/octet-stream"
      consumes:
      - "application/json"
      parameters:
      - name: "fileName"
        in: "query"
        description: "file name/path"
        required: true
        type: "string"
      - name: "fileType"
        in: "query"
        description: "file type"
        required: false
        type: "string"
      - name: "workspaceName"
        in: "query"
        description: "workspace name if blank its the current work space"
        required: false
        type: "string"
      responses:
        200:
          description: "Delete file"
          schema:
            type: file
        400:
          description: "Bad Request"
  /files:
    get:
      security:
      - Bearer: []
      tags:
      - "file"
      summary: "list files of a requested type"
      description: ""
      operationId: "filesGet"
      produces:
      - "application/json"
      consumes:
      - "application/json"
      parameters:
      - name: "fileExtensions"
        in: "query"
        description: "file extensions"
        required: false
        type: "string"
      - name: "fileType"
        in: "query"
        description: "file type"
        required: true
        type: "string"
      - name: "workspaceName"
        in: "query"
        description: "workspace name"
        required: false
        type: "string"
      responses:
        200:
          description: "OK"
        400:
          description: "Bad Request"
  /setPeerActive:
    post:
      security:
      - Bearer: []
      tags:
      - "redundancy"
      summary: "Make the peer machine active"
      description: "Make the peer machine active"
      operationId: "setPeerActive"
      consumes:
      - application/x-www-form-urlencoded
      produces:
      - "application/json"
      parameters:
      - name: "forceSwitchOver"
        in: "query"
        description: "if true will force the peer active"
        required: true
        type: boolean
      responses:
        200:
          description: "Success"
        400:
          description: "Bad Request"
  /syncConfig:
    post:
      security:
      - Bearer: []
      tags:
      - "redundancy"
      summary: "Synchronize the configuration"
      description: "Synchronize the configuration from the active machine to the in-active machine"
      operationId: "syncConfig"
      consumes:
      - application/x-www-form-urlencoded
      produces:
      - "application/json"
      parameters:
      - name: "forceSync"
        in: "query"
        description: "if true will force a sync of the config"
        required: true
        type: boolean
      responses:
        200:
          description: "Success"
        400:
          description: "Bad Request"
  /sharedStatusInformation:
    get:
      security:
      - Bearer: []
      tags:
      - "redundancy"
      summary: "Get shared status information."
      description: "shared status information consists of i.	Heartbeat ii.	Am I Active? iii.	# of Devices Connected iv.	Status of Inactive Gateway"
      operationId: "getSharedStatusInformation"
      produces:
      - "application/json"
      parameters:
      - name: "resyncHeartBeatFlag"
        in: "query"
        description: "set true to cause a re-sync of the heart beat"
        required: false
        type: boolean
      - name: "noIncrementHeartBeatFlag"
        in: "query"
        description: "set true to not increment the heart beat"
        required: false
        type: boolean
      responses:
        200:
          description: "OK"
          schema:
            type: array
            items:
              $ref: '#/definitions/SharedStatusInformationDTO'
        204:
          description: "No Content"
        400:
          description: "Bad Request"
  /get_log_entries_range:
    get:
      security:
      - Bearer: []
      tags:
      - "log"
      summary: "Get log items."
      description: "Get protocol data object items as array"
      operationId: "getLogEntriesRange"
      produces:
      - "application/json"
      parameters:
      - name: "startEntryID"
        in: "query"
        description: "Retreive log entries starting with startEntryID"
        required: false
        type: "number"
      - name: "endEntryID"
        in: "query"
        description: "Retreive log entries ending with endEntryID"
        required: false
        type: "number"
      responses:
        200:
          description: "OK"
          schema:
            type: array
            items:
              $ref: '#/definitions/LogEntryDTO'
        204:
          description: "No Content"
        400:
          description: "Bad Request"
  /get_num_log_entries:
    get:
      security:
      - Bearer: []
      tags:
      - "log"
      summary: "Retrieve number of lines in protocol buffer"
      description: "Get number of lines"
      operationId: "getNumLogEntries"
      produces:
      - "application/json"
      parameters: []
      responses:
        200:
          schema:
            type: number
          description: "Number of lines"
        400:
          description: "Bad Request"
  /get_mirror_all_to_log_file:
    get:
      security:
      - Bearer: []
      tags:
      - "log"
      summary: "Retrieve MirrorAllToLogFile setting for logger"
      description: "Get MirrorAllToLogFile setting"
      operationId: "getMirrorAllToLogFile"
      produces:
      - "application/json"
      parameters: []
      responses:
        200:
          description: "Get MirrorAllToLogFile setting"
        400:
          description: "Bad Request"
  /set_mirror_all_to_log_file:
    post:
      security:
      - Bearer: []
      tags:
      - "log"
      summary: "Set MirrorAllToLogFile setting for logger"
      description: "Set MirrorAllToLogFile setting"
      operationId: "putMirrorAllToLogFile"
      produces:
      - "application/json"
      consumes:
      - application/x-www-form-urlencoded
      parameters:
      - name: "mirrorAllToLogFile"
        in: "query"
        description: "True to Mirror All To Log File"
        required: true
        type: "boolean"
      responses:
        200:
          description: "Success"
        400:
          description: "Bad Request"
  /set_pause_logging:
    post:
      security:
      - Bearer: []
      tags:
      - "log"
      summary: "Set Pause setting for logger"
      description: "Set true to pause logging"
      operationId: "setPauseLogging"
      produces:
      - "application/json"
      consumes:
      - application/x-www-form-urlencoded
      parameters:
      - name: "pauseLogging"
        in: "query"
        description: "True to pause logging"
        required: true
        type: "boolean"
      responses:
        200:
          description: "Success"
        400:
          description: "Bad Request"
  /get_last_log_entry_id:
    get:
      security:
      - Bearer: []
      tags:
      - "log"
      summary: "Retrieve last log entry id"
      description: "Get last log entry id"
      operationId: "getLastLogEntryID"
      produces:
      - "application/json"
      parameters: []
      responses:
        200:
          schema:
            type: number
          description: "Last log entry id"
        400:
          description: "Bad Request"
  /get_max_log_entries:
    get:
      security:
      - Bearer: []
      tags:
      - "log"
      summary: "Retrieve max log entries to store on server"
      description: "Get max log entries"
      operationId: "getMaxLogEntries"
      produces:
      - "application/json"
      parameters: []
      responses:
        200:
          schema:
            type: number
          description: "Max log entries setting"
        400:
          description: "Bad Request"
  /set_max_log_entries:
    post:
      security:
      - Bearer: []
      tags:
      - "log"
      summary: "Set max log entries"
      description: "Set max log entries"
      operationId: "putMaxLogEntries"
      produces:
      - "application/json"
      consumes:
      - application/x-www-form-urlencoded
      parameters:
      - name: "maxLogEntries"
        in: "query"
        description: "Max log entries to store on server"
        required: true
        type: "integer"
        format: "int64"
      responses:
        200:
          description: "Success"
        400:
          description: "Bad Request"  
  /get_log_filter_config:
    get:
      security:
      - Bearer: []
      tags:
      - "log"
      summary: "Retrieve severity and category masks for each of source: SDG=0, SCL=1, 6T=2"
      description: "Retrieve log filter masks"
      operationId: "getLogFilterConfig"
      produces:
      - "application/json"
      parameters: []
      responses:
        200:
          description: "Retrieve severity and category masks for each of source: SDG=0, SCL=1, 6T=2"
          schema:
            type: array
            items:
              $ref: '#/definitions/LogConfigMaskDTO'
        400:
          description: "Bad Request"
  /set_log_filter_config:
    post:
      security:
      - Bearer: []
      tags:
      - "log"
      summary: "Set severity mask"
      description: "Set severity mask"
      operationId: "putLogFilterConfig"
      produces:
      - "application/json"
      consumes:
      - application/json
      parameters:
      - name: "logconfigmasks"
        in: "body"
        description: "Severity and category mask for each source"
        required: true
        schema:
          type: array
          items:
            $ref: '#/definitions/LogConfigMaskDTO'
      responses:
        200:
          description: "Set Severity and category mask for each source"
        400:
          description: "Bad Request"
  /set_max_suppression_time:
    post:
      security:
      - Bearer: []
      tags:
      - "log"
      summary: "Set max log suppression time"
      description: "Set log suppresion time in seconds, 0 disables"
      operationId: "setMaxSuppressionTime"
      produces:
      - "application/json"
      consumes:
      - application/x-www-form-urlencoded
      parameters:
      - name: "maxSuppressionTime"
        in: "query"
        description: "How long to suppress duplicate log messages in seconds"
        required: true
        type: "integer"
        format: "int64"
      responses:
        200:
          description: "Success"
        400:
          description: "Bad Request"
  /get_max_suppression_time:
    get:
      security:
      - Bearer: []
      tags:
      - "log"
      summary: "Retrieve max log suppression time"
      description: "Get  log suppression time in seconds"
      operationId: "getMaxSuppressionTime"
      produces:
      - "application/json"
      parameters: []
      responses:
        200:
          schema:
            type: number
          description: "Max log suppresion time"
        400:
          description: "Bad Request"
definitions: 
  LogConfigMask:
    description: "Log configuration mask"
    type: "object"
    properties:
      source:
        type: "string"
      severitymask:
        type: "integer"
      categorymask:
        type: "integer"
  LogEntry:
    description: "Sent by /getLogEnties web socket endpoint"
    type: "object"
    properties:
      source:
        type: "string"
      name:
        type: "string"
      id:
        type: "integer"
      category:
        type: "string"
      severity:
        type: "string"
      timeStamp:
        type: "string"
      message:
        type: "string"
  SDGConfigDTO:
    description: "represents the data stored in the gtw_config.json file"
    type: "object"
    properties:
      gtwDoValidateConfig:
        type: "boolean"
      gtwDoValidateIntegrity:
        type: "boolean"
      gtwDoWorkSpaceMD5Verification:
        type: "boolean"
#      gtwExeName:
#        type: "string"
      gtwHttpPort:
        type: "integer"
      gtwHost:
        type: "string"
      monHttpPort:
        type: "integer"
      monHost:
        type: "string"
      redHttpPort:
        type: "integer"
      redHost:
        type: "string"
      redIpNic1:
        type: "string"
      redNic1:
        type: "string"
      redIpNic2:
        type: "string"
      redNic2:
        type: "string"
      redPeerHost:
        type: "string"
      redPrimary:
        type: "boolean"
      redEnable:
        type: "boolean"
      redFavorPrimary:
        type: "boolean"
      redEnableSyncConfig:
        type: "boolean"
      redEnableAutoFailover:
        type: "boolean"
      redSlaveTolerancePercentage:
        type: "number"
      redMasterTolerancePercentage:
        type: "number"
      redCheckLocalEngineOnlineTimeout:
        type: "number"
      redCheckRemoteEngineOnlineTimeout:
        type: "number"
      redMonitorReStartRetryLimit:
        type: "number"
      redEngineReStartRetryLimit:
        type: "number"
      gtwWebDir:
        type: "string"
      gtwTzPath:
        type: "string"
      gtwAllowedIPs:
        type: "string"
      #httpsPrivateKeyFile:
      #  type: "string"
      #httpsPrivateKeyPassPhrase:
      #  type: "string"
      #httpsCertificateFile:
      #  type: "string"
      gtwDoAuth:
        type: "boolean"
      gtwPasswordComplexity:
        type: "number"
      gtwDoAudit:
        type: "boolean"
      gtwUseWebSSL:
        type: "boolean"
      gtwHttpsCertIsTmwSigned:
        type: "boolean"
      gtwUseLocalHostForEngineAndMonitorComms:
        type: "boolean"
      gtwEnableHttpDeflate:
        type: "boolean"
      gtwAuthExpVIEWER_ROLE:
        type: "integer"
      gtwAuthExpOPERATOR_ROLE:
        type: "integer"
      gtwAuthExpCONFIGURATOR_ROLE:
        type: "integer"
      gtwAuthExpSU_ROLE:
        type: "integer"
      gtwWsUpdateRate:
        type: "integer"
      gtwWsUpdateBlockSize:
        type: "integer"
      gtwHttpPageBlockSize:
        type: "integer"
      currentWorkSpaceName:
        type: "string"
      gtwMaxLogFiles:
        type: "integer"
      gtwMaxBackupFiles:
        type: "integer"
      fullLogOnRestart:
        type: "boolean"
  TraceMaskEnumDTO:
    type: "object"
    properties:
      maskValue:
        type: "integer"
        enum:
        - value: 0x00000000
          description: TRACE_MASK_TRACE_NONE
        - value: 0x00000001
          description: TRACE_MASK_TRACE_STATUS
        - value: 0x00000002
          description: TRACE_MASK_APP_START_STOP
        - value: 0x00000004
          description: TRACE_MASK_APP_STATUS
        - value: 0x00000008
          description: TRACE_MASK_APP_ERROR
        - value: 0x00000010
          description: TRACE_MASK_APP_HTTP
        - value: 0x00000020
          description: TRACE_MASK_LICENSE
        - value: 0x00000040
          description: TRACE_MASK_OPC
        - value: 0x00000080
          description: TRACE_MASK_SERVICE_PROTO
        - value: 0x00000100
          description: TRACE_MASK_CUSTOM
        - value: 0x00000200
          description: TRACE_MASK_TARGET
        - value: 0x00000400
          description: TRACE_MASK_OPC_SU
        - value: 0x00000800
          description: TRACE_MASK_OPC_UA
  RedStateObjectDTO:
    description: "response for get_red_state api"
    type: "object"
    properties:
      redundancyState: # SET_ACTIVE_RESULT enum
        type: "string"
      redundancySyncState: # DO_SYNC_RESULT enum
        type: "string"
  RedHealthObjectDTO:
    description: "Sent by /getHealth web socket endpoint"
    type: "object"
    properties:
      engineState:
        type: "string"
      monitorState:
        type: "string"
      engineExitFailState:
        type: "string"
      redundantHealthy:
        type: "boolean"
      configSyncRequired:
        type: "boolean"
      redCpu:
        type: "number"
      redMem:
        type: "number"
      engineCpu:
        type: "number"
      engineMem:
        type: "number"
      monitorCpu:
        type: "number"
      monitorMem:
        type: "number"
  BroadcastEventDTO:
    description: "Describes a broadcast event"
    type: "object"
    properties:
      messageLogMask:
        type: "number"
      messageType:
        type: "string"
      messageKey:
        type: "string"
      messageText:
        type: "string"
      messageTime:
        type: "string"
      parameters:
        type: "object"
  LogConfigMaskDTO:
    description: "Log configuration mask"
    type: "object"
    properties:
      source:
        type: "string"
      severitymask:
        type: "integer"
      categorymask:
        type: "integer"
  LogEntryDTO:
    description: "Sent by /getLogEnties web socket endpoint"
    type: "object"
    properties:
      source:
        type: "string"
      name:
        type: "string"
      id:
        type: "integer"
      category:
        type: "string"
      severity:
        type: "string"
      timeStamp:
        type: "string"
      message:
        type: "string"
  SharedStatusInformationDTO:
    type: "object"
    properties:
      pairSDGStatus:
        type: "string"
      engineState:
        type: "string"
      monitorState:
        type: "string"
      heartBeat:
        type: "number"
      heartBeatWrapped:
        type: "boolean"
      isPeerActive:
        type: "boolean"
      numSlaveDevices:
        type: "number"
      numSlaveDevicesHealthy:
        type: "number"
      numMasterDevices:
        type: "number"
      numMasterDevicesHealthy:
        type: "number"
      redundantHealthy:
        type: "boolean"
      configSyncRequired:
        type: "boolean"
