# Simulated Power System Devices Framework

## CR-010: Comprehensive Power System Device Simulator

This framework provides a complete simulation environment for power system devices, designed for integration testing of the OpenMIC Python Container system. It creates realistic device behavior, generates IEEE-compliant files, and supports comprehensive testing scenarios.

## Features

### Device Types Supported
- **Digital Fault Recorders (DFRs)** - High-speed waveform capture and fault analysis
- **Power Meters** - Revenue metering and power quality monitoring  
- **Protective Relays** - Trip logic, protection elements, and event recording
- **Phasor Measurement Units (PMUs)** - IEEE C37.118 synchrophasor measurements

### Standards Compliance
- **IEEE C37.118** - Synchrophasor standards for PMU simulation
- **IEEE C37.111** - COMTRADE file generation for fault events
- **IEEE 1159.3** - PQDIF file generation for power quality events
- **DNP3** - Full wire-level implementation with proper frame structure, CRC calculation, and all function codes
- **Modbus TCP/RTU** - Complete protocol implementation with correct PDU encoding and exception handling
- **IEC 61850** - MMS protocol stack with logical device modeling and GOOSE multicast publishing
- **FTP, SFTP, HTTP** - File transfer protocol simulation for device file management

### Key Capabilities
- **Realistic Waveform Generation** - 3-phase voltage/current with harmonics and noise
- **Event-Driven Fault Injection** - Configurable fault scenarios with timing control
- **Multi-Device Coordination** - System-wide event simulation and cascading effects
- **Device Health Simulation** - Communication failures and degradation modeling
- **Performance Metrics** - Comprehensive simulation statistics and monitoring
- **File Generation** - Automatic COMTRADE and PQDIF file creation
- **Wire-Level Protocol Implementation** - Real DNP3, Modbus, and IEC 61850 servers that work on the wire
- **Protocol Compliance** - Full protocol stack implementation with proper encoding/decoding
- **Interoperability Testing** - Compatible with real protocol clients and SCADA systems
- **File Transfer Protocols** - FTP, SFTP, and HTTP servers for device file management
- **Communication Error Simulation** - Realistic error injection and recovery testing
- **Protocol Timing Simulation** - Network latency and jitter simulation
- **Security Testing** - Authentication failure simulation and lockout testing
- **Load Testing** - Concurrent protocol session performance testing
- **Message Logging** - Comprehensive protocol message analysis and logging
- **Triangle MicroWorks Integration** - Full SCADA Gateway REST API integration

## Architecture

### Unified Protocol Server (Production Use)
```
UnifiedProtocolServer
├── WireLevelDNP3Simulator (real DNP3 protocol)
├── WireLevelModbusSimulator (real Modbus TCP protocol)
├── WireLevelIEC61850Simulator (real IEC 61850 MMS + GOOSE)
├── ProtocolMessageLogger (comprehensive logging)
├── CommunicationErrorSimulator (error injection)
├── LoadTestingFramework (performance testing)
└── ProtocolServerFactory (easy configuration)
```

### Power System Simulation Framework
```
SimulationCoordinator
├── DeviceFactory (creates devices from profiles)
├── EventGenerator (coordinates system events)
├── FileCoordinator (manages file generation)
└── Multiple PowerSystemSimulators
    ├── Device-specific measurement generation
    ├── Event response simulation
    ├── Health monitoring
    └── Protocol integration
```

## Quick Start

### Production Protocol Server (Recommended)

```python
from simulated_devices import ProtocolServerFactory

# Create a protective relay server with DNP3 and IEC 61850
server = ProtocolServerFactory.create_relay_server("RELAY_01", base_port=20000)

# Start all protocol servers
await server.start_all_servers()

# Update measurements (propagates to all protocols)
server.update_measurement('voltage_a', 138000.0)
server.update_measurement('current_a', 150.0)
server.update_status('breaker_status', True)

# Get connection information for clients
connection_info = server.get_connection_info()
print(f"DNP3 server: localhost:{connection_info['protocols']['dnp3']['port']}")
print(f"IEC 61850 server: localhost:{connection_info['protocols']['iec61850']['port']}")
```

### Power System Simulation

```python
import asyncio
from simulated_devices import SimulationCoordinator

async def main():
    # Create coordinator
    coordinator = SimulationCoordinator()
    
    # Create substation from configuration
    substation_config = {
        "name": "TEST_SUBSTATION",
        "devices": [
            {
                "device_id": "RELAY_1",
                "profile": "schweitzer_sel351",
                "config": {"nominal_voltage": 138000.0}
            },
            {
                "device_id": "DFR_1", 
                "profile": "qualitrol_dfr",
                "config": {"sampling_rate": 15360}
            }
        ]
    }
    
    coordinator.create_substation_from_config(substation_config)
    
    # Start simulation
    await coordinator.start_simulation()

if __name__ == "__main__":
    asyncio.run(main())
```

### Using Device Factory

```python
from simulated_devices import DeviceFactory, DeviceType

# Create factory
factory = DeviceFactory()

# Create specific device
device = factory.create_device(
    device_id="RELAY_LINE_1",
    profile_name="schweitzer_sel351", 
    station_name="SUBSTATION_A",
    custom_config={"nominal_voltage": 138000.0}
)

# Start device simulation
await device.start_simulation()
```

### Event Generation

```python
from simulated_devices import EventGenerator, EventScenario, EventSeverity, EventType

# Create event generator
event_gen = EventGenerator()

# Register devices
event_gen.register_devices([device1, device2, device3])

# Create custom scenario
scenario = EventScenario(
    name="custom_fault",
    description="Custom transmission line fault",
    event_type=EventType.LINE_FAULT,
    severity=EventSeverity.HIGH,
    duration_range=(0.1, 2.0),
    affected_devices=["relay_*"],
    probability=0.5
)

event_gen.add_custom_scenario("custom_fault", scenario)

# Start automatic event generation
await event_gen.start_event_generation()
```

## Device Profiles

The framework includes realistic manufacturer profiles:

### Protective Relays
- **Schweitzer SEL-351** - Overcurrent and distance protection
- **ABB RED670** - Line differential protection  
- **GE D60** - Multifunction line protection
- **Siemens 7SA522** - Distance and overcurrent protection

### Power Meters
- **Cooper Form 6** - Revenue metering with power quality
- **Schneider PM8000** - Advanced power monitoring

### Digital Fault Recorders
- **Qualitrol DFR-1800** - High-speed fault recording

### PMUs
- **Arbiter 1133A** - IEEE C37.118 compliant PMU

## Configuration

### JSON Configuration File

```json
{
  "substations": [
    {
      "name": "NORTH_SUBSTATION",
      "devices": [
        {
          "device_id": "RELAY_LINE_1",
          "profile": "schweitzer_sel351",
          "config": {
            "nominal_voltage": 138000.0,
            "nominal_current": 1200.0,
            "sampling_rate": 960
          }
        }
      ]
    }
  ],
  "scenarios": [
    {
      "name": "transmission_fault",
      "event_type": "line_fault",
      "severity": 0.9,
      "duration_range": [0.1, 1.5],
      "affected_devices": ["relay_*"],
      "probability": 0.2
    }
  ]
}
```

### Loading Configuration

```python
coordinator = SimulationCoordinator("config.json")
await coordinator.start_simulation()
```

## File Generation

### COMTRADE Files
Automatically generated for fault events on DFRs and relays:
- `.cfg` - Configuration file with channel definitions
- `.dat` - Waveform data in ASCII format  
- `.hdr` - Header file with event information

### PQDIF Files
Generated for power quality events on meters:
- Binary format compliant with IEEE 1159.3
- Contains measurement data and event classification

### File Handling

```python
from simulated_devices import FileCoordinator

# Create file coordinator
file_coord = FileCoordinator("output_directory")

# Add file notification handler
async def handle_new_file(file_info):
    print(f"New {file_info['type']} file: {file_info['filename']}")

file_coord.add_file_handler(handle_new_file)
```

## Wire-Level Protocol Implementation

### Real Protocol Servers

The framework includes **production-ready, wire-level protocol implementations** that work with real clients:

```python
from simulated_devices import (
    WireLevelDNP3Simulator, WireLevelModbusSimulator,
    WireLevelIEC61850Simulator, WireProtocolCoordinator
)

# Create wire-level protocol coordinator
coordinator = WireProtocolCoordinator("RELAY_01")

# Enable protocols with specific ports
coordinator.enable_protocol(ProtocolType.DNP3, port=20000)
coordinator.enable_protocol(ProtocolType.MODBUS, port=502)
coordinator.enable_protocol(ProtocolType.IEC61850, port=102)

# Start all protocol servers
await coordinator.start_all_servers()

# Update measurements across all protocols
coordinator.update_measurement('voltage_a', 138000.0)
coordinator.update_status('breaker_status', True)
```

### DNP3 Wire-Level Implementation

**Complete DNP3 outstation with proper protocol encoding:**

- ✅ **Frame Structure**: Proper start bytes (0x05, 0x64), length, control, addresses
- ✅ **CRC Calculation**: Correct DNP3 CRC-16 for headers and data blocks
- ✅ **Function Codes**: All standard function codes (READ, WRITE, SELECT, OPERATE, etc.)
- ✅ **Data Types**: Analog inputs, binary inputs, counters, control outputs
- ✅ **Unsolicited Responses**: Event-driven data reporting
- ✅ **Quality Flags**: Proper quality indication for all data points
- ✅ **Sequence Numbers**: Correct application layer sequence handling

```python
# DNP3 server with real protocol implementation
dnp3_sim = WireLevelDNP3Simulator("DEVICE_01", outstation_address=1)
await dnp3_sim.start_server(port=20000)

# Update data points
dnp3_sim.update_analog_input(0, 120.5, quality=0x01)  # Good quality
dnp3_sim.update_binary_input(1, True, quality=0x01)

# Send unsolicited response
await dnp3_sim.send_unsolicited_response()
```

### Modbus Wire-Level Implementation

**Complete Modbus TCP server with proper PDU encoding:**

- ✅ **MBAP Header**: Transaction ID, protocol ID, length, unit ID
- ✅ **Function Codes**: All standard functions (01-06, 15-16, 23)
- ✅ **Data Model**: Coils, discrete inputs, holding registers, input registers
- ✅ **Exception Responses**: Proper error handling and exception codes
- ✅ **Multi-Client Support**: Concurrent client connections
- ✅ **Register Mapping**: Standard Modbus addressing (0x, 1x, 3x, 4x)

```python
# Modbus TCP server with real protocol implementation
modbus_sim = WireLevelModbusSimulator("DEVICE_01", slave_address=1)
await modbus_sim.start_server(port=502)

# Update registers
modbus_sim.update_holding_register(0, 1200)  # Voltage register
modbus_sim.update_input_register(10, 500)    # Current register
modbus_sim.update_coil(0, True)              # Control output
```

### IEC 61850 Wire-Level Implementation

**Complete IEC 61850 server with MMS protocol and GOOSE:**

- ✅ **MMS Protocol Stack**: Manufacturing Message Specification implementation
- ✅ **Logical Device Model**: Proper IEC 61850 data modeling (LD/LN/DO/DA)
- ✅ **Data Attributes**: All common data classes with functional constraints
- ✅ **GOOSE Publishing**: Multicast GOOSE message publishing
- ✅ **MMS Services**: Read, write, dataset creation, report control
- ✅ **Quality & Timestamps**: Proper data quality and time synchronization

```python
# IEC 61850 server with MMS and GOOSE
iec61850_sim = WireLevelIEC61850Simulator("DEVICE_01", ied_name="TESTIED")
await iec61850_sim.start_server(port=102)

# Update data attributes
iec61850_sim.update_data_attribute("CTRL", "MMXU1", "TotW", None, 1500.0)
iec61850_sim.update_data_attribute("CTRL", "XCBR1", "Pos", None, True)

# Publish GOOSE message
await iec61850_sim.publish_goose_message(
    "CTRL/LLN0$GO$gcb01", "CTRL/LLN0$dataset01", ["trip_event", time.time()]
)
```

## Protocol Communication Testing

### Enhanced Protocol Simulation

The framework also includes comprehensive protocol communication testing:

```python
from simulated_devices import (
    ProtocolMessageLogger, CommunicationErrorSimulator,
    ProtocolLatencySimulator, SecurityTestingFramework,
    LoadTestingFramework
)

# Setup protocol testing
message_logger = ProtocolMessageLogger("protocol_logs")
error_simulator = CommunicationErrorSimulator(error_rate=0.05)
latency_simulator = ProtocolLatencySimulator()
security_framework = SecurityTestingFramework()
load_tester = LoadTestingFramework()

# Configure network conditions
latency_simulator.set_network_conditions(
    base_latency_ms=20.0,
    jitter_ms=5.0,
    congestion=0.1
)

# Add error patterns
from simulated_devices.protocol_communication import CommunicationError
error_simulator.add_error_pattern(
    CommunicationError.TIMEOUT, probability=0.1, duration_seconds=5.0
)

# Run load testing
load_results = await load_tester.run_concurrent_session_test(
    protocol_type="DNP3",
    host="localhost",
    port=20000,
    concurrent_sessions=10,
    duration_seconds=30
)
```

### File Transfer Protocol Testing

```python
from simulated_devices import FileTransferCoordinator

# Setup file transfer servers
file_coordinator = FileTransferCoordinator("test_files")
await file_coordinator.start_all_servers()

# Run comprehensive tests
test_results = await file_coordinator.run_comprehensive_tests()

# Get server statistics
stats = file_coordinator.get_all_statistics()
```

## Integration Examples

### Triangle MicroWorks Integration

```python
from simulated_devices import TMWIntegrationCoordinator, TMWConfig, DeviceConnection

# Configure Triangle MicroWorks connection
tmw_config = TMWConfig(
    base_url="http://localhost",
    config_port=58090,
    runtime_port=58080,
    redundancy_port=58070,
    username="admin",
    password="password"
)

# Create integration coordinator
tmw_integration = TMWIntegrationCoordinator(tmw_config)
await tmw_integration.start_integration()

# Integrate simulated device
protocol_mapping = {
    "protocol": "DNP3",
    "connection_string": "tcp://localhost:20000",
    "polling_interval": 1000
}

success = await tmw_integration.integrate_simulated_device(
    device_simulator, protocol_mapping
)

# Get integration statistics
stats = tmw_integration.get_integration_statistics()
```

### STTP Publishing

```python
class STTPPublisher:
    async def publish_measurement(self, measurement):
        # Publish to IEEE P2664 STTP stream
        await self.sttp_server.publish(measurement)

sttp = STTPPublisher()
coordinator.add_measurement_handler(sttp.publish_measurement)
```

## Performance Monitoring

```python
# Get simulation statistics
status = coordinator.get_simulation_status()
print(f"Devices online: {status['devices']['online']}")
print(f"Total measurements: {status['performance']['total_measurements']}")
print(f"Events generated: {status['performance']['total_events']}")

# Get device health
device_list = coordinator.get_device_list()
for device in device_list:
    print(f"{device['device_id']}: Health={device['health']:.1f}%")
```

## Testing Integration

The framework is designed for comprehensive testing of:

1. **Triangle MicroWorks SCADA Gateway Integration**
   - REST API communication testing
   - Authentication and authorization flows
   - Real-time data streaming validation

2. **STTP Server Implementation**
   - IEEE P2664 compliance verification
   - Publisher/subscriber functionality
   - Data quality and timing validation

3. **File Processing Systems**
   - COMTRADE and PQDIF parsing accuracy
   - File transfer and storage mechanisms

## Examples

The framework includes comprehensive examples demonstrating different use cases:

### `examples/unified_protocol_server_demo.py` (Recommended)
Complete demonstration of production-ready protocol servers:
- Multiple device types (relay, meter, PMU)
- Wire-level protocol implementations
- Realistic data simulation
- Load testing capabilities
- Client connection examples

### `examples/power_system_simulation_demo.py`
Power system simulation framework demonstration:
- Device factory usage
- Event generation and coordination
- File generation (COMTRADE, PQDIF)
- Multi-device system simulation

### `examples/protocol_testing_demo.py`
Comprehensive protocol testing capabilities:
- Error simulation and recovery testing
- Security testing with authentication failures
- Load testing with concurrent sessions
- Protocol timing and latency simulation
- Triangle MicroWorks integration testing

### `examples/substation_config.json`
Example configuration file showing:
- Multi-substation setup
- Device profiles and configurations
- Event scenarios and timing
- Protocol assignments

## Getting Started

1. **For Protocol Server Use** (Most Common):
   ```bash
   python examples/unified_protocol_server_demo.py
   ```

2. **For Power System Simulation**:
   ```bash
   python examples/power_system_simulation_demo.py
   ```

3. **For Protocol Testing**:
   ```bash
   python examples/protocol_testing_demo.py
   ```
   - Event correlation and analysis

4. **System Performance**
   - High-frequency data handling
   - Concurrent device simulation
   - Memory and CPU utilization

## Requirements

- Python 3.11+
- asyncio for concurrent operations
- numpy for waveform generation
- logging for comprehensive monitoring

## Installation

```bash
# Install in development mode
pip install -e .

# Or install required dependencies
pip install numpy asyncio-mqtt
```

## Examples

See the `examples/` directory for:
- `power_system_simulation_demo.py` - Complete demonstration
- `substation_config.json` - Configuration example
- Integration examples with Triangle MicroWorks and STTP

## License

Part of the OpenMIC Python Container project for comprehensive power system monitoring and data collection.
