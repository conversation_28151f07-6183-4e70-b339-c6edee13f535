{"data_mtime": 1757982705, "dep_lines": [15, 6, 7, 8, 9, 10, 11, 12, 13, 14, 18, 19, 20, 21, 22, 23, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 16, 17], "dep_prios": [10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 10, 10, 10, 10, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10, 10], "dependencies": ["aiohttp.web", "asyncio", "logging", "os", "shutil", "tempfile", "datetime", "pathlib", "typing", "aiohttp", "socket", "threading", "time", "ssl", "ftplib", "<PERSON><PERSON><PERSON>", "builtins", "_frozen_importlib", "_socket", "_ssl", "abc", "aiohttp.abc", "aiohttp.typedefs", "aiohttp.web_app", "aiohttp.web_runner", "aiohttp.web_urldispatcher", "asyncio.events", "enum", "typing_extensions"], "hash": "99d74864cc44882500687ffc41308149fae64b3c", "id": "simulated_devices.file_transfer_simulation", "ignore_all": true, "interface_hash": "bab2b4d438a35b5ed6811280f94e2436fbfdd732", "mtime": 1757982406, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\Quanta\\simulated_devices\\file_transfer_simulation.py", "plugin_data": null, "size": 23192, "suppressed": ["aiofiles", "<PERSON><PERSON><PERSON>"], "version_id": "1.15.0"}