#!/usr/bin/env python3
"""
Unified Protocol Server Demo
Demonstrates the consolidated protocol server approach:
- Single interface for multiple protocols
- Wire-level implementations with testing capabilities
- Easy configuration and management
- Production-ready protocol servers
"""

import asyncio
import logging
import sys
import json
from pathlib import Path
from datetime import datetime

# Add the parent directory to the path
sys.path.append(str(Path(__file__).parent.parent))

from simulated_devices.unified_protocol_server import (
    ProtocolServerFactory, 
    ProtocolType
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('unified_protocol_server_demo.log'),
        logging.StreamHandler()
    ]
)

class UnifiedProtocolServerDemo:
    """Demonstration of unified protocol server capabilities"""
    
    def __init__(self):
        self.logger = logging.getLogger("UnifiedProtocolServerDemo")
        self.servers = {}
        
    async def create_example_servers(self):
        """Create example servers for different device types"""
        self.logger.info("Creating example protocol servers...")
        
        # Create a protective relay server
        self.servers['RELAY_01'] = ProtocolServerFactory.create_relay_server(
            "RELAY_01", base_port=20001
        )
        
        # Create a power meter server
        self.servers['METER_01'] = ProtocolServerFactory.create_meter_server(
            "METER_01", base_port=20010
        )
        
        # Create a PMU server
        self.servers['PMU_01'] = ProtocolServerFactory.create_pmu_server(
            "PMU_01", base_port=20020
        )
        
        # Create a multi-protocol server
        self.servers['MULTI_01'] = ProtocolServerFactory.create_multi_protocol_server(
            "MULTI_01", base_port=20030
        )
        
        # Create server from configuration
        config_dict = {
            'protocols': {
                'dnp3': {
                    'port': 20040,
                    'enabled': True,
                    'outstation_address': 5,
                    'enable_error_simulation': True,
                    'error_rate': 0.02
                },
                'modbus': {
                    'port': 20041,
                    'enabled': True,
                    'slave_address': 5,
                    'enable_latency_simulation': True,
                    'base_latency_ms': 15.0
                }
            }
        }
        
        self.servers['CONFIG_01'] = ProtocolServerFactory.create_from_config(
            "CONFIG_01", config_dict
        )
        
        self.logger.info(f"Created {len(self.servers)} protocol servers")
        
    async def start_all_servers(self):
        """Start all protocol servers"""
        self.logger.info("Starting all protocol servers...")
        
        for server_id, server in self.servers.items():
            try:
                await server.start_all_servers()
                self.logger.info(f"Started server: {server_id}")
            except Exception as e:
                self.logger.error(f"Failed to start server {server_id}: {e}")
                
        self.logger.info("All servers started")
        
    async def stop_all_servers(self):
        """Stop all protocol servers"""
        self.logger.info("Stopping all protocol servers...")
        
        for server_id, server in self.servers.items():
            try:
                await server.stop_all_servers()
                self.logger.info(f"Stopped server: {server_id}")
            except Exception as e:
                self.logger.error(f"Error stopping server {server_id}: {e}")
                
        self.logger.info("All servers stopped")
        
    def display_server_information(self):
        """Display comprehensive server information"""
        print("\n" + "="*80)
        print("UNIFIED PROTOCOL SERVERS")
        print("="*80)
        
        for server_id, server in self.servers.items():
            connection_info = server.get_connection_info()
            stats = server.get_server_statistics()
            
            print(f"\n{server_id}:")
            print(f"  Device ID: {connection_info['device_id']}")
            print(f"  Status: {'Running' if stats['running'] else 'Stopped'}")
            print(f"  Enabled Protocols: {', '.join(stats['enabled_protocols'])}")
            print(f"  Measurements: {stats['measurement_count']}")
            print(f"  Status Points: {stats['status_count']}")
            
            # Protocol-specific information
            for protocol_name, protocol_info in connection_info['protocols'].items():
                print(f"\n  {protocol_name.upper()}:")
                print(f"    Host: {protocol_info['host']}")
                print(f"    Port: {protocol_info['port']}")
                print(f"    Status: {protocol_info['status']}")
                
                if protocol_name == 'dnp3':
                    print(f"    Outstation Address: {protocol_info['outstation_address']}")
                    print(f"    Master Address: {protocol_info['master_address']}")
                elif protocol_name == 'modbus':
                    print(f"    Slave Address: {protocol_info['slave_address']}")
                elif protocol_name == 'iec61850':
                    print(f"    IED Name: {protocol_info['ied_name']}")
                    
                print(f"    Client Example: {protocol_info['example_client']}")
                
        print("\n" + "="*80)
        print("CLIENT CONNECTION SUMMARY")
        print("="*80)
        
        # Group by protocol type
        protocol_connections = {'dnp3': [], 'modbus': [], 'iec61850': []}
        
        for server_id, server in self.servers.items():
            connection_info = server.get_connection_info()
            for protocol_name, protocol_info in connection_info['protocols'].items():
                protocol_connections[protocol_name].append({
                    'server_id': server_id,
                    'port': protocol_info['port'],
                    'address': protocol_info.get('outstation_address') or 
                              protocol_info.get('slave_address') or 
                              protocol_info.get('ied_name', 'N/A')
                })
                
        for protocol_name, connections in protocol_connections.items():
            if connections:
                print(f"\n{protocol_name.upper()} Servers:")
                for conn in connections:
                    print(f"  {conn['server_id']}: localhost:{conn['port']} (Address: {conn['address']})")
                    
        print("="*80)
        
    async def simulate_realistic_data(self):
        """Simulate realistic power system data"""
        self.logger.info("Starting realistic data simulation...")
        
        import random
        import math
        
        # Base values for different device types
        device_base_values = {
            'RELAY_01': {
                'voltage_a': 138000.0, 'voltage_b': 138000.0, 'voltage_c': 138000.0,
                'current_a': 150.0, 'current_b': 150.0, 'current_c': 150.0,
                'power_total': 3500.0, 'frequency': 60.0
            },
            'METER_01': {
                'voltage_a': 138000.0, 'voltage_b': 138000.0, 'voltage_c': 138000.0,
                'current_a': 100.0, 'current_b': 100.0, 'current_c': 100.0,
                'power_total': 24000.0, 'frequency': 60.0
            },
            'PMU_01': {
                'voltage_a': 138000.0, 'voltage_b': 138000.0, 'voltage_c': 138000.0,
                'current_a': 100.0, 'current_b': 100.0, 'current_c': 100.0,
                'power_total': 2400.0, 'frequency': 60.000
            },
            'MULTI_01': {
                'voltage_a': 69000.0, 'voltage_b': 69000.0, 'voltage_c': 69000.0,
                'current_a': 200.0, 'current_b': 200.0, 'current_c': 200.0,
                'power_total': 2400.0, 'frequency': 60.0
            },
            'CONFIG_01': {
                'voltage_a': 13800.0, 'voltage_b': 13800.0, 'voltage_c': 13800.0,
                'current_a': 300.0, 'current_b': 300.0, 'current_c': 300.0,
                'power_total': 7200.0, 'frequency': 60.0
            }
        }
        
        simulation_time = 0
        
        try:
            while True:
                for server_id, server in self.servers.items():
                    if server_id not in device_base_values:
                        continue
                        
                    base_values = device_base_values[server_id]
                    
                    # Add realistic variations
                    for measurement_name, base_value in base_values.items():
                        if measurement_name == 'frequency':
                            # Frequency varies slightly with load changes
                            variation = 0.05 * math.sin(simulation_time * 0.1) + random.uniform(-0.02, 0.02)
                            new_value = base_value + variation
                        elif 'voltage' in measurement_name:
                            # Voltage varies with load and has some noise
                            load_variation = 0.02 * math.sin(simulation_time * 0.05)
                            noise = random.uniform(-0.005, 0.005)
                            new_value = base_value * (1 + load_variation + noise)
                        elif 'current' in measurement_name:
                            # Current varies more significantly with load
                            load_variation = 0.15 * math.sin(simulation_time * 0.03) + 0.1 * math.sin(simulation_time * 0.07)
                            noise = random.uniform(-0.02, 0.02)
                            new_value = base_value * (1 + load_variation + noise)
                        else:  # power
                            # Power follows current variations
                            load_variation = 0.15 * math.sin(simulation_time * 0.03) + 0.1 * math.sin(simulation_time * 0.07)
                            noise = random.uniform(-0.03, 0.03)
                            new_value = base_value * (1 + load_variation + noise)
                            
                        # Ensure positive values
                        new_value = max(new_value, 0.0)
                        
                        # Update measurement
                        server.update_measurement(measurement_name, new_value)
                        
                    # Update status occasionally
                    if simulation_time % 30 == 0:  # Every 30 seconds
                        # Simulate occasional status changes
                        if random.random() < 0.1:  # 10% chance
                            status_name = random.choice(['breaker_status', 'alarm_active', 'maintenance_mode'])
                            if status_name == 'breaker_status':
                                new_status = random.choice([True, True, True, False])  # Usually closed
                            else:
                                new_status = random.choice([True, False])
                            server.update_status(status_name, new_status)
                            
                simulation_time += 1
                await asyncio.sleep(1.0)  # Update every second
                
        except asyncio.CancelledError:
            self.logger.info("Data simulation stopped")
            
    async def run_load_tests(self):
        """Run load tests on all servers"""
        self.logger.info("Running load tests...")
        
        test_results = {}
        
        for server_id, server in self.servers.items():
            server_results = {}
            stats = server.get_server_statistics()
            
            for protocol_name in stats['enabled_protocols']:
                try:
                    protocol_type = ProtocolType(protocol_name)
                    result = await server.run_load_test(
                        protocol_type=protocol_type,
                        concurrent_sessions=5,
                        duration_seconds=30
                    )
                    server_results[protocol_name] = result
                    self.logger.info(f"Load test completed for {server_id} {protocol_name}")
                except Exception as e:
                    self.logger.error(f"Load test failed for {server_id} {protocol_name}: {e}")
                    server_results[protocol_name] = {'error': str(e)}
                    
            test_results[server_id] = server_results
            
        # Save test results
        with open('load_test_results.json', 'w') as f:
            json.dump(test_results, f, indent=2, default=str)
            
        self.logger.info("Load tests completed. Results saved to load_test_results.json")
        return test_results
        
    async def run_demonstration(self):
        """Run the complete demonstration"""
        try:
            # Create and start servers
            await self.create_example_servers()
            await self.start_all_servers()
            
            # Display server information
            self.display_server_information()
            
            print("\nProtocol servers are running. You can now:")
            print("1. Connect protocol clients to test functionality")
            print("2. Press Enter to start data simulation")
            print("3. Press Ctrl+C to exit")
            
            try:
                # Use asyncio-compatible input alternative
                await asyncio.sleep(0.1)  # Allow event loop to process
                print("Starting data simulation automatically...")
            except KeyboardInterrupt:
                print("\nExiting...")
                return
                
            # Start data simulation
            simulation_task = asyncio.create_task(self.simulate_realistic_data())
            
            print("\nData simulation started. Servers will run with realistic data.")
            print("Press Enter to run load tests, or Ctrl+C to exit...")
            
            try:
                # Wait a bit for connections, then run load tests
                await asyncio.sleep(2)

                # Run load tests
                print("\nRunning load tests...")
                await self.run_load_tests()

                print("\nLoad tests completed. Server will continue running...")

                # Keep running
                while True:
                    await asyncio.sleep(10)
                    
                    # Display periodic statistics
                    print(f"\n[{datetime.now().strftime('%H:%M:%S')}] Server Status:")
                    for server_id, server in self.servers.items():
                        stats = server.get_server_statistics()
                        total_connections = 0
                        
                        for protocol_name, protocol_stats in stats['protocols'].items():
                            if protocol_name == 'dnp3':
                                total_connections += protocol_stats.get('connections', 0)
                            elif protocol_name == 'modbus':
                                total_connections += protocol_stats.get('connections', 0)
                            elif protocol_name == 'iec61850':
                                total_connections += protocol_stats.get('mms_connections', 0)
                                
                        print(f"  {server_id}: {total_connections} active connections")
                        
            except KeyboardInterrupt:
                print("\nShutting down...")
                simulation_task.cancel()
                
        finally:
            # Stop all servers
            await self.stop_all_servers()

async def main():
    """Main demonstration function"""
    demo = UnifiedProtocolServerDemo()
    
    try:
        await demo.run_demonstration()
        
    except KeyboardInterrupt:
        print("\nDemo interrupted by user")
    except Exception as e:
        print(f"Demo failed: {e}")
        logging.exception("Demo error")

if __name__ == "__main__":
    print("Unified Protocol Server Demo")
    print("===========================")
    print("This demo creates production-ready protocol servers with:")
    print("- Wire-level DNP3, Modbus, and IEC 61850 implementations")
    print("- Integrated testing capabilities")
    print("- Unified management interface")
    print("- Realistic power system data simulation")
    print()
    
    asyncio.run(main())
