"""
File Transfer Protocol Simulation
Simulates FTP, SFTP, and HTTP file transfer protocols for device testing
"""

import asyncio
import logging
import os
import shutil
import tempfile
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import aiohttp
from aiohttp import web, ClientSession
import aiofiles
import paramiko
import socket
import threading
import time
import ssl
from ftplib import FTP
import hashlib

class FileTransferServer:
    """Base class for file transfer servers"""
    
    def __init__(self, server_type: str, port: int, root_directory: str):
        self.logger = logging.getLogger(f"{server_type}Server")
        self.server_type = server_type
        self.port = port
        self.root_dir = Path(root_directory)
        self.root_dir.mkdir(exist_ok=True, parents=True)
        
        # Server state
        self.running = False
        self.server = None
        self.connections = {}
        
        # Statistics
        self.stats = {
            "total_connections": 0,
            "active_connections": 0,
            "files_uploaded": 0,
            "files_downloaded": 0,
            "bytes_transferred": 0,
            "authentication_attempts": 0,
            "authentication_failures": 0
        }
        
        # Authentication
        self.users = {
            "admin": {"password": "admin123", "permissions": "rwx"},
            "operator": {"password": "oper456", "permissions": "rw"},
            "readonly": {"password": "read789", "permissions": "r"}
        }
        
    async def start_server(self):
        """Start the file transfer server"""
        self.running = True
        self.logger.info(f"Starting {self.server_type} server on port {self.port}")
        
    async def stop_server(self):
        """Stop the file transfer server"""
        self.running = False
        if self.server:
            self.server.close()
            if hasattr(self.server, 'wait_closed'):
                await self.server.wait_closed()
        self.logger.info(f"Stopped {self.server_type} server")
        
    def authenticate_user(self, username: str, password: str) -> Tuple[bool, str]:
        """Authenticate user credentials"""
        self.stats["authentication_attempts"] += 1
        
        if username in self.users:
            if self.users[username]["password"] == password:
                return True, self.users[username]["permissions"]
                
        self.stats["authentication_failures"] += 1
        return False, ""
        
    def get_statistics(self) -> Dict[str, Any]:
        """Get server statistics"""
        return {
            **self.stats,
            "server_type": self.server_type,
            "port": self.port,
            "running": self.running,
            "root_directory": str(self.root_dir)
        }

class HTTPFileServer(FileTransferServer):
    """HTTP file transfer server simulation"""
    
    def __init__(self, port: int = 8080, root_directory: str = "http_files"):
        super().__init__("HTTP", port, root_directory)
        self.app = web.Application()
        self.setup_routes()
        
    def setup_routes(self):
        """Setup HTTP routes"""
        self.app.router.add_get('/', self.handle_index)
        self.app.router.add_get('/files', self.handle_file_list)
        self.app.router.add_get('/files/{filename}', self.handle_file_download)
        self.app.router.add_post('/files/{filename}', self.handle_file_upload)
        self.app.router.add_delete('/files/{filename}', self.handle_file_delete)
        self.app.router.add_get('/status', self.handle_status)
        
    async def handle_index(self, request):
        """Handle index page"""
        return web.Response(text=f"OpenMIC {self.server_type} File Server\nPort: {self.port}")
        
    async def handle_file_list(self, request):
        """Handle file listing"""
        try:
            files = []
            for file_path in self.root_dir.iterdir():
                if file_path.is_file():
                    stat = file_path.stat()
                    files.append({
                        "name": file_path.name,
                        "size": stat.st_size,
                        "modified": datetime.fromtimestamp(stat.st_mtime, timezone.utc).isoformat()
                    })
                    
            return web.json_response({"files": files})
            
        except Exception as e:
            self.logger.error(f"File list error: {e}")
            return web.json_response({"error": str(e)}, status=500)
            
    async def handle_file_download(self, request):
        """Handle file download"""
        filename = request.match_info['filename']
        file_path = self.root_dir / filename
        
        if not file_path.exists():
            return web.json_response({"error": "File not found"}, status=404)
            
        try:
            self.stats["files_downloaded"] += 1
            self.stats["bytes_transferred"] += file_path.stat().st_size
            
            return web.FileResponse(file_path)
            
        except Exception as e:
            self.logger.error(f"File download error: {e}")
            return web.json_response({"error": str(e)}, status=500)
            
    async def handle_file_upload(self, request):
        """Handle file upload"""
        filename = request.match_info['filename']
        file_path = self.root_dir / filename
        
        try:
            # Read uploaded data
            data = await request.read()
            
            # Write file
            async with aiofiles.open(file_path, 'wb') as f:
                await f.write(data)
                
            self.stats["files_uploaded"] += 1
            self.stats["bytes_transferred"] += len(data)
            
            return web.json_response({
                "message": "File uploaded successfully",
                "filename": filename,
                "size": len(data)
            })
            
        except Exception as e:
            self.logger.error(f"File upload error: {e}")
            return web.json_response({"error": str(e)}, status=500)
            
    async def handle_file_delete(self, request):
        """Handle file deletion"""
        filename = request.match_info['filename']
        file_path = self.root_dir / filename
        
        if not file_path.exists():
            return web.json_response({"error": "File not found"}, status=404)
            
        try:
            file_path.unlink()
            return web.json_response({"message": "File deleted successfully"})
            
        except Exception as e:
            self.logger.error(f"File delete error: {e}")
            return web.json_response({"error": str(e)}, status=500)
            
    async def handle_status(self, request):
        """Handle status request"""
        return web.json_response(self.get_statistics())
        
    async def start_server(self):
        """Start HTTP server"""
        await super().start_server()
        
        runner = web.AppRunner(self.app)
        await runner.setup()
        
        site = web.TCPSite(runner, 'localhost', self.port)
        await site.start()
        
        self.server = runner

class FTPServer(FileTransferServer):
    """FTP server simulation"""
    
    def __init__(self, port: int = 21, root_directory: str = "ftp_files"):
        super().__init__("FTP", port, root_directory)
        self.server_socket = None
        self.server_thread = None
        
    async def start_server(self):
        """Start FTP server"""
        await super().start_server()
        
        # Start FTP server in separate thread
        self.server_thread = threading.Thread(target=self._run_ftp_server)
        self.server_thread.daemon = True
        self.server_thread.start()
        
    def _run_ftp_server(self):
        """Run FTP server in thread"""
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind(('localhost', self.port))
            self.server_socket.listen(5)
            
            self.logger.info(f"FTP server listening on port {self.port}")
            
            while self.running:
                try:
                    client_socket, address = self.server_socket.accept()
                    self.stats["total_connections"] += 1
                    self.stats["active_connections"] += 1
                    
                    # Handle client in separate thread
                    client_thread = threading.Thread(
                        target=self._handle_ftp_client,
                        args=(client_socket, address)
                    )
                    client_thread.daemon = True
                    client_thread.start()
                    
                except socket.error:
                    if self.running:
                        self.logger.error("FTP server socket error")
                    break
                    
        except Exception as e:
            self.logger.error(f"FTP server error: {e}")
            
    def _handle_ftp_client(self, client_socket, address):
        """Handle FTP client connection"""
        try:
            client_socket.send(b"220 OpenMIC FTP Server Ready\r\n")
            
            authenticated = False
            username = ""
            current_dir = self.root_dir
            
            while self.running:
                try:
                    data = client_socket.recv(1024).decode('utf-8').strip()
                    if not data:
                        break
                        
                    command_parts = data.split(' ', 1)
                    command = command_parts[0].upper()
                    args = command_parts[1] if len(command_parts) > 1 else ""
                    
                    if command == "USER":
                        username = args
                        client_socket.send(b"331 Password required\r\n")
                        
                    elif command == "PASS":
                        success, permissions = self.authenticate_user(username, args)
                        if success:
                            authenticated = True
                            client_socket.send(b"230 Login successful\r\n")
                        else:
                            client_socket.send(b"530 Login incorrect\r\n")
                            
                    elif command == "PWD":
                        if authenticated:
                            client_socket.send(f"257 \"{current_dir}\" is current directory\r\n".encode())
                        else:
                            client_socket.send(b"530 Not logged in\r\n")
                            
                    elif command == "LIST":
                        if authenticated:
                            self._send_file_list(client_socket, current_dir)
                        else:
                            client_socket.send(b"530 Not logged in\r\n")
                            
                    elif command == "QUIT":
                        client_socket.send(b"221 Goodbye\r\n")
                        break
                        
                    else:
                        client_socket.send(b"502 Command not implemented\r\n")
                        
                except socket.error:
                    break
                    
        except Exception as e:
            self.logger.error(f"FTP client handler error: {e}")
        finally:
            client_socket.close()
            self.stats["active_connections"] -= 1
            
    def _send_file_list(self, client_socket, directory):
        """Send file listing to FTP client"""
        try:
            file_list = []
            for file_path in directory.iterdir():
                if file_path.is_file():
                    stat = file_path.stat()
                    file_list.append(f"-rw-r--r-- 1 <USER> <GROUP> {stat.st_size} {file_path.name}")
                    
            listing = "\r\n".join(file_list) + "\r\n"
            client_socket.send(f"150 Opening data connection\r\n".encode())
            client_socket.send(listing.encode())
            client_socket.send(b"226 Transfer complete\r\n")
            
        except Exception as e:
            self.logger.error(f"FTP file list error: {e}")
            client_socket.send(b"550 Failed to list directory\r\n")

class SFTPServer(FileTransferServer):
    """SFTP server simulation using paramiko"""
    
    def __init__(self, port: int = 22, root_directory: str = "sftp_files"):
        super().__init__("SFTP", port, root_directory)
        self.host_key = None
        self.server_socket = None
        
    async def start_server(self):
        """Start SFTP server"""
        await super().start_server()
        
        # Generate host key
        self.host_key = paramiko.RSAKey.generate(2048)
        
        # Start server in thread
        self.server_thread = threading.Thread(target=self._run_sftp_server)
        self.server_thread.daemon = True
        self.server_thread.start()
        
    def _run_sftp_server(self):
        """Run SFTP server in thread"""
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind(('localhost', self.port))
            self.server_socket.listen(5)
            
            self.logger.info(f"SFTP server listening on port {self.port}")
            
            while self.running:
                try:
                    client_socket, address = self.server_socket.accept()
                    self.stats["total_connections"] += 1
                    self.stats["active_connections"] += 1
                    
                    # Handle SSH/SFTP client
                    transport = paramiko.Transport(client_socket)
                    transport.add_server_key(self.host_key)
                    
                    server = SFTPServerInterface(self)
                    transport.set_subsystem_handler('sftp', paramiko.SFTPServer, sftp_si=server)
                    
                    transport.start_server(server=server)
                    
                except Exception as e:
                    self.logger.error(f"SFTP client connection error: {e}")
                    self.stats["active_connections"] -= 1
                    
        except Exception as e:
            self.logger.error(f"SFTP server error: {e}")

class SFTPServerInterface(paramiko.ServerInterface):
    """SFTP server interface implementation"""
    
    def __init__(self, file_server):
        self.file_server = file_server
        
    def check_auth_password(self, username, password):
        """Check password authentication"""
        success, _ = self.file_server.authenticate_user(username, password)
        return paramiko.AUTH_SUCCESSFUL if success else paramiko.AUTH_FAILED
        
    def check_channel_request(self, kind, chanid):
        """Check channel request"""
        return paramiko.OPEN_SUCCEEDED

class FileTransferClient:
    """File transfer client for testing servers"""
    
    def __init__(self):
        self.logger = logging.getLogger("FileTransferClient")
        
    async def test_http_server(self, host: str, port: int) -> Dict[str, Any]:
        """Test HTTP file server"""
        results = {
            "protocol": "HTTP",
            "host": host,
            "port": port,
            "tests": []
        }
        
        base_url = f"http://{host}:{port}"
        
        async with ClientSession() as session:
            # Test file list
            try:
                async with session.get(f"{base_url}/files") as response:
                    if response.status == 200:
                        files = await response.json()
                        results["tests"].append({
                            "test": "file_list",
                            "success": True,
                            "files_count": len(files.get("files", []))
                        })
                    else:
                        results["tests"].append({
                            "test": "file_list",
                            "success": False,
                            "error": f"HTTP {response.status}"
                        })
            except Exception as e:
                results["tests"].append({
                    "test": "file_list",
                    "success": False,
                    "error": str(e)
                })
                
            # Test file upload
            test_data = b"Test file content for HTTP upload"
            try:
                async with session.post(f"{base_url}/files/test_upload.txt", data=test_data) as response:
                    if response.status == 200:
                        results["tests"].append({
                            "test": "file_upload",
                            "success": True,
                            "bytes_uploaded": len(test_data)
                        })
                    else:
                        results["tests"].append({
                            "test": "file_upload",
                            "success": False,
                            "error": f"HTTP {response.status}"
                        })
            except Exception as e:
                results["tests"].append({
                    "test": "file_upload",
                    "success": False,
                    "error": str(e)
                })
                
            # Test file download
            try:
                async with session.get(f"{base_url}/files/test_upload.txt") as response:
                    if response.status == 200:
                        downloaded_data = await response.read()
                        results["tests"].append({
                            "test": "file_download",
                            "success": True,
                            "bytes_downloaded": len(downloaded_data),
                            "data_matches": downloaded_data == test_data
                        })
                    else:
                        results["tests"].append({
                            "test": "file_download",
                            "success": False,
                            "error": f"HTTP {response.status}"
                        })
            except Exception as e:
                results["tests"].append({
                    "test": "file_download",
                    "success": False,
                    "error": str(e)
                })
                
        return results
        
    def test_ftp_server(self, host: str, port: int, username: str = "admin", password: str = "admin123") -> Dict[str, Any]:
        """Test FTP server"""
        results = {
            "protocol": "FTP",
            "host": host,
            "port": port,
            "tests": []
        }
        
        try:
            ftp = FTP()
            ftp.connect(host, port)
            
            # Test login
            try:
                ftp.login(username, password)
                results["tests"].append({
                    "test": "authentication",
                    "success": True
                })
            except Exception as e:
                results["tests"].append({
                    "test": "authentication",
                    "success": False,
                    "error": str(e)
                })
                return results
                
            # Test directory listing
            try:
                file_list = ftp.nlst()
                results["tests"].append({
                    "test": "directory_listing",
                    "success": True,
                    "files_count": len(file_list)
                })
            except Exception as e:
                results["tests"].append({
                    "test": "directory_listing",
                    "success": False,
                    "error": str(e)
                })
                
            ftp.quit()
            
        except Exception as e:
            results["tests"].append({
                "test": "connection",
                "success": False,
                "error": str(e)
            })
            
        return results

class FileTransferCoordinator:
    """Coordinates multiple file transfer servers for testing"""
    
    def __init__(self, base_directory: str = "file_transfer_test"):
        self.logger = logging.getLogger("FileTransferCoordinator")
        self.base_dir = Path(base_directory)
        self.base_dir.mkdir(exist_ok=True)
        
        # Initialize servers
        self.servers = {
            "http": HTTPFileServer(8080, str(self.base_dir / "http")),
            "ftp": FTPServer(2121, str(self.base_dir / "ftp")),
            "sftp": SFTPServer(2222, str(self.base_dir / "sftp"))
        }
        
        # Test client
        self.client = FileTransferClient()
        
    async def start_all_servers(self):
        """Start all file transfer servers"""
        for name, server in self.servers.items():
            try:
                await server.start_server()
                self.logger.info(f"Started {name.upper()} server")
            except Exception as e:
                self.logger.error(f"Failed to start {name.upper()} server: {e}")
                
    async def stop_all_servers(self):
        """Stop all file transfer servers"""
        for name, server in self.servers.items():
            try:
                await server.stop_server()
                self.logger.info(f"Stopped {name.upper()} server")
            except Exception as e:
                self.logger.error(f"Failed to stop {name.upper()} server: {e}")
                
    async def run_comprehensive_tests(self) -> Dict[str, Any]:
        """Run comprehensive tests on all servers"""
        test_results = {
            "start_time": datetime.now(timezone.utc).isoformat(),
            "servers": {}
        }
        
        # Test HTTP server
        try:
            http_results = await self.client.test_http_server("localhost", 8080)
            test_results["servers"]["http"] = http_results
        except Exception as e:
            test_results["servers"]["http"] = {"error": str(e)}
            
        # Test FTP server
        try:
            ftp_results = self.client.test_ftp_server("localhost", 2121)
            test_results["servers"]["ftp"] = ftp_results
        except Exception as e:
            test_results["servers"]["ftp"] = {"error": str(e)}
            
        test_results["end_time"] = datetime.now(timezone.utc).isoformat()
        
        return test_results
        
    def get_all_statistics(self) -> Dict[str, Any]:
        """Get statistics from all servers"""
        stats = {}
        for name, server in self.servers.items():
            stats[name] = server.get_statistics()
        return stats
