#!/usr/bin/env python3
"""
Debug DNP3 Server - Test the corrected CRC implementation
"""

import asyncio
import logging
import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from simulated_devices.wire_protocol_simulators import WireLevelDNP3Simulator

async def main():
    # Set up detailed logging
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Create DNP3 server with debug logging
    dnp3_server = WireLevelDNP3Simulator(
        device_id="DEBUG_DNP3",
        outstation_address=2,
        master_address=100
    )
    
    # Add comprehensive test data for integrity poll
    # Analog Inputs (AI0-AI9) - Power system measurements
    dnp3_server.update_analog_input(0, 138500.0)  # Phase A Voltage (138.5kV)
    dnp3_server.update_analog_input(1, 138200.0)  # Phase B Voltage (138.2kV)
    dnp3_server.update_analog_input(2, 138800.0)  # Phase C Voltage (138.8kV)
    dnp3_server.update_analog_input(3, 1050.0)    # Phase A Current (1050A)
    dnp3_server.update_analog_input(4, 1020.0)    # Phase B Current (1020A)
    dnp3_server.update_analog_input(5, 1080.0)    # Phase C Current (1080A)
    dnp3_server.update_analog_input(6, 145.2)     # Active Power (145.2MW)
    dnp3_server.update_analog_input(7, 32.8)      # Reactive Power (32.8MVAR)
    dnp3_server.update_analog_input(8, 59.98)     # Frequency (59.98Hz)
    dnp3_server.update_analog_input(9, 0.95)      # Power Factor (0.95)

    # Binary Inputs (BI0-BI4) - Status points
    dnp3_server.update_binary_input(0, True)   # Main Breaker Closed
    dnp3_server.update_binary_input(1, False)  # Fault Alarm
    dnp3_server.update_binary_input(2, True)   # System Normal
    dnp3_server.update_binary_input(3, False)  # Maintenance Mode
    dnp3_server.update_binary_input(4, True)   # Communications OK
    
    print("🔧 Starting DEBUG DNP3 Server on port 20011...")
    print("📊 Server will log all incoming requests and outgoing responses")
    print("🎯 External client should connect to 192.168.0.85:20011")
    print("=" * 60)
    
    try:
        # Start the server
        await dnp3_server.start_server(port=20011)
        print("✅ DNP3 Debug Server started successfully!")
        
        # Keep running
        while True:
            await asyncio.sleep(1)
            
    except KeyboardInterrupt:
        print("\n🛑 Shutting down debug server...")
    except Exception as e:
        print(f"❌ Server error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
