# Migration Guide: Protocol Implementation Updates

This guide helps migrate from the old protocol implementations to the new unified protocol server architecture.

## What Changed

### Files Removed
- ❌ `simulated_devices/protocol_simulator.py` - **REMOVED** (replaced by wire-level implementations)
- ❌ `examples/wire_protocol_demo.py` - **REMOVED** (replaced by unified demo)

### Files Added/Enhanced
- ✅ `simulated_devices/unified_protocol_server.py` - **NEW** (recommended primary interface)
- ✅ `simulated_devices/wire_protocol_simulators.py` - **ENHANCED** (real wire-level protocols)
- ✅ `examples/unified_protocol_server_demo.py` - **NEW** (comprehensive demo)

### Files Maintained
- ✅ `simulated_devices/wire_protocol_coordinator.py` - **MAINTAINED** (for complex scenarios)
- ✅ `simulated_devices/protocol_communication.py` - **MAINTAINED** (testing framework)
- ✅ `examples/protocol_testing_demo.py` - **MAINTAINED** (testing focus)

## Migration Steps

### 1. Old Protocol Simulator Usage

**OLD CODE (No longer works):**
```python
from simulated_devices import ProtocolSimulator, ProtocolType, DNP3Simulator

# This will cause ImportError
simulator = ProtocolSimulator("device_id", [ProtocolType.DNP3])
dnp3_sim = DNP3Simulator("device_id")
```

**NEW CODE (Recommended):**
```python
from simulated_devices import ProtocolServerFactory

# Use unified protocol server
server = ProtocolServerFactory.create_relay_server("device_id", base_port=20000)
await server.start_all_servers()

# Update data across all protocols
server.update_measurement('voltage_a', 138000.0)
server.update_status('breaker_status', True)
```

### 2. Wire-Level Protocol Usage

**OLD CODE:**
```python
from simulated_devices import WireProtocolCoordinator

# Still works, but unified server is easier
coordinator = WireProtocolCoordinator("device_id")
coordinator.enable_protocol(ProtocolType.DNP3, port=20000)
await coordinator.start_all_servers()
```

**NEW CODE (Simpler):**
```python
from simulated_devices import ProtocolServerFactory

# Much simpler setup
server = ProtocolServerFactory.create_relay_server("device_id", base_port=20000)
await server.start_all_servers()
```

### 3. Custom Protocol Configuration

**OLD CODE:**
```python
# Manual configuration was complex
coordinator = WireProtocolCoordinator("device_id")
coordinator.enable_protocol(ProtocolType.DNP3, port=20000)
coordinator.enable_protocol(ProtocolType.IEC61850, port=102)
# ... lots of manual setup
```

**NEW CODE:**
```python
from simulated_devices import ProtocolServerConfig, UnifiedProtocolServer, ProtocolType

# Declarative configuration
configs = [
    ProtocolServerConfig(
        protocol_type=ProtocolType.DNP3,
        port=20000,
        dnp3_outstation_address=1,
        enable_error_simulation=True,
        error_rate=0.01
    ),
    ProtocolServerConfig(
        protocol_type=ProtocolType.IEC61850,
        port=102,
        iec61850_ied_name="RELAY_IED",
        enable_latency_simulation=True
    )
]

server = UnifiedProtocolServer("device_id", configs)
await server.start_all_servers()
```

### 4. Factory Methods for Common Cases

**Instead of manual configuration, use factory methods:**

```python
from simulated_devices import ProtocolServerFactory

# Protective Relay (DNP3 + IEC 61850)
relay_server = ProtocolServerFactory.create_relay_server("RELAY_01")

# Power Meter (Modbus + DNP3)
meter_server = ProtocolServerFactory.create_meter_server("METER_01")

# PMU (IEC 61850 only)
pmu_server = ProtocolServerFactory.create_pmu_server("PMU_01")

# All protocols enabled
multi_server = ProtocolServerFactory.create_multi_protocol_server("MULTI_01")

# From JSON configuration
config_dict = {
    'protocols': {
        'dnp3': {'port': 20000, 'outstation_address': 1},
        'modbus': {'port': 502, 'slave_address': 1}
    }
}
config_server = ProtocolServerFactory.create_from_config("CONFIG_01", config_dict)
```

## Benefits of New Architecture

### 1. Simplified Interface
- **Single API** for multiple protocols
- **Factory methods** for common configurations
- **Unified data updates** across all protocols

### 2. Production Ready
- **Real wire-level protocols** that work with actual clients
- **Integrated testing** capabilities
- **Comprehensive monitoring** and statistics

### 3. Better Integration
- **Cross-protocol synchronization** - update once, propagates everywhere
- **Built-in error simulation** and load testing
- **Easy client connection** information

### 4. Maintainable Code
- **Consolidated architecture** - fewer files to manage
- **Clear separation** between simulation and protocol serving
- **Comprehensive examples** and documentation

## Troubleshooting

### ImportError for Old Classes
If you get `ImportError` for old classes:

```python
# This will fail:
from simulated_devices import ProtocolSimulator, DNP3Simulator

# Use this instead:
from simulated_devices import ProtocolServerFactory, UnifiedProtocolServer
```

### Missing Protocol Functionality
If you need specific protocol features:

```python
# Access underlying wire-level simulators if needed
from simulated_devices import WireLevelDNP3Simulator, WireLevelModbusSimulator

# Or use the unified server which wraps them
server = ProtocolServerFactory.create_relay_server("RELAY_01")
# Access underlying simulators through server.protocol_servers if needed
```

### Complex Multi-Device Scenarios
For complex scenarios with many devices:

```python
# WireProtocolCoordinator is still available for complex cases
from simulated_devices import WireProtocolCoordinator

# But consider multiple UnifiedProtocolServers instead:
relay_server = ProtocolServerFactory.create_relay_server("RELAY_01", base_port=20000)
meter_server = ProtocolServerFactory.create_meter_server("METER_01", base_port=20010)
pmu_server = ProtocolServerFactory.create_pmu_server("PMU_01", base_port=20020)
```

## Recommended Migration Path

1. **Start with examples**: Run `examples/unified_protocol_server_demo.py`
2. **Use factory methods**: Replace manual configuration with factory methods
3. **Test with real clients**: Verify wire-level protocol compatibility
4. **Add testing capabilities**: Use built-in error simulation and load testing
5. **Monitor performance**: Use built-in statistics and monitoring

## Support

The new unified architecture provides all the functionality of the old system with significant improvements in usability, performance, and maintainability. The wire-level protocol implementations are fully compatible with real industrial protocol clients and SCADA systems.
