"""
Event Generator - Coordinated power system event simulation
Supports realistic fault scenarios and system-wide event coordination
"""

import asyncio
import random
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
from enum import Enum
from .power_system_simulator import PowerSystemSimulator, EventType

class EventSeverity(Enum):
    LOW = 0.3
    MEDIUM = 0.6
    HIGH = 0.9
    CRITICAL = 1.0

@dataclass
class EventScenario:
    """Defines a power system event scenario"""
    name: str
    description: str
    event_type: EventType
    severity: EventSeverity
    duration_range: tuple  # (min_seconds, max_seconds)
    affected_devices: List[str]  # Device IDs or patterns
    probability: float  # Events per hour
    prerequisites: Optional[List[str]] = None  # Required system conditions
    cascading_events: Optional[List[Dict[str, Any]]] = None  # Follow-up events

class EventGenerator:
    """
    Coordinated power system event generator for realistic fault simulation
    Supports complex scenarios with cascading effects and system-wide coordination
    """
    
    def __init__(self):
        self.logger = logging.getLogger("EventGenerator")
        self.devices: Dict[str, PowerSystemSimulator] = {}
        self.scenarios: Dict[str, EventScenario] = {}
        self.active_events: Dict[str, Dict[str, Any]] = {}
        self.event_history: List[Dict[str, Any]] = []
        self.running = False
        
        # Event statistics
        self.stats = {
            "events_generated": 0,
            "scenarios_executed": 0,
            "devices_affected": 0,
            "total_event_duration": 0.0
        }
        
        # Load default scenarios
        self._load_default_scenarios()
        
    def _load_default_scenarios(self):
        """Load default power system event scenarios"""
        scenarios = {
            "transmission_line_fault": EventScenario(
                name="Transmission Line Fault",
                description="Single-phase or three-phase fault on transmission line",
                event_type=EventType.LINE_FAULT,
                severity=EventSeverity.HIGH,
                duration_range=(0.1, 2.0),
                affected_devices=["relay_*", "dfr_*"],
                probability=0.5,  # 0.5 events per hour
                cascading_events=[
                    {"type": "voltage_sag", "delay": 0.05, "duration": 1.0, "devices": ["meter_*"]},
                    {"type": "frequency_deviation", "delay": 0.1, "duration": 5.0, "devices": ["pmu_*"]}
                ]
            ),
            "transformer_overload": EventScenario(
                name="Transformer Overload",
                description="Transformer thermal overload condition",
                event_type=EventType.TRANSFORMER_TRIP,
                severity=EventSeverity.MEDIUM,
                duration_range=(10.0, 300.0),
                affected_devices=["relay_transformer_*", "meter_transformer_*"],
                probability=0.1,
                prerequisites=["high_load_condition"]
            ),
            "capacitor_bank_switching": EventScenario(
                name="Capacitor Bank Switching",
                description="Capacitor bank energization or de-energization",
                event_type=EventType.CAPACITOR_SWITCH,
                severity=EventSeverity.LOW,
                duration_range=(0.5, 2.0),
                affected_devices=["meter_*", "pmu_*"],
                probability=2.0,  # 2 events per hour
                cascading_events=[
                    {"type": "harmonic_distortion", "delay": 0.1, "duration": 10.0, "devices": ["meter_*"]}
                ]
            ),
            "voltage_regulator_operation": EventScenario(
                name="Voltage Regulator Operation",
                description="Automatic voltage regulator tap change",
                event_type=EventType.VOLTAGE_SAG,
                severity=EventSeverity.LOW,
                duration_range=(1.0, 5.0),
                affected_devices=["meter_*", "relay_*"],
                probability=1.0
            ),
            "generator_trip": EventScenario(
                name="Generator Trip",
                description="Large generator sudden disconnection",
                event_type=EventType.FREQUENCY_DEVIATION,
                severity=EventSeverity.CRITICAL,
                duration_range=(5.0, 60.0),
                affected_devices=["pmu_*", "relay_*", "meter_*"],
                probability=0.05,
                cascading_events=[
                    {"type": "voltage_sag", "delay": 0.2, "duration": 10.0, "devices": ["meter_*"]},
                    {"type": "power_swing", "delay": 1.0, "duration": 30.0, "devices": ["relay_*"]}
                ]
            ),
            "load_rejection": EventScenario(
                name="Load Rejection",
                description="Sudden large load disconnection",
                event_type=EventType.FREQUENCY_DEVIATION,
                severity=EventSeverity.MEDIUM,
                duration_range=(2.0, 30.0),
                affected_devices=["pmu_*", "meter_*"],
                probability=0.2
            ),
            "harmonic_disturbance": EventScenario(
                name="Harmonic Disturbance",
                description="Non-linear load causing harmonic distortion",
                event_type=EventType.HARMONIC_DISTORTION,
                severity=EventSeverity.MEDIUM,
                duration_range=(60.0, 3600.0),
                affected_devices=["meter_*"],
                probability=0.3
            ),
            "power_swing": EventScenario(
                name="Power System Swing",
                description="Power system oscillation following disturbance",
                event_type=EventType.POWER_SWING,
                severity=EventSeverity.HIGH,
                duration_range=(5.0, 120.0),
                affected_devices=["relay_*", "pmu_*"],
                probability=0.1,
                prerequisites=["system_stressed"]
            )
        }
        
        for name, scenario in scenarios.items():
            self.scenarios[name] = scenario
            
        self.logger.info(f"Loaded {len(scenarios)} default event scenarios")
        
    def register_device(self, device: PowerSystemSimulator):
        """Register a device for event coordination"""
        self.devices[device.config.device_id] = device
        self.logger.debug(f"Registered device: {device.config.device_id}")
        
    def register_devices(self, devices: List[PowerSystemSimulator]):
        """Register multiple devices"""
        for device in devices:
            self.register_device(device)
            
    async def start_event_generation(self):
        """Start automatic event generation based on scenarios"""
        if self.running:
            self.logger.warning("Event generation already running")
            return
            
        self.running = True
        self.logger.info("Starting automatic event generation")
        
        # Start event generation tasks
        tasks = [
            asyncio.create_task(self._generate_random_events()),
            asyncio.create_task(self._monitor_cascading_events()),
            asyncio.create_task(self._cleanup_expired_events())
        ]
        
        try:
            await asyncio.gather(*tasks)
        except asyncio.CancelledError:
            self.logger.info("Event generation cancelled")
        except Exception as e:
            self.logger.error(f"Event generation error: {e}")
        finally:
            self.running = False
            
    async def stop_event_generation(self):
        """Stop automatic event generation"""
        self.running = False
        self.logger.info("Stopping event generation")
        
    async def _generate_random_events(self):
        """Generate random events based on scenario probabilities"""
        while self.running:
            try:
                # Check each scenario for event generation
                for scenario_name, scenario in self.scenarios.items():
                    # Calculate probability for this time interval (1 second)
                    probability_per_second = scenario.probability / 3600.0
                    
                    if random.random() < probability_per_second:
                        await self._execute_scenario(scenario_name)
                        
                await asyncio.sleep(1.0)  # Check every second
                
            except Exception as e:
                self.logger.error(f"Random event generation error: {e}")
                await asyncio.sleep(10.0)
                
    async def _execute_scenario(self, scenario_name: str):
        """Execute a specific event scenario"""
        if scenario_name not in self.scenarios:
            self.logger.error(f"Unknown scenario: {scenario_name}")
            return
            
        scenario = self.scenarios[scenario_name]
        
        # Check prerequisites
        if scenario.prerequisites and not self._check_prerequisites(scenario.prerequisites):
            self.logger.debug(f"Prerequisites not met for scenario: {scenario_name}")
            return
            
        # Find affected devices
        affected_devices = self._find_affected_devices(scenario.affected_devices)
        if not affected_devices:
            self.logger.warning(f"No devices found for scenario: {scenario_name}")
            return
            
        # Generate event duration
        duration = random.uniform(*scenario.duration_range)
        
        # Create event record
        event_id = f"{scenario_name}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S_%f')}"
        event_record = {
            "id": event_id,
            "scenario": scenario_name,
            "start_time": datetime.utcnow(),
            "duration": duration,
            "severity": scenario.severity.value,
            "affected_devices": [d.config.device_id for d in affected_devices],
            "cascading_events": []
        }
        
        self.active_events[event_id] = event_record
        self.event_history.append(event_record)
        
        # Trigger event on affected devices
        for device in affected_devices:
            try:
                await device.trigger_event(
                    scenario.event_type,
                    scenario.severity.value,
                    duration,
                    {"scenario": scenario_name, "event_id": event_id}
                )
            except Exception as e:
                self.logger.error(f"Failed to trigger event on {device.config.device_id}: {e}")
                
        # Schedule cascading events
        if scenario.cascading_events:
            for cascading_event in scenario.cascading_events:
                asyncio.create_task(self._schedule_cascading_event(event_id, cascading_event))
                
        # Update statistics
        self.stats["events_generated"] += 1
        self.stats["scenarios_executed"] += 1
        self.stats["devices_affected"] += len(affected_devices)
        self.stats["total_event_duration"] += duration
        
        self.logger.info(f"Executed scenario '{scenario_name}' affecting {len(affected_devices)} devices")
        
    async def _schedule_cascading_event(self, parent_event_id: str, cascading_config: Dict[str, Any]):
        """Schedule a cascading event"""
        try:
            # Wait for delay
            delay = cascading_config.get("delay", 0.0)
            await asyncio.sleep(delay)
            
            # Check if parent event is still active
            if parent_event_id not in self.active_events:
                return
                
            # Find affected devices for cascading event
            device_patterns = cascading_config.get("devices", [])
            affected_devices = self._find_affected_devices(device_patterns)
            
            if not affected_devices:
                return
                
            # Create cascading event
            event_type = EventType(cascading_config["type"])
            duration = cascading_config.get("duration", 5.0)
            severity = cascading_config.get("severity", 0.5)
            
            # Trigger cascading event
            for device in affected_devices:
                await device.trigger_event(
                    event_type,
                    severity,
                    duration,
                    {"parent_event": parent_event_id, "cascading": True}
                )
                
            # Record cascading event
            self.active_events[parent_event_id]["cascading_events"].append({
                "type": event_type.value,
                "start_time": datetime.utcnow(),
                "duration": duration,
                "devices": [d.config.device_id for d in affected_devices]
            })
            
            self.logger.info(f"Triggered cascading {event_type.value} event from {parent_event_id}")
            
        except Exception as e:
            self.logger.error(f"Cascading event error: {e}")
            
    def _find_affected_devices(self, device_patterns: List[str]) -> List[PowerSystemSimulator]:
        """Find devices matching the given patterns"""
        affected_devices = []
        
        for pattern in device_patterns:
            if pattern.endswith("*"):
                # Wildcard pattern matching
                prefix = pattern[:-1]
                for device_id, device in self.devices.items():
                    if device_id.startswith(prefix):
                        affected_devices.append(device)
            else:
                # Exact match
                if pattern in self.devices:
                    affected_devices.append(self.devices[pattern])
                    
        return affected_devices
        
    def _check_prerequisites(self, prerequisites: List[str]) -> bool:
        """Check if scenario prerequisites are met"""
        # Simplified prerequisite checking
        # In a real implementation, this would check system conditions
        for prereq in prerequisites:
            if prereq == "high_load_condition":
                return random.random() < 0.3  # 30% chance of high load
            elif prereq == "system_stressed":
                return random.random() < 0.1  # 10% chance of stressed system
                
        return True
        
    async def _monitor_cascading_events(self):
        """Monitor and manage cascading events"""
        while self.running:
            try:
                # This could implement more sophisticated cascading logic
                await asyncio.sleep(5.0)
            except Exception as e:
                self.logger.error(f"Cascading event monitoring error: {e}")
                await asyncio.sleep(10.0)
                
    async def _cleanup_expired_events(self):
        """Clean up expired event records"""
        while self.running:
            try:
                current_time = datetime.utcnow()
                expired_events = []
                
                for event_id, event_record in self.active_events.items():
                    event_end_time = event_record["start_time"] + timedelta(seconds=event_record["duration"])
                    if current_time > event_end_time:
                        expired_events.append(event_id)
                        
                # Remove expired events
                for event_id in expired_events:
                    del self.active_events[event_id]
                    
                await asyncio.sleep(60.0)  # Cleanup every minute
                
            except Exception as e:
                self.logger.error(f"Event cleanup error: {e}")
                await asyncio.sleep(60.0)
                
    async def trigger_manual_event(self, scenario_name: str, device_ids: Optional[List[str]] = None):
        """Manually trigger a specific scenario"""
        if scenario_name not in self.scenarios:
            raise ValueError(f"Unknown scenario: {scenario_name}")
            
        scenario = self.scenarios[scenario_name]
        
        # Use specified devices or find from pattern
        if device_ids:
            affected_devices = [self.devices[device_id] for device_id in device_ids if device_id in self.devices]
        else:
            affected_devices = self._find_affected_devices(scenario.affected_devices)
            
        if not affected_devices:
            raise ValueError("No valid devices found for scenario")
            
        # Execute the scenario
        await self._execute_scenario(scenario_name)
        
    def get_statistics(self) -> Dict[str, Any]:
        """Get event generation statistics"""
        return {
            **self.stats,
            "active_events": len(self.active_events),
            "registered_devices": len(self.devices),
            "available_scenarios": len(self.scenarios),
            "event_history_size": len(self.event_history)
        }
        
    def get_active_events(self) -> Dict[str, Dict[str, Any]]:
        """Get currently active events"""
        return self.active_events.copy()
        
    def get_event_history(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """Get event history"""
        if limit:
            return self.event_history[-limit:]
        return self.event_history.copy()
        
    def add_custom_scenario(self, name: str, scenario: EventScenario):
        """Add a custom event scenario"""
        self.scenarios[name] = scenario
        self.logger.info(f"Added custom scenario: {name}")
        
    def remove_scenario(self, name: str):
        """Remove an event scenario"""
        if name in self.scenarios:
            del self.scenarios[name]
            self.logger.info(f"Removed scenario: {name}")
        else:
            self.logger.warning(f"Scenario not found: {name}")
