"""
Unified Protocol Server
Combines wire-level protocol implementations with testing framework
- Production-ready protocol servers (DNP3, Modbus, IEC 61850)
- Integrated testing capabilities (error simulation, load testing)
- Unified management interface
- SCADA gateway integration
"""

import asyncio
import logging
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional, Set, Union, Tuple
from enum import Enum
from dataclasses import dataclass
import json

from .wire_protocol_simulators import (
    WireLevelDNP3Simulator,
    WireLevelModbusSimulator,
    WireLevelIEC61850Simulator
)
from .protocol_communication import (
    ProtocolMessageLogger,
    CommunicationErrorSimulator,
    ProtocolLatencySimulator,
    SecurityTestingFramework,
    LoadTestingFramework
)

class ProtocolType(Enum):
    """Supported protocol types"""
    DNP3 = "dnp3"
    MODBUS = "modbus"
    IEC61850 = "iec61850"

@dataclass
class ProtocolServerConfig:
    """Configuration for a protocol server"""
    protocol_type: ProtocolType
    port: int
    enabled: bool = True
    # Protocol-specific configuration
    dnp3_outstation_address: int = 1
    dnp3_master_address: int = 100
    modbus_slave_address: int = 1
    iec61850_ied_name: str = "TESTIED"
    # Testing configuration
    enable_error_simulation: bool = False
    enable_latency_simulation: bool = False
    enable_security_testing: bool = False
    error_rate: float = 0.01
    base_latency_ms: float = 10.0
    jitter_ms: float = 2.0

class UnifiedProtocolServer:
    """
    Unified protocol server that combines wire-level implementations
    with comprehensive testing capabilities
    """
    
    def __init__(self, device_id: str, server_configs: List[ProtocolServerConfig]):
        self.logger = logging.getLogger(f"UnifiedProtocolServer_{device_id}")
        self.device_id = device_id
        self.server_configs = {config.protocol_type: config for config in server_configs}
        
        # Wire-level protocol simulators
        self.protocol_servers: Dict[ProtocolType, Any] = {}
        
        # Testing framework components
        self.message_logger = ProtocolMessageLogger(f"logs/{device_id}")
        self.error_simulator = CommunicationErrorSimulator()
        self.latency_simulator = ProtocolLatencySimulator()
        self.security_framework = SecurityTestingFramework()
        self.load_tester = LoadTestingFramework()
        
        # Server state
        self.running = False
        self.measurement_data: Dict[str, Any] = {}
        self.status_data: Dict[str, bool] = {}
        
        # Initialize protocol servers
        self._initialize_protocol_servers()
        
    def _initialize_protocol_servers(self):
        """Initialize protocol server instances based on configuration"""
        for protocol_type, config in self.server_configs.items():
            if not config.enabled:
                continue
                
            if protocol_type == ProtocolType.DNP3:
                self.protocol_servers[protocol_type] = WireLevelDNP3Simulator(
                    self.device_id,
                    outstation_address=config.dnp3_outstation_address,
                    master_address=config.dnp3_master_address
                )
                
            elif protocol_type == ProtocolType.MODBUS:
                self.protocol_servers[protocol_type] = WireLevelModbusSimulator(
                    self.device_id,
                    slave_address=config.modbus_slave_address
                )
                
            elif protocol_type == ProtocolType.IEC61850:
                self.protocol_servers[protocol_type] = WireLevelIEC61850Simulator(
                    self.device_id,
                    ied_name=config.iec61850_ied_name
                )
                
            # Configure testing frameworks if enabled
            if config.enable_error_simulation:
                self.error_simulator.error_rate = config.error_rate
                
            if config.enable_latency_simulation:
                self.latency_simulator.set_network_conditions(
                    base_latency_ms=config.base_latency_ms,
                    jitter_ms=config.jitter_ms
                )
                
        self.logger.info(f"Initialized {len(self.protocol_servers)} protocol servers")
        
    async def start_all_servers(self):
        """Start all configured protocol servers"""
        if self.running:
            self.logger.warning("Servers already running")
            return
            
        self.running = True
        
        # Start each protocol server
        for protocol_type, server in self.protocol_servers.items():
            config = self.server_configs[protocol_type]
            
            try:
                if protocol_type == ProtocolType.DNP3:
                    await server.start_server(config.port)
                elif protocol_type == ProtocolType.MODBUS:
                    await server.start_server(config.port)
                elif protocol_type == ProtocolType.IEC61850:
                    await server.start_server(config.port)
                    
                self.logger.info(f"Started {protocol_type.value} server on port {config.port}")
                
            except Exception as e:
                self.logger.error(f"Failed to start {protocol_type.value} server: {e}")
                
        self.logger.info("All protocol servers started")
        
    async def stop_all_servers(self):
        """Stop all protocol servers"""
        if not self.running:
            return
            
        self.running = False
        
        # Stop each protocol server
        for protocol_type, server in self.protocol_servers.items():
            try:
                await server.stop_server()
                self.logger.info(f"Stopped {protocol_type.value} server")
            except Exception as e:
                self.logger.error(f"Error stopping {protocol_type.value} server: {e}")
                
        self.logger.info("All protocol servers stopped")
        
    def update_measurement(self, measurement_name: str, value: float, quality: int = 1):
        """Update measurement across all protocol servers"""
        self.measurement_data[measurement_name] = {
            'value': value,
            'quality': quality,
            'timestamp': datetime.now(timezone.utc)
        }
        
        # Update DNP3 server
        if ProtocolType.DNP3 in self.protocol_servers:
            dnp3_server = self.protocol_servers[ProtocolType.DNP3]
            point_mapping = self._get_dnp3_point_mapping()
            
            if measurement_name in point_mapping:
                dnp3_server.update_analog_input(
                    point_mapping[measurement_name], value, quality
                )
                
        # Update Modbus server
        if ProtocolType.MODBUS in self.protocol_servers:
            modbus_server = self.protocol_servers[ProtocolType.MODBUS]
            register_mapping = self._get_modbus_register_mapping()
            
            if measurement_name in register_mapping:
                # Scale value for 16-bit register with appropriate scaling
                if 'voltage' in measurement_name and value > 10000:
                    # High voltage (>10kV): scale by 0.1 to fit in 16-bit register
                    # 138,000V -> 13,800 (fits in 16-bit, represents 138kV/10)
                    scaled_value = int(value * 0.1) & 0xFFFF
                elif 'voltage' in measurement_name:
                    # Low voltage (<10kV): scale by 10 (store in 0.1V units)
                    scaled_value = int(value * 10) & 0xFFFF
                elif 'power' in measurement_name:
                    # Power: scale by 0.1 (store in 0.1kW units)
                    scaled_value = int(value * 0.1) & 0xFFFF
                elif 'frequency' in measurement_name:
                    # Frequency: scale by 100 (store in 0.01Hz units)
                    scaled_value = int(value * 100) & 0xFFFF
                else:
                    # Current and other values: scale by 10
                    scaled_value = int(value * 10) & 0xFFFF

                modbus_server.update_holding_register(
                    register_mapping[measurement_name], scaled_value
                )

                # Debug logging for current_c
                if measurement_name == 'current_c':
                    self.logger.info(f"Updated {measurement_name} = {value} -> register {register_mapping[measurement_name]} = {scaled_value}")
                
        # Update IEC 61850 server
        if ProtocolType.IEC61850 in self.protocol_servers:
            iec61850_server = self.protocol_servers[ProtocolType.IEC61850]
            attribute_mapping = self._get_iec61850_attribute_mapping()
            
            if measurement_name in attribute_mapping:
                ld, ln, do, da = attribute_mapping[measurement_name]
                iec61850_server.update_data_attribute(ld, ln, do, da, value)
                
    def update_status(self, status_name: str, value: bool, quality: int = 1):
        """Update status across all protocol servers"""
        self.status_data[status_name] = {
            'value': value,
            'quality': quality,
            'timestamp': datetime.now(timezone.utc)
        }
        
        # Update DNP3 server
        if ProtocolType.DNP3 in self.protocol_servers:
            dnp3_server = self.protocol_servers[ProtocolType.DNP3]
            binary_mapping = self._get_dnp3_binary_mapping()
            
            if status_name in binary_mapping:
                dnp3_server.update_binary_input(
                    binary_mapping[status_name], value, quality
                )
                
        # Update Modbus server
        if ProtocolType.MODBUS in self.protocol_servers:
            modbus_server = self.protocol_servers[ProtocolType.MODBUS]
            coil_mapping = self._get_modbus_coil_mapping()
            
            if status_name in coil_mapping:
                modbus_server.update_discrete_input(
                    coil_mapping[status_name], value
                )
                
        # Update IEC 61850 server
        if ProtocolType.IEC61850 in self.protocol_servers:
            iec61850_server = self.protocol_servers[ProtocolType.IEC61850]
            status_mapping = self._get_iec61850_status_mapping()
            
            if status_name in status_mapping:
                ld, ln, do, da = status_mapping[status_name]
                iec61850_server.update_data_attribute(ld, ln, do, da, value)
                
    def _get_dnp3_point_mapping(self) -> Dict[str, int]:
        """Get DNP3 analog input point mapping"""
        return {
            'voltage_a': 0, 'voltage_b': 1, 'voltage_c': 2,
            'current_a': 3, 'current_b': 4, 'current_c': 5,
            'power_total': 6, 'power_reactive': 7,
            'frequency': 8, 'power_factor': 9
        }
        
    def _get_dnp3_binary_mapping(self) -> Dict[str, int]:
        """Get DNP3 binary input point mapping"""
        return {
            'breaker_status': 0, 'protection_trip': 1,
            'alarm_active': 2, 'maintenance_mode': 3,
            'communication_ok': 4
        }
        
    def _get_modbus_register_mapping(self) -> Dict[str, int]:
        """Get Modbus holding register mapping"""
        return {
            'voltage_a': 0, 'voltage_b': 1, 'voltage_c': 2,
            'current_a': 10, 'current_b': 11, 'current_c': 12,
            'power_total': 20, 'power_reactive': 21,
            'frequency': 30, 'power_factor': 31
        }
        
    def _get_modbus_coil_mapping(self) -> Dict[str, int]:
        """Get Modbus coil/discrete input mapping"""
        return {
            'breaker_status': 0, 'protection_trip': 1,
            'alarm_active': 2, 'maintenance_mode': 3,
            'communication_ok': 4
        }
        
    def _get_iec61850_attribute_mapping(self) -> Dict[str, Tuple[str, str, str, Optional[str]]]:
        """Get IEC 61850 data attribute mapping"""
        return {
            'voltage_a': ("CTRL", "MMXU1", "PPV", "phsA"),
            'voltage_b': ("CTRL", "MMXU1", "PPV", "phsB"),
            'voltage_c': ("CTRL", "MMXU1", "PPV", "phsC"),
            'current_a': ("CTRL", "MMXU1", "A", "phsA"),
            'current_b': ("CTRL", "MMXU1", "A", "phsB"),
            'current_c': ("CTRL", "MMXU1", "A", "phsC"),
            'power_total': ("CTRL", "MMXU1", "TotW", None),
            'power_reactive': ("CTRL", "MMXU1", "TotVAr", None),
            'frequency': ("CTRL", "MMXU1", "Hz", None)
        }
        
    def _get_iec61850_status_mapping(self) -> Dict[str, Tuple[str, str, str, Optional[str]]]:
        """Get IEC 61850 status attribute mapping"""
        return {
            'breaker_status': ("CTRL", "XCBR1", "Pos", None),
            'protection_trip': ("CTRL", "PTRC1", "Op", None),
            'alarm_active': ("CTRL", "LLN0", "Health", None)
        }
        
    async def run_load_test(self, protocol_type: ProtocolType, 
                           concurrent_sessions: int = 10, 
                           duration_seconds: int = 60) -> Dict[str, Any]:
        """Run load test on specific protocol"""
        if protocol_type not in self.protocol_servers:
            raise ValueError(f"Protocol {protocol_type.value} not enabled")
            
        config = self.server_configs[protocol_type]
        
        return await self.load_tester.run_concurrent_session_test(
            protocol_type=protocol_type.value,
            host="localhost",
            port=config.port,
            concurrent_sessions=concurrent_sessions,
            duration_seconds=duration_seconds,
            requests_per_session=100
        )
        
    def get_server_statistics(self) -> Dict[str, Any]:
        """Get comprehensive server statistics"""
        stats = {
            'device_id': self.device_id,
            'running': self.running,
            'enabled_protocols': [pt.value for pt in self.protocol_servers.keys()],
            'measurement_count': len(self.measurement_data),
            'status_count': len(self.status_data),
            'protocols': {}
        }
        
        # Add protocol-specific statistics
        for protocol_type, server in self.protocol_servers.items():
            config = self.server_configs[protocol_type]
            
            protocol_stats = {
                'port': config.port,
                'enabled': config.enabled
            }
            
            if protocol_type == ProtocolType.DNP3:
                protocol_stats.update({
                    'connections': len(server.connections),
                    'analog_inputs': len(server.analog_inputs),
                    'binary_inputs': len(server.binary_inputs),
                    'unsolicited_enabled': server.unsolicited_enabled,
                    'outstation_address': server.outstation_address
                })
                
            elif protocol_type == ProtocolType.MODBUS:
                protocol_stats.update({
                    'connections': len(server.connections),
                    'holding_registers': len(server.holding_registers),
                    'input_registers': len(server.input_registers),
                    'coils': len(server.coils),
                    'slave_address': server.slave_address
                })
                
            elif protocol_type == ProtocolType.IEC61850:
                protocol_stats.update({
                    'mms_connections': len(server.mms_connections),
                    'logical_devices': len(server.logical_devices),
                    'goose_enabled': server.goose_enabled,
                    'ied_name': server.ied_name
                })
                
            stats['protocols'][protocol_type.value] = protocol_stats
            
        return stats
        
    def get_connection_info(self) -> Dict[str, Any]:
        """Get connection information for clients"""
        connection_info = {
            'device_id': self.device_id,
            'protocols': {}
        }
        
        for protocol_type, config in self.server_configs.items():
            if not config.enabled:
                continue
                
            protocol_info = {
                'host': 'localhost',
                'port': config.port,
                'status': 'running' if self.running else 'stopped'
            }
            
            if protocol_type == ProtocolType.DNP3:
                protocol_info.update({
                    'outstation_address': config.dnp3_outstation_address,
                    'master_address': config.dnp3_master_address,
                    'example_client': f"Connect DNP3 master to localhost:{config.port}"
                })
                
            elif protocol_type == ProtocolType.MODBUS:
                protocol_info.update({
                    'slave_address': config.modbus_slave_address,
                    'example_client': f"Connect Modbus TCP client to localhost:{config.port}"
                })
                
            elif protocol_type == ProtocolType.IEC61850:
                protocol_info.update({
                    'ied_name': config.iec61850_ied_name,
                    'example_client': f"Connect IEC 61850 client to localhost:{config.port}"
                })
                
            connection_info['protocols'][protocol_type.value] = protocol_info

        return connection_info

class ProtocolServerFactory:
    """Factory for creating protocol servers with common configurations"""

    @staticmethod
    def create_relay_server(device_id: str, base_port: int = 20000) -> UnifiedProtocolServer:
        """Create a protocol server configured for a protective relay"""
        configs = [
            ProtocolServerConfig(
                protocol_type=ProtocolType.DNP3,
                port=base_port,
                dnp3_outstation_address=1,
                enable_error_simulation=True,
                error_rate=0.01
            ),
            ProtocolServerConfig(
                protocol_type=ProtocolType.IEC61850,
                port=base_port + 100,
                iec61850_ied_name=f"RELAY_{device_id}",
                enable_latency_simulation=True,
                base_latency_ms=15.0
            )
        ]
        return UnifiedProtocolServer(device_id, configs)

    @staticmethod
    def create_meter_server(device_id: str, base_port: int = 20000) -> UnifiedProtocolServer:
        """Create a protocol server configured for a power meter"""
        configs = [
            ProtocolServerConfig(
                protocol_type=ProtocolType.MODBUS,
                port=base_port,
                modbus_slave_address=1,
                enable_error_simulation=True,
                error_rate=0.005
            ),
            ProtocolServerConfig(
                protocol_type=ProtocolType.DNP3,
                port=base_port + 1,
                dnp3_outstation_address=2,
                enable_latency_simulation=True,
                base_latency_ms=10.0
            )
        ]
        return UnifiedProtocolServer(device_id, configs)

    @staticmethod
    def create_pmu_server(device_id: str, base_port: int = 20000) -> UnifiedProtocolServer:
        """Create a protocol server configured for a PMU"""
        configs = [
            ProtocolServerConfig(
                protocol_type=ProtocolType.IEC61850,
                port=base_port,
                iec61850_ied_name=f"PMU_{device_id}",
                enable_latency_simulation=True,
                base_latency_ms=5.0,  # PMUs need low latency
                jitter_ms=1.0
            )
        ]
        return UnifiedProtocolServer(device_id, configs)

    @staticmethod
    def create_multi_protocol_server(device_id: str, base_port: int = 20000) -> UnifiedProtocolServer:
        """Create a server with all protocols enabled"""
        configs = [
            ProtocolServerConfig(
                protocol_type=ProtocolType.DNP3,
                port=base_port,
                dnp3_outstation_address=1,
                enable_error_simulation=True
            ),
            ProtocolServerConfig(
                protocol_type=ProtocolType.MODBUS,
                port=base_port + 1,
                modbus_slave_address=1,
                enable_latency_simulation=True
            ),
            ProtocolServerConfig(
                protocol_type=ProtocolType.IEC61850,
                port=base_port + 2,
                iec61850_ied_name=f"IED_{device_id}",
                enable_security_testing=True
            )
        ]
        return UnifiedProtocolServer(device_id, configs)

    @staticmethod
    def create_from_config(device_id: str, config_dict: Dict[str, Any]) -> UnifiedProtocolServer:
        """Create server from configuration dictionary"""
        configs = []

        for protocol_name, protocol_config in config_dict.get('protocols', {}).items():
            if protocol_name.lower() == 'dnp3':
                config = ProtocolServerConfig(
                    protocol_type=ProtocolType.DNP3,
                    port=protocol_config.get('port', 20000),
                    enabled=protocol_config.get('enabled', True),
                    dnp3_outstation_address=protocol_config.get('outstation_address', 1),
                    dnp3_master_address=protocol_config.get('master_address', 100),
                    enable_error_simulation=protocol_config.get('enable_error_simulation', False),
                    error_rate=protocol_config.get('error_rate', 0.01)
                )
                configs.append(config)

            elif protocol_name.lower() == 'modbus':
                config = ProtocolServerConfig(
                    protocol_type=ProtocolType.MODBUS,
                    port=protocol_config.get('port', 502),
                    enabled=protocol_config.get('enabled', True),
                    modbus_slave_address=protocol_config.get('slave_address', 1),
                    enable_latency_simulation=protocol_config.get('enable_latency_simulation', False),
                    base_latency_ms=protocol_config.get('base_latency_ms', 10.0)
                )
                configs.append(config)

            elif protocol_name.lower() == 'iec61850':
                config = ProtocolServerConfig(
                    protocol_type=ProtocolType.IEC61850,
                    port=protocol_config.get('port', 102),
                    enabled=protocol_config.get('enabled', True),
                    iec61850_ied_name=protocol_config.get('ied_name', f"IED_{device_id}"),
                    enable_security_testing=protocol_config.get('enable_security_testing', False)
                )
                configs.append(config)

        return UnifiedProtocolServer(device_id, configs)
