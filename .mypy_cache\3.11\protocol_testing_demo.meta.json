{"data_mtime": 1758128823, "dep_lines": [29, 34, 35, 15, 16, 17, 18, 19, 20, 21, 26, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 10, 10, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["simulated_devices.protocol_communication", "simulated_devices.file_transfer_simulation", "simulated_devices.triangle_microworks_integration", "asyncio", "logging", "json", "sys", "datetime", "pathlib", "typing", "simulated_devices", "builtins", "_asyncio", "_contextvars", "_frozen_importlib", "_io", "_typeshed", "abc", "enum", "io", "json.encoder", "os", "simulated_devices.device_factory", "simulated_devices.simulation_coordinator", "typing_extensions"], "hash": "a0d4ebe3a5aa3543d07931b7fb4d40736241bef1", "id": "protocol_testing_demo", "ignore_all": false, "interface_hash": "4c415a4030bfd3c5533b879710cb798791e88f40", "mtime": 1758128821, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\Quanta\\examples\\protocol_testing_demo.py", "plugin_data": null, "size": 19455, "suppressed": [], "version_id": "1.15.0"}